import React from 'react';
import { mapProps } from 'recompose';
import _isNil from 'lodash/isNil';
import _keyBy from 'lodash/keyBy';
import _toUpper from 'lodash/toUpper';
import _toNumber from 'lodash/toNumber';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _includes from 'lodash/includes';

import { NO_DATA, EMPTY_OBJECT } from 'tbase/app.constants';
import { CERTIFICATION_STATUS_CONFIG } from 'tbase/constants/vehicleInventory/certificationStatus';
import { addCommasToNum } from 'tbase/formatters/number';
import { capitalizeFirstLetters } from 'tbase/formatters/string';
import {
  getDisplayModalText,
  getDaysOnLot,
  getDaysTosell,
  getLengthInFeetAndInches,
  getUpperFirstMakeText,
} from 'tbase/helpers/vehicle.helper';
import isStringEmpty from 'tbase/utils/isStringEmpty';
import { tget } from 'tbase/utils/general';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { FUEL_TYPES, FUEL_TYPE_LABELS } from 'tbusiness/appServices/vehicleInventory/constants/fuelTypeOption';
import { BODY_TYPES, BODY_TYPE_LABELS } from 'tbusiness/appServices/vehicleInventory/constants/bodyTypeOption';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { getLabelForVehicleKind } from 'tbusiness/appServices/vehicleInventory/helpers/vehicleOption';
import Content from 'tcomponents/atoms/Content';
import Label from 'tcomponents/atoms/Label';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import CurrencyRenderer from 'tcomponents/molecules/CellRenderers/CurrencyRenderer';
import DateRenderer from 'tcomponents/molecules/CellRenderers/dateRenderer';
import makeCellRenderer from 'tcomponents/molecules/CellRenderers/makeCellRenderer';
import TextRenderer from 'tcomponents/molecules/CellRenderers/TextRenderer';
import PopoverWithCount from 'tcomponents/organisms/popoverWithCount';
import CampaignCodePopover from 'twidgets/organisms/sales/CampaignCodePopover';
import OptionsCodeCellRenderer from '@tekion/tekion-widgets/src/organisms/vi/OptionsCodeCellRenderer';
import { DATE_TIME_FORMAT } from '@tekion/tekion-conversion-web';

import { BOOLEAN_OPTIONS, SALES_DESTINATION_ENUM_LABEL_MAP, VIEW_TYPE } from 'constants/constants';

import { VEHICLE_ORDER_STATUS_CONFIG } from 'constants/constantEnum';
import withMaskedDataCell from 'hocs/withMaskedDataCell';
import { getSiteNameFromSiteId, getDealerNameFromId, isCRMEnabled, getWorkspaceNameFromId } from 'helpers/common';
import { showCountryDependentFields } from 'helpers/vehicle.helper';
import { getLastUpdatedUser, getUserNameById } from 'helpers/user';
import ActiveLeadsCell from 'molecules/cellRenderers/LeadsCell';
import FlagPickerRadioButtonCellRenderer from 'molecules/cellRenderers/FlagPickerRadioButtonCellRenderer';
import VehicleStatusRenderer from 'molecules/VehicleStatusRenderer';
import {
  hasAccountingCostView,
  hasHoldbackView,
  hasInventoryAllCostPricesViewPermission,
  hasPricingFieldsonListing,
  hasViewCostFields,
  hasViewPackField,
  hasWholesalePriceonListing,
} from 'permissions/inventory.permissions';
import { MARKETING_STATUS_CONFIG } from 'pages/VehicleDetailsV2/Components/Marketing/marketing.constants';

import { getPicturesCount } from './VehicleInventoryList.helpers';
import ActiveDealsCell from './Components/ActiveDealsCell';
import ExteriorColorRenderer from './Components/cellRenderers/ExteriorColorRenderer';
import NotesRenderer from './Components/cellRenderers/NotesRenderer';
import VehicleTypeCellRenderer from './Components/cellRenderers/VehicleTypeCellRenderer';
import RepairOrderCell from './Components/RepairOrderCell';
import COLUMN_IDS from './VehicleInventoryList.columnIds';
import COLUMN_IDS_RV from './VehicleInventoryList.columnIds_RV';
import VinDetails from './Components/VinDetails/VinDetails';
import { RENDERER_TYPE } from './Components/VehicleInventoryList.constants';
import StockDetails from './Components/StockDetails/StockDetails';
import YearMakeModelPopover from './Components/YearMakeModelPopover';
import TrackingTagPopover from './Components/TrackingTagPopover';
import PurchaseInvoiceCell from './Components/cellRenderers/PurchaseInvoiceCell/PurchaseInvoiceCell';

const RepairOrderCellRenderer = makeCellRenderer(RepairOrderCell);

const MaskedCurrencyRenderer = withMaskedDataCell(CurrencyRenderer);

const getActiveDealCellRendererProps = ({
  original,
  actions,
  activeDealsCount,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
}) => {
  const id = VehicleReader.id(original);

  return {
    id,
    actions,
    activeDealCount: activeDealsCount[_toUpper(id)] || 0,
    vehicleData: original,
    enterpriseV2Enabled,
    enterpriseV2Workspaces,
  };
};

const getActiveLeadsCellRendererProps = ({ actions, original, activeLeadsCount, leadData }) => {
  const id = VehicleReader.id(original);
  return {
    id,
    actions,
    leadsCount: activeLeadsCount[_toUpper(id)] || 0,
    leadData,
  };
};

const getPicturesCountCell = ({ original }) => {
  const count = getPicturesCount(original);
  return <Content>{`${count}`}</Content>;
};

const getDateCell = ({ value }) =>
  value ? <DateRenderer data={value} asLabel={false} format={DATE_TIME_FORMAT.DATE_ABBREVIATED_MONTH_YEAR} /> : NO_DATA;

const getFuelTypeCell = ({ value }) =>
  isInchcapeOrRRG() && _includes(FUEL_TYPES, value) ? FUEL_TYPE_LABELS[value] : value;

const getBodyTypeCell = ({ value }) => (_includes(BODY_TYPES, value) ? BODY_TYPE_LABELS[value] : value);

export const getAgeData = ({ value }) => (value ? <Content>{value}</Content> : '0');

const yesNoCell = ({ value }) => {
  if (value === null) return NO_DATA;
  return value ? __('Yes') : __('No');
};

const getInteriorColorCellRendererProps = ({ value }) => value && { value: capitalizeFirstLetters(value) };

const getMileageCellRendererProps = ({ value, original }) => {
  if (isStringEmpty(value)) {
    return EMPTY_OBJECT;
  }
  const mileage = VehicleReader.mileage(original);

  return {
    value: addCommasToNum(mileage),
  };
};

const getWeightCellRendererProps = ({ value }) => {
  if (isStringEmpty(value)) {
    return {};
  }
  return {
    value: addCommasToNum(value),
  };
};

const getSiteIdRendererProps = ({ value, dealerSiteOptions }) => {
  if (_isNil(value)) {
    return {};
  }

  return {
    value: getSiteNameFromSiteId(value, dealerSiteOptions),
  };
};

const getNotesCellRendererProps = ({ notesCount, original, showNotesModal }) => {
  const id = VehicleReader.id(original);
  return {
    assetId: id,
    onNotesIconClick: showNotesModal(id),
    notesCount: notesCount[id] || 0,
  };
};

const getVINCellRendererProps = ({
  actions,
  createSingleJobRO,
  original,
  openGallery,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
}) => ({
  actions,
  vehicle: original,
  createROCB: createSingleJobRO(original),
  openGalleryCB: openGallery(original),
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
});

const getStatusCellRendererProps = ({ original }) => ({
  vehicleDetails: original,
  viewType: VIEW_TYPE.LIST_VIEW,
});

const getStockCellRendererProps = ({ actions, original, enterpriseV2Enabled, enterpriseV2Workspaces }) => ({
  actions,
  vehicle: original,
  transferDetails: original?.inventoryTransferDetails,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
});

const getActiveLeadsCell = ({ activeLeadsCount, leadData, actions }) => {
  if (!isCRMEnabled()) {
    return NO_DATA;
  }

  return mapProps(({ original }) =>
    getActiveLeadsCellRendererProps({
      original,
      activeLeadsCount,
      leadData,
      actions,
    })
  )(ActiveLeadsCell);
};

const getExteriorColorCellRendererProps = ({ original }) => ({ vehicle: original });

const getSubStatusRendererProps = ({ value, original, customStatuses, actions }) => {
  const vehicleId = VehicleReader.id(original);

  return {
    vehicleSubStatuses: customStatuses,
    selectedOptionKey: value,
    vehicleId,
    actions,
  };
};

const CampaignCodeRenderer = ({ original }) => {
  const stopDeliveryIndicator = VehicleReader.stopDeliveryIndicator(original);

  return stopDeliveryIndicator ? (
    <CampaignCodePopover campaignCodes={VehicleReader.campaignCodes(original)} showCount />
  ) : (
    NO_DATA
  );
};

const getOptionsCodeRendererProps = ({ original, isVehicleConfiguratorEnabled }) => ({ options: VehicleReader.options(original), isVehicleConfiguratorEnabled });

const getBooleanFieldRenderer = ({ value }) => {
  if (!value) return NO_DATA;
  return tget(_find(BOOLEAN_OPTIONS, { value }), 'label', value);
};

export const COLUMN_VS_RENDERER_TYPE = {
  [RENDERER_TYPE.TEXT]: TextRenderer,
  [RENDERER_TYPE.PRICE]: MaskedCurrencyRenderer,
  [RENDERER_TYPE.DATE]: ({ value }) => getDateCell({ value: _toNumber(value) }),
  [RENDERER_TYPE.BOOLEAN]: getBooleanFieldRenderer,
};

const getCustomFieldColumnValue = (key, fieldType) => {
  if (!_includes([RENDERER_TYPE.DATE, RENDERER_TYPE.BOOLEAN], fieldType)) {
    return `customFields.${key}`;
  }
  return `customFields.${key}.0.value`;
};

const renderCustomFieldValue = ({ value }) => <Label>{value}</Label>;

const COLUMN_TYPE = (key, fieldType) => ({
  VI_CUSTOM_FIELD: getCustomFieldColumnValue(key, fieldType),
});

const RENDERER_TYPE_VS_COUMN_TYPE = {
  VI_CUSTOM_FIELD: ({ value }) => <PopoverWithCount data={value} renderItem={renderCustomFieldValue} />,
};

const getCertificationStatusRendererProps = ({ value }) => ({
  value: tget(_get(CERTIFICATION_STATUS_CONFIG, value), 'label', value),
});

const getMarketingStatusRendererProps = ({ value }) => ({
  value: tget(_get(MARKETING_STATUS_CONFIG, value), 'label', value),
});

const getDealerNameCellRenderer = ({ dealers, enterpriseV2Enabled, enterpriseV2Workspaces }) => ({ original }) => {
    const dealerId = VehicleReader.dealerId(original);
    return enterpriseV2Enabled
      ? getWorkspaceNameFromId(dealerId, enterpriseV2Workspaces)
      : getDealerNameFromId(dealerId, dealers);
  };

const getCustomPriceCellRendererProps = props => {
  const { column, original } = props || EMPTY_OBJECT;
  const columnKey = _get(column, 'key');
  const customPricingFields = _get(original, 'customPricingFields');
  const customPricingFieldData = _find(customPricingFields, { fieldName: columnKey });
  return {
    ...props,
    value: _get(customPricingFieldData, 'value'),
  };
};

export const getCommonColumnConfigByKey = ({ actions, enterpriseV2Enabled, workspaces }) => ({
  [COLUMN_IDS.STOCK_ID]: {
    id: COLUMN_IDS.STOCK_ID,
    minWidth: 122,
    accessor: COLUMN_IDS.STOCK_ID,
    Cell: mapProps(({ original }) =>
      getStockCellRendererProps({ actions, original, enterpriseV2Enabled, enterpriseV2Workspaces: workspaces })
    )(StockDetails),
  },
  [COLUMN_IDS.VEHICLE_TYPE]: {
    id: COLUMN_IDS.VEHICLE_TYPE,
    minWidth: 150,
    Cell: mapProps(({ original }) => ({ vehicle: original }))(VehicleTypeCellRenderer),
  },
  [COLUMN_IDS.STATUS]: {
    id: COLUMN_IDS.STATUS,
    accessor: COLUMN_IDS.STATUS,
    minWidth: 122,
    Cell: mapProps(getStatusCellRendererProps)(VehicleStatusRenderer),
  },
  [COLUMN_IDS.AGE]: {
    accessor: COLUMN_IDS.AGE,
    id: COLUMN_IDS.AGE,
    minWidth: 107,
    Cell: getAgeData,
  },
});

export const getColumnConfig =
  (
    actions,
    activeDealsCount,
    activeLeadsCount,
    notesCount,
    createSingleJobRO,
    leadData,
    dealerSiteOptions,
    showNotesModal,
    openGallery,
    customStatuses,
    dealers,
    displayModelSource,
    sourceTypeOptions,
    getUserInfoById,
    vehicleAlerts,
    redirectToVehicleDetails,
    enterpriseV2Enabled,
    enterpriseV2Workspaces,
    isVehicleConfiguratorEnabled,
    workspaces
  ) =>
  (accessor, columnMeta) => {
    const commonColumns = getCommonColumnConfigByKey({
      actions,
      enterpriseV2Enabled,
      enterpriseV2Workspaces,
      workspaces,
    });
    const columnConfig = {
      ...commonColumns,
      [COLUMN_IDS.ACTIVE_DEALS]: {
        id: COLUMN_IDS.ACTIVE_DEALS,
        minWidth: 132,
        Cell: mapProps(({ original }) =>
          getActiveDealCellRendererProps({
            actions,
            original,
            activeDealsCount,
            enterpriseV2Enabled,
            enterpriseV2Workspaces,
          })
        )(ActiveDealsCell),
      },
      [COLUMN_IDS.ACTIVE_LEADS]: {
        id: COLUMN_IDS.ACTIVE_LEADS,
        minWidth: 132,
        Cell: getActiveLeadsCell({ activeLeadsCount, leadData, actions }),
      },
      [COLUMN_IDS.BASE_INVOICE_PRICE]: {
        id: COLUMN_IDS.BASE_INVOICE_PRICE,
        accessor: 'pricingDetails.baseInvoice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.BASE_RETAIL_PRICE]: {
        id: COLUMN_IDS.BASE_RETAIL_PRICE,
        accessor: 'pricingDetails.baseRetail',
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.BODY_CLASS]: {
        id: COLUMN_IDS.BODY_CLASS,
        accessor: 'trimDetails.bodyClass',
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.VEHICLE_ASSIGNED]: {
        id: COLUMN_IDS.VEHICLE_ASSIGNED,
        accessor: 'isAssignedVehicle',
        minWidth: 132,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.VAT]: {
        id: COLUMN_IDS.VAT,
        accessor: 'isVAT',
        minWidth: 132,
      },
      [COLUMN_IDS.BILLS]: {
        id: COLUMN_IDS.BILLS,
        accessor: 'isBills',
        minWidth: 132,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.REGISTRATION_CARD_PAYABLE]: {
        id: COLUMN_IDS.REGISTRATION_CARD_PAYABLE,
        accessor: 'isRegistrationCardPayable',
        minWidth: 132,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.SALES_DESTINATION]: {
        id: COLUMN_IDS.SALES_DESTINATION,
        accessor: 'salesDestination',
        minWidth: 132,
        Cell: mapProps(({ value }) => ({
          value: tget(SALES_DESTINATION_ENUM_LABEL_MAP, value, value),
        }))(TextRenderer),
      },
      [COLUMN_IDS.LAST_VALUATION_DATE]: {
        id: COLUMN_IDS.LAST_VALUATION_DATE,
        accessor: 'lastValuationDate',
        minWidth: 132,
        Cell: getDateCell,
      },
      [COLUMN_IDS.LAST_REGISTRATION_DATE]: {
        id: COLUMN_IDS.LAST_REGISTRATION_DATE,
        accessor: 'lastRegistrationDate',
        minWidth: 132,
        Cell: getDateCell,
      },
      [COLUMN_IDS.DEMOSTRATION_DECLARATION_DATE]: {
        id: COLUMN_IDS.DEMOSTRATION_DECLARATION_DATE,
        accessor: 'demonstrationDeclarationDate',
        minWidth: 132,
        Cell: getDateCell,
      },
      [COLUMN_IDS.LAST_OWNER]: {
        id: COLUMN_IDS.LAST_OWNER,
        accessor: 'lastOwner',
        minWidth: 132,
      },
      [COLUMN_IDS.NEDC_CO2]: {
        id: COLUMN_IDS.NEDC_CO2,
        accessor: COLUMN_IDS.NEDC_CO2,
        minWidth: 132,
        Cell: mapProps(({ value }) => ({ value: value ? addCommasToNum(value) : NO_DATA }))(TextRenderer),
      },
      [COLUMN_IDS.VAT_UV_ONLY]: {
        id: COLUMN_IDS.VAT_UV_ONLY,
        accessor: 'vatUVOnly',
        minWidth: 132,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.FIRST_HAND_OWNER]: {
        id: COLUMN_IDS.FIRST_HAND_OWNER,
        accessor: 'isFirstHandOwner',
        minWidth: 150,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.ASSIGNED_VEHICLE_DETAILS]: {
        id: COLUMN_IDS.ASSIGNED_VEHICLE_DETAILS,
        accessor: 'assignedVehicleDetails',
        minWidth: 200,
        Cell: mapProps(({ value }) => ({
          value: getUserNameById(value, getUserInfoById),
        }))(TextRenderer),
      },
      [COLUMN_IDS.BODY_DOOR_COUNT]: {
        id: COLUMN_IDS.BODY_DOOR_COUNT,
        accessor: 'trimDetails.bodyDoorCount',
        minWidth: 132,
      },
      [COLUMN_IDS.BODY_STYLE_CODE]: {
        id: COLUMN_IDS.BODY_STYLE_CODE,
        accessor: 'trimDetails.bodyStyleCode',
        minWidth: 132,
      },
      [COLUMN_IDS.FUEL_TYPE]: {
        id: COLUMN_IDS.FUEL_TYPE,
        accessor: 'trimDetails.fuelType',
        minWidth: 132,
        Cell: getFuelTypeCell,
      },
      [COLUMN_IDS.LICENSE_PLATE_NUMBER]: {
        id: COLUMN_IDS.LICENSE_PLATE_NUMBER,
        minWidth: 132,
        accessor: 'licensePlateNumber',
      },
      [COLUMN_IDS.PICTURES_COUNT]: {
        id: COLUMN_IDS.PICTURES_COUNT,
        minWidth: 132,
        accessor: 'picturesCount',
        Cell: getPicturesCountCell,
      },
      [COLUMN_IDS.TRANSMISSION_CONTROL_TYPE]: {
        id: COLUMN_IDS.TRANSMISSION_CONTROL_TYPE,
        minWidth: 132,
        accessor: 'trimDetails.transmissionControlType',
      },
      [COLUMN_IDS.WARRANTY_DESCRIPTION]: {
        id: COLUMN_IDS.WARRANTY_DESCRIPTION,
        accessor: 'warrantyDescription',
        minWidth: 132,
      },
      [COLUMN_IDS.FISCAL_HORSE_POWER]: {
        id: COLUMN_IDS.FISCAL_HORSE_POWER,
        accessor: 'fiscalHorsePower',
        minWidth: 132,
      },
      [COLUMN_IDS.OBSERVATIONS]: {
        id: COLUMN_IDS.OBSERVATIONS,
        accessor: 'observations',
        minWidth: 132,
      },
      [COLUMN_IDS.VEHICLE_KIND]: {
        id: COLUMN_IDS.VEHICLE_KIND,
        accessor: 'vehicleKind',
        minWidth: 132,
        Cell: mapProps(({ value }) => getLabelForVehicleKind(value))(TextRenderer),
      },
      [COLUMN_IDS.WLTP_CO2]: {
        id: COLUMN_IDS.WLTP_CO2,
        accessor: COLUMN_IDS.WLTP_CO2,
        minWidth: 132,
        Cell: mapProps(({ value }) => ({ value: value ? addCommasToNum(value) : NO_DATA }))(TextRenderer),
      },
      [COLUMN_IDS.LAST_MODIFIED_USER]: {
        id: COLUMN_IDS.LAST_MODIFIED_USER,
        accessor: 'lastUpdatedByUser',
        minWidth: 132,
        Cell: mapProps(({ value }) => getLastUpdatedUser(value, getUserInfoById))(TextRenderer),
      },
      [COLUMN_IDS.TOTAL_OWNERS]: {
        id: COLUMN_IDS.TOTAL_OWNERS,
        accessor: 'totalOwners',
        minWidth: 132,
      },
      [COLUMN_IDS.BONUS_ELGIBLE]: {
        id: COLUMN_IDS.BONUS_ELGIBLE,
        accessor: 'isBonusEligible',
        minWidth: 132,
        Cell: yesNoCell,
      },
      [COLUMN_IDS.FIRST_REGISTRATION_DATE]: {
        id: COLUMN_IDS.FIRST_REGISTRATION_DATE,
        accessor: 'firstRegistrationDate',
        minWidth: 132,
        Cell: getDateCell,
      },
      [COLUMN_IDS.LAST_TECHNICAL_CONTROL_DATE]: {
        id: COLUMN_IDS.LAST_TECHNICAL_CONTROL_DATE,
        accessor: 'lastTechnicalControlDate',
        minWidth: 132,
        Cell: getDateCell,
      },
      [COLUMN_IDS.MSRP_DATE]: {
        id: COLUMN_IDS.MSRP_DATE,
        accessor: 'msrpDate',
        Cell: getDateCell,
        minWidth: 132,
      },
      [COLUMN_IDS.OEM_BONUS]: {
        id: COLUMN_IDS.OEM_BONUS,
        accessor: 'oemBonus',
        Cell: yesNoCell,
        minWidth: 132,
      },
      [COLUMN_IDS.ESTIMATED_RECONDITIONING_COST]: {
        id: COLUMN_IDS.ESTIMATED_RECONDITIONING_COST,
        accessor: 'pricingDetails.estimatedReconditioningCost',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.OEM_BONUS_DETAILS]: {
        id: COLUMN_IDS.OEM_BONUS_DETAILS,
        accessor: 'pricingDetails.oemBounusDetials',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.FINAL_PRICE]: {
        id: COLUMN_IDS.FINAL_PRICE,
        accessor: 'pricingDetails.finalPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.OEM_BONUS_DETAILS]: {
        id: COLUMN_IDS.OEM_BONUS_DETAILS,
        accessor: 'pricingDetails.oemBounusDetials',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.PROMOTIONAL_PRICE]: {
        id: COLUMN_IDS.PROMOTIONAL_PRICE,
        accessor: 'pricingDetails.promotionalPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.STORAGE_COST]: {
        id: COLUMN_IDS.STORAGE_COST,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.storageCost',
      },
      [COLUMN_IDS.FIXED_COSTS]: {
        id: COLUMN_IDS.FIXED_COSTS,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.fixedCosts',
      },
      [COLUMN_IDS.ONLINE_PUBLISHED_PRICE]: {
        id: COLUMN_IDS.ONLINE_PUBLISHED_PRICE,
        accessor: 'pricingDetails.onlinePublishedPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.INCENTIVE]: {
        id: COLUMN_IDS.INCENTIVE,
        accessor: 'pricingDetails.incentive',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.DISCOUNT_PERCENTAGE]: {
        id: COLUMN_IDS.DISCOUNT_PERCENTAGE,
        accessor: 'pricingDetails.discountPercentage',
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.BUYING_PRICE]: {
        id: COLUMN_IDS.BUYING_PRICE,
        accessor: 'pricingDetails.buyingPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.DISCOUNT_PRICE]: {
        id: COLUMN_IDS.DISCOUNT_PRICE,
        accessor: 'pricingDetails.takePrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.WARRANTY_EXTENSION]: {
        id: COLUMN_IDS.WARRANTY_EXTENSION,
        accessor: 'pricingDetails.warrantyExtension',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.UV_STOCK_NUMBER]: {
        id: COLUMN_IDS.UV_STOCK_NUMBER,
        minWidth: 132,
        accessor: 'uvStockNumber',
      },
      [COLUMN_IDS.GL_ACCOUNT]: {
        id: COLUMN_IDS.GL_ACCOUNT,
        minWidth: 132,
        accessor: 'pricingDetails.glAccount',
      },
      [COLUMN_IDS.COMMISSION_PRICE]: {
        id: COLUMN_IDS.COMMISSION_PRICE,
        accessor: 'pricingDetails.commissionPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.CREATED_DATE]: {
        id: COLUMN_IDS.CREATED_DATE,
        minWidth: 106,
        accessor: 'createdTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.IN_SERVICE_DATE]: {
        id: COLUMN_IDS.IN_SERVICE_DATE,
        minWidth: 106,
        accessor: COLUMN_IDS.IN_SERVICE_DATE,
        Cell: getDateCell,
      },
      [COLUMN_IDS.WEEK_OF_PRODUCTION]: {
        id: COLUMN_IDS.WEEK_OF_PRODUCTION,
        minWidth: 106,
        accessor: 'productionTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.DRIVE_TYPE]: {
        id: COLUMN_IDS.DRIVE_TYPE,
        accessor: 'trimDetails.driveType',
        minWidth: 122,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.EMPLOYEE_PRICE]: {
        id: COLUMN_IDS.EMPLOYEE_PRICE,
        accessor: 'pricingDetails.employeePrice',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
      },
      [COLUMN_IDS.EXTERIOR_BASE_COLOR]: {
        id: COLUMN_IDS.EXTERIOR_BASE_COLOR,
        accessor: 'exteriorColorDetail.baseColor',
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.EXTERIOR_COLOR]: {
        id: COLUMN_IDS.EXTERIOR_COLOR,
        minWidth: 122,
        Cell: mapProps(getExteriorColorCellRendererProps)(ExteriorColorRenderer),
      },
      [COLUMN_IDS.EXTERIOR_HEX_CODE]: {
        id: COLUMN_IDS.EXTERIOR_HEX_CODE,
        accessor: 'exteriorColorDetail.hexCode',
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.EXTERIOR_COLOR_CODE]: {
        id: COLUMN_IDS.EXTERIOR_COLOR_CODE,
        accessor: COLUMN_IDS.EXTERIOR_COLOR_CODE,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.FLOORING_AMOUNT]: {
        id: COLUMN_IDS.FLOORING_AMOUNT,
        accessor: 'pricingDetails.flooringAmt',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.FREIGHT_IN_PRICE]: {
        id: COLUMN_IDS.FREIGHT_IN_PRICE,
        accessor: 'pricingDetails.freightInPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.MARKET_PRICE]: {
        id: COLUMN_IDS.MARKET_PRICE,
        accessor: 'pricingDetails.marketPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.GL_BALANCE]: {
        id: COLUMN_IDS.GL_BALANCE,
        accessor: 'pricingDetails.glBalance',
        Cell: MaskedCurrencyRenderer,
        className: 'tk-table-cell-right-align',
        minWidth: 132,
      },
      [COLUMN_IDS.HOLDBACK_AMOUNT]: {
        id: COLUMN_IDS.HOLDBACK_AMOUNT,
        accessor: 'pricingDetails.holdBackAmount',
        Cell: MaskedCurrencyRenderer,
        className: 'tk-table-cell-right-align',
        minWidth: 216,
      },
      [COLUMN_IDS.INTERIOR_COLOR]: {
        id: COLUMN_IDS.INTERIOR_COLOR,
        accessor: COLUMN_IDS.INTERIOR_COLOR,
        minWidth: 122,
        Cell: mapProps(getInteriorColorCellRendererProps)(TextRenderer),
      },
      [COLUMN_IDS.INTERIOR_COLOR_CODE]: {
        id: COLUMN_IDS.INTERIOR_COLOR_CODE,
        accessor: COLUMN_IDS.INTERIOR_COLOR_CODE,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.INTERNET_PRICE]: {
        id: COLUMN_IDS.INTERNET_PRICE,
        accessor: 'pricingDetails.internetPrice',
        minWidth: 132,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.INVOICE_PRICE]: {
        id: COLUMN_IDS.INVOICE_PRICE,
        accessor: 'pricingDetails.invoicePrice',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
        className: 'tk-table-cell-right-align',
      },
      [COLUMN_IDS.LOCATION_CODE]: {
        id: COLUMN_IDS.LOCATION_CODE,
        accessor: COLUMN_IDS.LOCATION_CODE,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.NEW_TYRE_COUNT]: {
        id: COLUMN_IDS.NEW_TYRE_COUNT,
        accessor: COLUMN_IDS.NEW_TYRE_COUNT,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.PREVIOUS_USE]: {
        id: COLUMN_IDS.PREVIOUS_USE,
        accessor: 'certificationGroup.previousUse',
      },
      [COLUMN_IDS.GROSS_VEHICLE_WEIGHT_RATING]: {
        id: COLUMN_IDS.GROSS_VEHICLE_WEIGHT_RATING,
        accessor: COLUMN_IDS.GROSS_VEHICLE_WEIGHT_RATING,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.MAKE]: {
        id: COLUMN_IDS.MAKE,
        accessor: COLUMN_IDS.MAKE,
        minWidth: 106,
        Cell: ({ original }) => getUpperFirstMakeText(original),
      },
      [COLUMN_IDS.MILEAGE]: {
        id: COLUMN_IDS.MILEAGE,
        accessor: COLUMN_IDS.MILEAGE,
        minWidth: 132,
        Cell: mapProps(getMileageCellRendererProps)(TextRenderer),
      },
      [COLUMN_IDS.MODEL]: {
        id: COLUMN_IDS.MODEL,
        accessor: COLUMN_IDS.MODEL,
        minWidth: 122,
        Cell: ({ original }) => getDisplayModalText(original, displayModelSource),
      },
      [COLUMN_IDS.MODEL_CODE]: {
        id: COLUMN_IDS.MODEL_CODE,
        accessor: COLUMN_IDS.MODEL_CODE,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.MFR_MODEL_CODE]: {
        id: COLUMN_IDS.MFR_MODEL_CODE,
        accessor: COLUMN_IDS.MFR_MODEL_CODE,
        minWidth: 132,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.MODEL_TYPE]: {
        id: COLUMN_IDS.MODEL_TYPE,
        accessor: COLUMN_IDS.MODEL_TYPE,
        minWidth: 122,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.MODIFIED_DATE]: {
        id: COLUMN_IDS.MODIFIED_DATE,
        minWidth: 106,
        accessor: 'modifiedTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.MSRP]: {
        id: COLUMN_IDS.MSRP,
        accessor: 'pricingDetails.msrp',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
        className: 'tk-table-cell-right-align',
      },
      [COLUMN_IDS.NOTES]: {
        id: COLUMN_IDS.NOTES,
        Cell: mapProps(({ original }) => getNotesCellRendererProps({ notesCount, original, showNotesModal }))(
          NotesRenderer
        ),
      },
      [COLUMN_IDS.PORT_HOLD_BACK]: {
        id: COLUMN_IDS.PORT_HOLD_BACK,
        accessor: 'pricingDetails.portHoldback',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
      },
      [COLUMN_IDS.RECEIVED_DATE]: {
        id: COLUMN_IDS.RECEIVED_DATE,
        minWidth: 106,
        accessor: 'entryTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.REPAIR_ORDER]: {
        id: COLUMN_IDS.REPAIR_ORDER,
        accessor: 'roDetails',
        minWidth: 132,
        Cell: RepairOrderCellRenderer,
      },
      [COLUMN_IDS.RETAIL_PRICE]: {
        id: COLUMN_IDS.RETAIL_PRICE,
        accessor: 'pricingDetails.retailPrice',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
        className: 'tk-table-cell-right-align',
      },
      [COLUMN_IDS.SOLD_AT_SITE_ID]: {
        id: COLUMN_IDS.SOLD_AT_SITE_ID,
        accessor: COLUMN_IDS.SOLD_AT_SITE_ID,
        Cell: mapProps(({ value }) => getSiteIdRendererProps({ value, dealerSiteOptions }))(TextRenderer),
      },
      [COLUMN_IDS.STOCKED_IN_AT_SITE_ID]: {
        id: COLUMN_IDS.STOCKED_IN_AT_SITE_ID,
        accessor: COLUMN_IDS.STOCKED_IN_AT_SITE_ID,
        Cell: mapProps(({ value }) => getSiteIdRendererProps({ value, dealerSiteOptions }))(TextRenderer),
      },
      [COLUMN_IDS.STOCKED_IN_DATE]: {
        id: COLUMN_IDS.STOCKED_IN_DATE,
        minWidth: 106,
        accessor: 'stockedInTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.SUPPLIER_PRICE]: {
        id: COLUMN_IDS.SUPPLIER_PRICE,
        accessor: 'pricingDetails.supplierPrice',
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.TINT_NITRO]: {
        id: COLUMN_IDS.TINT_NITRO,
        accessor: 'pricingDetails.tintNitro',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
      },
      [COLUMN_IDS.VANGUARD]: {
        id: COLUMN_IDS.VANGUARD,
        minWidth: 120,
        accessor: 'pricingDetails.vanguard',
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.VEHICLE_SOLD_TIME]: {
        id: COLUMN_IDS.VEHICLE_SOLD_TIME,
        minWidth: 106,
        accessor: 'vehicleSoldTime',
        Cell: getDateCell,
      },
      [COLUMN_IDS.VEHICLE_SUB_TYPE]: {
        id: COLUMN_IDS.VEHICLE_SUB_TYPE,
        accessor: COLUMN_IDS.VEHICLE_SUB_TYPE,
        minWidth: 150,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.ENVIRONMENT_LABEL]: {
        id: COLUMN_IDS.ENVIRONMENT_LABEL,
        accessor: 'environmentalLabel',
        minWidth: 150,
      },
      [COLUMN_IDS.VIN]: {
        id: COLUMN_IDS.VIN,
        accessor: COLUMN_IDS.VIN,
        Cell: mapProps(({ original }) =>
          getVINCellRendererProps({
            actions,
            createSingleJobRO,
            original,
            openGallery,
            enterpriseV2Enabled,
            enterpriseV2Workspaces,
          })
        )(VinDetails),
        minWidth: 250,
      },
      [COLUMN_IDS.WHOLESALE_PRICE]: {
        id: COLUMN_IDS.WHOLESALE_PRICE,
        accessor: 'pricingDetails.wholesalePrice',
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.WHOLESALE_FINANCE_RESERVE]: {
        id: COLUMN_IDS.WHOLESALE_FINANCE_RESERVE,
        accessor: 'pricingDetails.wholeSaleFinanceReserve',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
      },
      [COLUMN_IDS.YEAR]: {
        id: COLUMN_IDS.YEAR,
        accessor: COLUMN_IDS.YEAR,
        minWidth: 72,
        className: 'tk-table-cell-right-align',
        Cell: TextRenderer,
      },
      [COLUMN_IDS.YEAR_MAKE_MODEL]: {
        id: COLUMN_IDS.YEAR_MAKE_MODEL,
        accessor: COLUMN_IDS.YEAR_MAKE_MODEL,
        minWidth: 216,
        Cell: mapProps(({ original }) => ({
          vehicle: original,
          displayModelSource,
          yearMakeModelOfVehicle: true,
          vehicleAlerts,
          vehicleId: VehicleReader.id(original),
          redirectToVehicleDetails,
        }))(YearMakeModelPopover),
      },
      [COLUMN_IDS.VEHICLE_SUB_STATUS]: {
        id: COLUMN_IDS.VEHICLE_SUB_STATUS,
        Cell: mapProps(({ value, original }) =>
          getSubStatusRendererProps({
            value,
            original,
            customStatuses,
            actions,
          })
        )(FlagPickerRadioButtonCellRenderer),
      },
      [COLUMN_IDS.TRIM_CODE]: {
        id: COLUMN_IDS.TRIM_CODE,
        Cell: TextRenderer,
        accessor: 'trimDetails.trim',
      },
      [COLUMN_IDS.TAGS]: {
        id: COLUMN_IDS.TAGS,
        accessor: COLUMN_IDS.TAGS,
        Cell: mapProps(({ original }) => ({
          vehicle: original,
        }))(TrackingTagPopover),
      },
      [COLUMN_IDS.MARKETABLE]: {
        id: COLUMN_IDS.MARKETABLE,
        Cell: yesNoCell,
        accessor: COLUMN_IDS.MARKETABLE,
      },
      [COLUMN_IDS.DEALER_NAME]: {
        id: COLUMN_IDS.DEALER_NAME,
        Cell: getDealerNameCellRenderer({ dealers, enterpriseV2Enabled, enterpriseV2Workspaces }),
        accessor: COLUMN_IDS.DEALER_NAME,
      },
      [COLUMN_IDS.ORDER_STATUS]: {
        id: COLUMN_IDS.ORDER_STATUS,
        Cell: mapProps(({ value }) => ({ value: VEHICLE_ORDER_STATUS_CONFIG[value]?.label }))(TextRenderer),
        accessor: 'orderStatus.status',
      },
      [COLUMN_IDS_RV.LOCATION_NAME]: {
        id: COLUMN_IDS_RV.LOCATION_NAME,
        Cell: TextRenderer,
        accessor: 'locationDetails.locationName',
      },
      [COLUMN_IDS_RV.LOCATION_STATUS]: {
        id: COLUMN_IDS_RV.LOCATION_STATUS,
        Cell: TextRenderer,
        accessor: 'locationDetails.locationStatus',
      },
      [COLUMN_IDS_RV.GL_LOCATION]: {
        id: COLUMN_IDS_RV.GL_LOCATION,
        Cell: TextRenderer,
        accessor: 'locationDetails.glLocation',
      },
      [COLUMN_IDS_RV.SERIAL_COACH_NUMBER]: {
        id: COLUMN_IDS_RV.SERIAL_COACH_NUMBER,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.serialCoachNumber',
      },
      [COLUMN_IDS_RV.ORIGINAL_TYPE_CODE]: {
        id: COLUMN_IDS_RV.ORIGINAL_TYPE_CODE,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.originalTypeCode',
      },
      [COLUMN_IDS_RV.TYPE_CODE]: {
        id: COLUMN_IDS_RV.TYPE_CODE,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.typeCode',
      },
      [COLUMN_IDS_RV.PDI_LIST_PRICE]: {
        id: COLUMN_IDS_RV.PDI_LIST_PRICE,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.pdiListPrice',
      },
      [COLUMN_IDS_RV.DAYS_ON_LOT]: {
        id: COLUMN_IDS_RV.DAYS_ON_LOT,
        Cell: ({ original }) => getDaysOnLot(original),
        accessor: 'vehicleAdditionalDetails.daysOnLot',
      },
      [COLUMN_IDS_RV.AGE_GROUP]: {
        id: COLUMN_IDS_RV.AGE_GROUP,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.ageGroup',
      },
      [COLUMN_IDS_RV.PACK]: {
        id: COLUMN_IDS_RV.PACK,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.pack',
      },
      [COLUMN_IDS_RV.PRODUCTION_DATE]: {
        id: COLUMN_IDS_RV.PRODUCTION_DATE,
        Cell: getDateCell,
        accessor: 'manufactureDate',
      },
      [COLUMN_IDS_RV.SALE_TYPE]: {
        id: COLUMN_IDS_RV.SALE_TYPE,
        Cell: TextRenderer,
        accessor: 'saleType',
      },
      [COLUMN_IDS_RV.POST_DATE]: {
        id: COLUMN_IDS_RV.POST_DATE,
        Cell: getDateCell,
        accessor: 'postTime',
      },
      [COLUMN_IDS_RV.PURCHASE_ORDER_DATE]: {
        id: COLUMN_IDS_RV.PURCHASE_ORDER_DATE,
        Cell: getDateCell,
        accessor: 'purchaseOrderDate',
      },
      [COLUMN_IDS_RV.TRUE_INVENTORY_COST]: {
        id: COLUMN_IDS_RV.TRUE_INVENTORY_COST,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.trueInventoryCost',
      },
      [COLUMN_IDS_RV.FLOORING_COMMENCED]: {
        id: COLUMN_IDS_RV.FLOORING_COMMENCED,
        Cell: getDateCell,
        accessor: 'vehicleAdditionalDetails.flooringCommencedDate',
      },
      [COLUMN_IDS_RV.DISCOUNT]: {
        id: COLUMN_IDS_RV.DISCOUNT,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.discount',
      },
      [COLUMN_IDS_RV.OTHER_COSTS]: {
        id: COLUMN_IDS_RV.OTHER_COSTS,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.otherCosts',
      },
      [COLUMN_IDS_RV.TAKE_PRICE]: {
        id: COLUMN_IDS_RV.TAKE_PRICE,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.takePrice',
      },
      [COLUMN_IDS_RV.DAYS_TO_SELL]: {
        id: COLUMN_IDS_RV.DAYS_TO_SELL,
        Cell: ({ original }) => getDaysTosell(original),
        accessor: 'vehicleAdditionalDetails.daysToSell',
      },
      [COLUMN_IDS_RV.RV_TYPE]: {
        id: COLUMN_IDS_RV.RV_TYPE,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.rvType',
      },
      [COLUMN_IDS_RV.BRAND_GROUP]: {
        id: COLUMN_IDS_RV.BRAND_GROUP,
        Cell: TextRenderer,
        accessor: 'trimDetails.brandGroup',
      },
      [COLUMN_IDS_RV.BRAND]: {
        id: COLUMN_IDS_RV.BRAND,
        Cell: TextRenderer,
        accessor: 'trimDetails.brand',
      },
      [COLUMN_IDS_RV.CURRENT_MODEL]: {
        id: COLUMN_IDS_RV.CURRENT_MODEL,
        Cell: yesNoCell,
        accessor: 'currentModel',
      },
      [COLUMN_IDS_RV.EXCLUSIVE_PRODUCT]: {
        id: COLUMN_IDS_RV.EXCLUSIVE_PRODUCT,
        Cell: yesNoCell,
        accessor: 'exclusiveProduct',
      },
      [COLUMN_IDS_RV.PLANT_LOCATION]: {
        id: COLUMN_IDS_RV.PLANT_LOCATION,
        Cell: TextRenderer,
        accessor: 'locationDetails.plantLocation',
      },
      [COLUMN_IDS_RV.FLOOR_PLAN]: {
        id: COLUMN_IDS_RV.FLOOR_PLAN,
        Cell: TextRenderer,
        accessor: 'trimDetails.floorPlan',
      },
      [COLUMN_IDS_RV.FLOOR_PLAN_GROUP]: {
        id: COLUMN_IDS_RV.FLOOR_PLAN_GROUP,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.floorPlanGroup',
      },
      [COLUMN_IDS_RV.SLIDEOUTS]: {
        id: COLUMN_IDS_RV.SLIDEOUTS,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.slideOut',
      },
      [COLUMN_IDS_RV.LENGTH]: {
        id: COLUMN_IDS_RV.LENGTH,
        Cell: ({ original }) => getLengthInFeetAndInches(original),
        accessor: 'vehicleAdditionalDetails.length',
      },
      [COLUMN_IDS_RV.GARAGE_LENGTH]: {
        id: COLUMN_IDS_RV.GARAGE_LENGTH,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.garageLength',
      },
      [COLUMN_IDS_RV.HEIGHT]: {
        id: COLUMN_IDS_RV.HEIGHT,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.height',
      },
      [COLUMN_IDS_RV.WIDTH]: {
        id: COLUMN_IDS_RV.WIDTH,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.width',
      },
      [COLUMN_IDS_RV.HITCH_WEIGHT]: {
        id: COLUMN_IDS_RV.HITCH_WEIGHT,
        Cell: mapProps(getWeightCellRendererProps)(TextRenderer),
        accessor: 'vehicleAdditionalDetails.hitchWeight',
      },
      [COLUMN_IDS_RV.CHASSIS]: {
        id: COLUMN_IDS_RV.CHASSIS,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.chassis',
      },
      [COLUMN_IDS_RV.HORSEPOWER]: {
        id: COLUMN_IDS_RV.HORSEPOWER,
        Cell: TextRenderer,
        accessor: 'trimDetails.horsePower',
      },
      [COLUMN_IDS_RV.TORQUE]: {
        id: COLUMN_IDS_RV.TORQUE,
        Cell: TextRenderer,
        accessor: 'trimDetails.torque',
      },
      [COLUMN_IDS_RV.GCWR]: {
        id: COLUMN_IDS_RV.GCWR,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.gcwr',
      },
      [COLUMN_IDS_RV.FACTORY_LIST_PRICE]: {
        id: COLUMN_IDS_RV.FACTORY_LIST_PRICE,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.factoryListPrice',
      },
      [COLUMN_IDS_RV.TOTAL_COST]: {
        id: COLUMN_IDS_RV.TOTAL_COST,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.totalCost',
      },
      [COLUMN_IDS_RV.FLOORING_ORIGINAL_AMOUNT]: {
        id: COLUMN_IDS_RV.FLOORING_ORIGINAL_AMOUNT,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.flooringOriginalAmount',
      },
      [COLUMN_IDS_RV.OUTSTANDING_FLOOR_PLAN]: {
        id: COLUMN_IDS_RV.OUTSTANDING_FLOOR_PLAN,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.outstandingFloorPlan',
      },
      [COLUMN_IDS_RV.FREIGHT_COST]: {
        id: COLUMN_IDS_RV.FREIGHT_COST,
        Cell: MaskedCurrencyRenderer,
        accessor: 'pricingDetails.freightCost',
      },
      [COLUMN_IDS_RV.OEM]: {
        id: COLUMN_IDS_RV.OEM,
        Cell: TextRenderer,
        accessor: 'trimDetails.oem',
      },
      [COLUMN_IDS_RV.ESTIMATED_DELIVERY_DATE]: {
        id: COLUMN_IDS_RV.ESTIMATED_DELIVERY_DATE,
        Cell: getDateCell,
        accessor: COLUMN_IDS_RV.ESTIMATED_DELIVERY_DATE,
      },
      [COLUMN_IDS_RV.SOURCE]: {
        id: COLUMN_IDS_RV.SOURCE,
        accessor: 'sourceInfo.source',
        Cell: ({ value }) => {
          const selectedSourceType =
            _find(sourceTypeOptions, ({ value: sourceTypeValue }) => sourceTypeValue === value) || EMPTY_OBJECT;
          const displayLabel = _get(selectedSourceType, 'label') || value;
          return displayLabel;
        },
      },
      [COLUMN_IDS_RV.ORDER_NUMBER]: {
        id: COLUMN_IDS_RV.ORDER_NUMBER,
        Cell: TextRenderer,
        accessor: COLUMN_IDS_RV.ORDER_NUMBER,
      },
      [COLUMN_IDS_RV.BODY_TYPE]: {
        id: COLUMN_IDS_RV.BODY_TYPE,
        Cell: isInchcapeOrRRG() ? TextRenderer : getBodyTypeCell,
        accessor: 'trimDetails.bodyType',
      },
      [COLUMN_IDS_RV.AXLES]: {
        id: COLUMN_IDS_RV.AXLES,
        Cell: TextRenderer,
        accessor: 'trimDetails.axleCount',
      },
      [COLUMN_IDS_RV.UNLADEN_WEIGHT]: {
        id: COLUMN_IDS_RV.UNLADEN_WEIGHT,
        Cell: mapProps(getWeightCellRendererProps)(TextRenderer),
        accessor: COLUMN_IDS_RV.UNLADEN_WEIGHT,
      },
      [COLUMN_IDS_RV.FLOOR_PLAN_BIFURCAION]: {
        id: COLUMN_IDS_RV.FLOOR_PLAN_BIFURCAION,
        Cell: TextRenderer,
        accessor: COLUMN_IDS_RV.FLOOR_PLAN_BIFURCAION,
      },
      [COLUMN_IDS_RV.SLEEP]: {
        id: COLUMN_IDS_RV.FLOOR_PLAN_BIFURCAION,
        Cell: TextRenderer,
        accessor: 'vehicleAdditionalDetails.sleeps',
      },
      [COLUMN_IDS_RV.SALES_COST]: {
        id: COLUMN_IDS_RV.SALES_COST,
        accessor: 'pricingDetails.salesCost',
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.ENTERPRISE_AGE]: {
        accessor: COLUMN_IDS.ENTERPRISE_AGE,
        id: COLUMN_IDS.ENTERPRISE_AGE,
        Cell: getAgeData,
      },
      [COLUMN_IDS.CAMPAIGN_CODES]: {
        id: COLUMN_IDS.CAMPAIGN_CODES,
        accessor: COLUMN_IDS.CAMPAIGN_CODES,
        Cell: CampaignCodeRenderer,
        minWidth: 250,
      },
      [COLUMN_IDS_RV.RECOMMENDED]: {
        id: COLUMN_IDS_RV.RECOMMENDED,
        Cell: yesNoCell,
        accessor: COLUMN_IDS_RV.RECOMMENDED,
      },
      [COLUMN_IDS.IN_TEST_DRIVE_POOL]: {
        id: COLUMN_IDS.IN_TEST_DRIVE_POOL,
        Cell: yesNoCell,
        accessor: COLUMN_IDS.IN_TEST_DRIVE_POOL,
      },
      [COLUMN_IDS.OPTIONS]: {
        id: COLUMN_IDS.OPTIONS,
        accessor: COLUMN_IDS.OPTIONS,
        Cell: mapProps(({ original }) => getOptionsCodeRendererProps({original, isVehicleConfiguratorEnabled}))(OptionsCodeCellRenderer),
      },
      [COLUMN_IDS.AG_STATUS]: {
        id: COLUMN_IDS.AG_STATUS,
        accessor: COLUMN_IDS.AG_STATUS,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.AG_STATUS_CODE]: {
        id: COLUMN_IDS.AG_STATUS_CODE,
        accessor: COLUMN_IDS.AG_STATUS_CODE,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.ORDER_NUMBER]: {
        id: COLUMN_IDS.ORDER_NUMBER,
        accessor: COLUMN_IDS.ORDER_NUMBER,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.TITLE_NUMBER]: {
        id: COLUMN_IDS.TITLE_NUMBER,
        accessor: 'titleInfo.titleNumber',
        Cell: TextRenderer,
      },
      [COLUMN_IDS.ACTUAL_MADA_DATE]: {
        id: COLUMN_IDS.ACTUAL_MADA_DATE,
        accessor: COLUMN_IDS.ACTUAL_MADA_DATE,
        Cell: getDateCell,
      },
      [COLUMN_IDS.PROVIDER_ORDER_STATUS]: {
        id: COLUMN_IDS.PROVIDER_ORDER_STATUS,
        accessor: 'orderStatus.statusDisplayName',
        minWidth: 170,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.VAT_QUALIFYING]: {
        id: COLUMN_IDS.VAT_QUALIFYING,
        accessor: 'isVATQualifying',
        Cell: yesNoCell,
      },
      [COLUMN_IDS.CUSTOM_ORDER_NUMBER]: {
        id: COLUMN_IDS.CUSTOM_ORDER_NUMBER,
        accessor: 'dspOrderNo',
        minWidth: 170,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.CERTIFICATION_TIER]: {
        id: COLUMN_IDS.CERTIFICATION_TIER,
        accessor: 'certificationGroup.tierName',
        minWidth: 170,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.SOURCE_NAME]: {
        id: COLUMN_IDS.SOURCE_NAME,
        accessor: 'sourceInfo.sourceName',
        minWidth: 170,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.VEHICLE_VATABLE]: {
        id: COLUMN_IDS.VEHICLE_VATABLE,
        accessor: 'pricingDetails.vehicleVatableAmount',
        minWidth: 170,
        Cell: MaskedCurrencyRenderer,
      },
      [COLUMN_IDS.CERTIFICATION_STATUS]: {
        id: COLUMN_IDS.CERTIFICATION_STATUS,
        accessor: COLUMN_IDS.CERTIFICATION_STATUS,
        Cell: mapProps(getCertificationStatusRendererProps)(TextRenderer),
      },
      [COLUMN_IDS.FACTORY_NAME]: {
        id: COLUMN_IDS.FACTORY_NAME,
        accessor: COLUMN_IDS.FACTORY_NAME,
        minWidth: 122,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.FACTORY_CODE]: {
        id: COLUMN_IDS.FACTORY_CODE,
        accessor: COLUMN_IDS.FACTORY_CODE,
        minWidth: 122,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.OPTION_CONFIGURATION_STATE]: {
        id: COLUMN_IDS.OPTION_CONFIGURATION_STATE,
        accessor: COLUMN_IDS.OPTION_CONFIGURATION_STATE,
        minWidth: 122,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.PRODUCTION_PLANNING_DATE]: {
        id: COLUMN_IDS.PRODUCTION_PLANNING_DATE,
        minWidth: 150,
        accessor: COLUMN_IDS.PRODUCTION_PLANNING_DATE,
        Cell: getDateCell,
      },
      [COLUMN_IDS.MARKETING_CONTENT_STATUS]: {
        id: COLUMN_IDS.MARKETING_CONTENT_STATUS,
        minWidth: 200,
        accessor: COLUMN_IDS.MARKETING_CONTENT_STATUS,
        Cell: mapProps(getMarketingStatusRendererProps)(TextRenderer),
      },
      [COLUMN_IDS.PURCHASE_INVOICES]: {
        id: COLUMN_IDS.PURCHASE_INVOICES,
        minWidth: 132,
        accessor: COLUMN_IDS.PURCHASE_INVOICES,
        Cell: mapProps(props => ({ ...props, isErrorInvoiceCell: false, enterpriseV2Enabled, enterpriseV2Workspaces }))(PurchaseInvoiceCell),
      },
      [COLUMN_IDS.ERROR_PURCHASE_INVOICES]: {
        id: COLUMN_IDS.ERROR_PURCHASE_INVOICES,
        minWidth: 132,
        accessor: COLUMN_IDS.ERROR_PURCHASE_INVOICES,
        Cell: mapProps(props => ({ ...props, isErrorInvoiceCell: true, enterpriseV2Enabled, enterpriseV2Workspaces }))(PurchaseInvoiceCell),
      },
      [COLUMN_IDS.PENDING_RO_APPROVAL]: {
        id: COLUMN_IDS.PENDING_RO_APPROVAL,
        accessor: COLUMN_IDS.PENDING_RO_APPROVAL,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.PENDING_DAMAGE_REPAIR_APPROVAL]: {
        id: COLUMN_IDS.PENDING_DAMAGE_REPAIR_APPROVAL,
        accessor: COLUMN_IDS.PENDING_DAMAGE_REPAIR_APPROVAL,
        Cell: TextRenderer,
      },
      [COLUMN_IDS.INCLUSIVE_PRICE]: {
        id: COLUMN_IDS.INCLUSIVE_PRICE,
        accessor: 'pricingDetails.inclusivePrice',
        Cell: MaskedCurrencyRenderer,
        minWidth: 132,
      },
    };

    if (columnConfig?.[accessor]) {
      return columnConfig?.[accessor];
    }

    const columnMetaData = _keyBy(columnMeta, 'key');
    const columnType = columnMetaData?.[accessor]?.columnType;
    const fieldType = columnMetaData?.[accessor]?.fieldType;
    const renderer = COLUMN_VS_RENDERER_TYPE[fieldType];
    const id = columnMetaData?.[accessor]?.key;
    const fieldAccessor = columnMetaData?.[accessor]?.fieldName;
    let CustomCellRenderer =
      RENDERER_TYPE_VS_COUMN_TYPE?.[columnType] && !_includes([RENDERER_TYPE.DATE, RENDERER_TYPE.BOOLEAN], fieldType)
        ? RENDERER_TYPE_VS_COUMN_TYPE?.[columnType]
        : renderer;

    if (columnType === 'VI_CUSTOM_FIELD' && fieldType === RENDERER_TYPE.PRICE) {
      CustomCellRenderer = mapProps(getCustomPriceCellRendererProps)(MaskedCurrencyRenderer);
    }

    return {
      id,
      accessor: COLUMN_TYPE(id, fieldType)?.[columnType] ? COLUMN_TYPE(id, fieldType)?.[columnType] : fieldAccessor,
      Cell: CustomCellRenderer,
    };
  };

export const COLUMN_CONFIG_BY_KEY = ({ dealerData }) => ({
  [COLUMN_IDS.GL_BALANCE]: {
    isVisibleInConfigurator: hasAccountingCostView(),
    show: hasAccountingCostView(),
  },
  [COLUMN_IDS.HOLDBACK_AMOUNT]: {
    isVisibleInConfigurator: hasHoldbackView(),
    show: hasHoldbackView(),
  },
  [COLUMN_IDS_RV.SALES_COST]: {
    isVisibleInConfigurator: hasViewCostFields(),
    show: hasViewCostFields(),
  },
  [COLUMN_IDS_RV.TRUE_INVENTORY_COST]: {
    isVisibleInConfigurator: hasViewCostFields(),
    show: hasViewCostFields(),
  },
  [COLUMN_IDS_RV.TOTAL_COST]: {
    isVisibleInConfigurator: hasViewCostFields(),
    show: hasViewCostFields(),
  },
  [COLUMN_IDS_RV.PACK]: {
    isVisibleInConfigurator: hasViewPackField(),
    show: hasViewPackField(),
  },
  [COLUMN_IDS.CAMPAIGN_CODES]: {
    isVisibleInConfigurator: showCountryDependentFields({ dealerData, fieldKey: COLUMN_IDS.CAMPAIGN_CODES }),
    show: showCountryDependentFields({ dealerData, fieldKey: COLUMN_IDS.CAMPAIGN_CODES }),
  },
  [COLUMN_IDS.INVOICE_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing() && hasInventoryAllCostPricesViewPermission(),
    show: hasPricingFieldsonListing() && hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS.RETAIL_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.COMMISSION_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.BASE_RETAIL_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.BASE_INVOICE_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing() && hasInventoryAllCostPricesViewPermission(),
    show: hasPricingFieldsonListing() && hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS.FREIGHT_IN_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.MSRP]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.WHOLESALE_PRICE]: {
    isVisibleInConfigurator: hasWholesalePriceonListing(),
    show: hasWholesalePriceonListing(),
  },
  [COLUMN_IDS.EMPLOYEE_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.SUPPLIER_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.INTERNET_PRICE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.FLOORING_AMOUNT]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: hasPricingFieldsonListing(),
  },
  [COLUMN_IDS.TAGS]: {
    isVisibleInConfigurator: DealerPropertyHelper.isVehicleTrackingEnabled(),
    show: DealerPropertyHelper.isVehicleTrackingEnabled(),
  },
  [COLUMN_IDS.WHOLESALE_FINANCE_RESERVE]: {
    isVisibleInConfigurator: hasPricingFieldsonListing() && hasInventoryAllCostPricesViewPermission(),
    show: DealerPropertyHelper.isAsburyProgramEnabled() && hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS.PORT_HOLD_BACK]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: DealerPropertyHelper.isAsburyProgramEnabled(),
  },
  [COLUMN_IDS.VANGUARD]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: DealerPropertyHelper.isAsburyProgramEnabled(),
  },
  [COLUMN_IDS.TINT_NITRO]: {
    isVisibleInConfigurator: hasPricingFieldsonListing(),
    show: DealerPropertyHelper.isAsburyProgramEnabled(),
  },
  [COLUMN_IDS.BUYING_PRICE]: {
    isVisibleInConfigurator: hasInventoryAllCostPricesViewPermission(),
    show: hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS_RV.FLOORING_ORIGINAL_AMOUNT]: {
    isVisibleInConfigurator: hasInventoryAllCostPricesViewPermission(),
    show: hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS_RV.OUTSTANDING_FLOOR_PLAN]: {
    isVisibleInConfigurator: hasInventoryAllCostPricesViewPermission(),
    show: hasInventoryAllCostPricesViewPermission(),
  },
  [COLUMN_IDS.FLOORING_AMOUNT]: {
    isVisibleInConfigurator: hasInventoryAllCostPricesViewPermission(),
    show: hasInventoryAllCostPricesViewPermission(),
  },
});
