/* eslint-disable no-param-reassign */
import React, { PureComponent } from 'react';
import Proptypes from 'prop-types';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEqual from 'lodash/isEqual';
import _reject from 'lodash/reject';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import {
  getTransferBalancePrice,
  calculateInclusivePriceWithOptions,
  getDiscountFromMsrp,
} from 'tbusiness/appServices/vehicleInventory/helpers/vehiclePricing';
import Heading from 'tcomponents/atoms/Heading';
import Content from 'tcomponents/atoms/Content';
import InfoBadge from 'tcomponents/molecules/infoBadge';
import Info from 'tcomponents/molecules/Info';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import dealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { PRICING_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';

import DescriptionSwitch from 'molecules/DescriptionSwitch';
import Discount from 'molecules/formFields/Discount';
import PROGRAM_CONFIG from 'constants/programConfig';
import { PRICING_FORM_FIELDS } from 'constants/formFields';
import { spreadDisabledToFields, appendOptionsToFields } from 'constants/utils';
import withMetaData from 'hocs/withMetaData';
import { formFieldReader } from 'pages/Settings/settings.reader';
import { hasDetailedPricingEdit, hasVehicleEditDates } from 'permissions/inventory.permissions';
import renderFormData from 'utils/formBuilder';
import { getYesNoOptions } from 'helpers/common';
import { parseInputWithDecimal } from 'utils/fieldFormatter';

import styles from './pricingForm.module.scss';

const chromeDataFields = ['BASE_INVOICE', 'BASE_RETAIL', 'RETAIL_PRICE', 'INVOICE_PRICE', 'MSRP', 'FREIGHT_IN'];

class PricingForm extends PureComponent {
  constructor(props) {
    super(props);
    const { vehicleDetails } = props;
    this.state = {
      fields: EMPTY_ARRAY,
      enablePricingUpdate: _get(vehicleDetails, 'priceUpdateRestrict'),
    };
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(prevProps) {
    const {
      fields,
      shouldDisableInvoiceDate,
      associatedGlAccounts,
      defaultGlAccount,
      disablePricingEdits,
      customerViewProps,
    } = this.props;
    if (
      !_isEqual(prevProps.fields, fields) ||
      !_isEqual(prevProps.shouldDisableInvoiceDate, shouldDisableInvoiceDate) ||
      !_isEqual(prevProps.associatedGlAccounts, associatedGlAccounts) ||
      !_isEqual(prevProps.defaultGlAccount, defaultGlAccount) ||
      !_isEqual(prevProps.disablePricingEdits, disablePricingEdits) ||
      !_isEqual(prevProps.customerViewProps, customerViewProps)
    ) {
      this.init();
    }
  }

  prepareGlAccountOption = data => {
    const { getGLAccounts } = this.props;
    return getGLAccounts(data, 'id');
  };

  getTargetGlAccountNumber = () => {
    const { defaultGlAccount } = this.props;
    return _get(defaultGlAccount, 'id');
  };

  init = () => {
    const {
      fields,
      handelGlAccountChange,
      associatedGlAccounts,
      disablePricingEdits,
      form,
      dealerMasterData,
      customerViewProps,
    } = this.props;
    const formState = form.getState();
    const glAccountHandlers = { onChange: handelGlAccountChange };
    const pricingFields = fields.map(field => {
      let priceField = PRICING_FORM_FIELDS.find(formField => formField.key === field.key);
      if (field.key === 'glAccount') {
        priceField.input = glAccountHandlers;
        priceField = appendOptionsToFields(
          priceField,
          this.prepareGlAccountOption(associatedGlAccounts),
          this.getTargetGlAccountNumber()
        );
      }
      if (field.key === 'OEM_BONUS') {
        priceField.options = getYesNoOptions(field?.options);
        priceField.fieldType = field.fieldType;
      }
      if (dealerPropertyHelper.isViArcLiteEnabled()) {
        priceField.disabled = true;
        priceField.isDisabled = true;
      }
      return formFieldReader(priceField, field);
    });

    let newPricingField = pricingFields.map(field => {
      // do not remove might be used in future
      // if (field.key === 'INVOICE_DATE' && shouldDisableInvoiceDate) {
      //   return spreadDisabledToFields(field);
      // }

      if (field.key === 'glBalance') {
        return spreadDisabledToFields(field);
      }

      if (field.key === 'glAccount') {
        return appendOptionsToFields(
          { ...field, input: glAccountHandlers },
          this.prepareGlAccountOption(associatedGlAccounts),
          this.getTargetGlAccountNumber()
        );
      }

      if (_includes([PRICING_FIELD_KEYS.MSRP, PRICING_FIELD_KEYS.INVOICE_PRICE], field?.key)) {
        const formValues = _get(formState, 'values');
        const status = VehicleReader.status(formValues);
        if (_includes([VEHICLE_STATUS.STOCKED_IN], status)) {
          field = { ...field, disabled: disablePricingEdits || dealerPropertyHelper.isViArcLiteEnabled() };
        }
        const { adjustedMsrp, adjustedInvoicePrice } = _get(formValues, 'pricingDetails', EMPTY_OBJECT);
        if (
          (field?.key === PRICING_FIELD_KEYS.MSRP && Math.abs(adjustedMsrp) > 0) ||
          (field?.key === PRICING_FIELD_KEYS.INVOICE_PRICE && Math.abs(adjustedInvoicePrice) > 0)
        )
          field = { ...field, suffix: <Info helpText={__('Adjusted Price')} tooltipClass="align-center p-12" /> };
      }

      if (field.key === PRICING_FIELD_KEYS.INVOICE_DATE) {
        field = { ...field, disabled: !hasVehicleEditDates() || dealerPropertyHelper.isViArcLiteEnabled() };
      }

      if (field.key === PRICING_FIELD_KEYS.DISCOUNT_ON_MSRP) {
        field.component = Discount;
        field.getMsrpValue = this.getMsrpValue;
        field.onChangeCb = this.onDiscountOnMSRPChangeHandler;
      }

      if (_includes(chromeDataFields, field?.key)) {
        field.onChangeCb = value => {
          form.change(field?.name, value);
          form.change('pricesManuallyUpdated', true);
        };
      }

      if (field?.key === PRICING_FIELD_KEYS.MSRP) {
        field.onChangeCb = this.onMsrpChangeHandler;
      }

      return field;
    });

    if (!hasDetailedPricingEdit()) {
      newPricingField = newPricingField.map(field => spreadDisabledToFields(field));
    }

    newPricingField = _reject(
      newPricingField,
      field =>
        field?.shouldShow &&
        !field?.shouldShow({
          dealerData: dealerMasterData,
          fieldKey: field?.key,
          ...customerViewProps,
        })
    );

    this.setState({
      fields: newPricingField,
    });
  };

  getMsrpValue = () => {
    const { form } = this.props;
    const { values } = form.getState();
    return _get(values, 'pricingDetails.msrp');
  };

  onMsrpChangeHandler = value => {
    const { form, removeDefaultOptions, optionsInStore } = this.props;
    const transferBalanceValue = getTransferBalancePrice(value);
    form.change('pricingDetails.transferBalance', transferBalanceValue);

    const { values } = form.getState();

    const discountOnMSRP = getDiscountFromMsrp(_get(values, 'pricingDetails.discountOnMSRP'), value);

    const inclusivePrice = calculateInclusivePriceWithOptions({
      msrpValue: value,
      discountOnMSRP,
      removeDefaultOptions,
      options: optionsInStore,
    });

    form.change('pricingDetails.discountOnMSRP', discountOnMSRP);
    form.change('pricingDetails.inclusivePrice', inclusivePrice);
  };

  onDiscountOnMSRPChangeHandler = value => {
    const { form, optionsInStore, removeDefaultOptions } = this.props;
    const { values } = form.getState();

    const inclusivePrice = calculateInclusivePriceWithOptions({
      msrpValue: parseInputWithDecimal(_get(values, 'pricingDetails.msrp')),
      discountOnMSRP: value,
      removeDefaultOptions,
      options: optionsInStore,
    });

    form.change('pricingDetails.inclusivePrice', inclusivePrice);
  };

  getPricingToggle = () => {
    const { enablePricingUpdate } = this.state;
    const { getPricingConfg, vehicleID } = this.props;
    this.setState({ enablePricingUpdate: !enablePricingUpdate }, async () => {
      await getPricingConfg(vehicleID, this.state?.enablePricingUpdate);
    });
  };

  getStopAutomaticPriceUpdates = () => (
    <div className="d-flex justify-content-start align-items-center">
      <Content>{__('Stop Automatic Price Updates')}</Content>
      <InfoBadge infoBadgeClassName="p-l-16" helpText={__('Toggle this setting to stop automatic price updates')} />
    </div>
  );

  render() {
    const { fields, enablePricingUpdate } = this.state;
    const { viSettings } = this.props;
    return (
      <React.Fragment>
        <div className={styles.pricingHeaderContainer}>
          <div className={styles.pricingSectionTitle}>
            <Heading size={3} className="m-y-16">
              {__('Vehicle Pricing')}
            </Heading>
          </div>
          <PropertyControlledComponent
            controllerProperty={
              !PROGRAM_CONFIG.shouldHideAutoPriceUpdates() && viSettings?.otherSettings?.enablePriceUpdateConfig?.enable
            }>
            <DescriptionSwitch
              detail={this.getStopAutomaticPriceUpdates()}
              enabled={enablePricingUpdate}
              onToggle={this.getPricingToggle}
            />
          </PropertyControlledComponent>
        </div>
        <div className={styles.pricingFormContent}>{renderFormData(fields)}</div>
      </React.Fragment>
    );
  }
}
const PropTypePricinFormActions = {
  onSubmit: Proptypes.func.isRequired,
};

PricingForm.propTypes = {
  ...PropTypePricinFormActions,
  disablePricingEdits: Proptypes.bool.isRequired,
  removeDefaultOptions: Proptypes.bool,
  optionsInStore: Proptypes.array,
};
PricingForm.defaultProps = {
  ...PropTypePricinFormActions,
  removeDefaultOptions: false,
  optionsInStore: EMPTY_ARRAY,
};

export default withMetaData(PricingForm);
