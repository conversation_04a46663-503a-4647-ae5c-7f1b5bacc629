@use "tstyles/component.scss";

$headerHeight: 6.4rem;
$footerHeight: 6.4rem;

.drawerContainer {
  z-index: 999;

  :global(.ant-drawer-content-wrapper) {
    max-width: 100rem;
  }

  :global(.ant-drawer-header) {
    background-color: component.$glitter;
    @include component.flex($align-items: center);
    height: $headerHeight;
  }

  :global(.ant-drawer-body) {
    @include component.is-paddingless();
    height: calc(100% - $headerHeight);
  }
}

.bodyContainer {
  height: calc(100% - $footerHeight);

  .anchorContainer {
    width: 24rem;
    z-index: 1;
  }

  .formContainer {
    width: calc(100% - 24rem);
    flex-grow: 1;
    border-left: component.$border;
  }
}

.fieldTypeRadioGroup {
  :global(.ant-radio-button-wrapper) {
    font-family: component.$font-semi-bold;
    color: component.$atomic;
  }
  :global(.ant-radio-button-wrapper-checked) {
    color: component.$dodgerBlueLight !important;
    background-color: component.$aliceBlue !important;
  }
}

.fontBlack {
  color: component.$text-color;
}

.targetingSection {
  padding: 1.6rem 0 0 2.4rem !important;
}

.addButtonCTA {
  width: max-content;
}

.descriptionInput {
  height: auto !important;
}

.usageModuleTargetingHeader {
  margin-bottom: 0rem !important;
}

.usageModuleTargetingSubheader {
  font-family: component.$font-regular;
  font-size: component.$font-size-small;
  color: component.$atomic;
}
