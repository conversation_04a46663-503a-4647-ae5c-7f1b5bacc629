import _isEmpty from 'lodash/isEmpty';
import _trim from 'lodash/trim';
import _isNil from 'lodash/isNil';
import _isEqual from 'lodash/isEqual';

import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';
import { isRequiredRule, isTableColumnNameUniqueIgnoringCase } from 'tbase/utils/formValidators';
import { RADIO_TYPES } from 'tcomponents/atoms/Radio';
import InputWrapper from 'tcomponents/molecules/inputWrapper/InputWrapper';
import { Button, Checkbox, Radio, SelectInput, TextInput } from 'tcomponents/organisms/FormBuilder/fieldRenderers';
import { POSITIONS } from 'tcomponents/organisms/FormBuilder/fieldRenderers/checkbox/Checkbox.constants';
import { tableValidator } from 'tcomponents/utils/tableFieldValidators';
import CheckboxWithTooltip from 'twidgets/molecules/CheckboxWithTooltip';

import DealerConfigReader from 'readers/dealerConfig';
import { isMultiLingualEnabled } from 'helpers/vehicle.helper';
import { getPacksConfigurationLabelConfig, getSourceName } from 'helpers/common';
import TargetingTableField, {
  PARAMETERS as TARGETING_PARAMETERS,
  COLUMN_KEYS as TARGETING_COLUMN_KEYS,
} from 'molecules/TargetingTableField';
import { MESSAGES } from 'constants/constants';
import { USAGE_MODULE_TARGETING_IDS } from 'pages/SettingsV2/settings.constants';
import PROGRAM_CONFIG from 'constants/programConfig';

import { getFieldTypeRadioOptionsConfig } from './addFieldDrawer.config';
import {
  ANCHOR_IDS,
  FIELD_TYPES,
  FORM_FIELD_KEYS,
  MODES,
  AT_THE_TIME_OF_CREATION_STATUS_OPTION,
} from './addFieldDrawer.constants';
import {
  shouldShowCustomOptions,
  shouldShowCustomValuesTable,
  shouldShowDefaultValue,
  shouldShowSources,
  isAddingExtraOptionsSupported,
  shouldShowPacksConfiguration,
} from './addFieldDrawer.constraints';
import {
  getCustomFieldTypeOptions,
  getTargetingParameters,
  getReadOnlyTargetingParameters,
  getApplicationVisibilityFields,
  shouldShowReadOnlyParams,
} from './addFieldDrawer.helpers';
import { isUniqueFieldName, isValidSourceDisplayName } from './addFieldDrawer.utils';
import CustomFieldValues, { COLUMN_KEYS as CUSTOM_FIELD_VALUES_COLUMN_KEYS } from './molecules/customFieldValues';
import SourceFieldTable, { COLUMN_KEYS as SOURCE_TABLE_COLUMN_KEYS } from './molecules/sourceFieldTable';
import CustomOptions, { COLUMN_KEYS as CUSTOM_OPTIONS_COLUMN_KEYS } from './molecules/customOptions';
import DefaultValueToggle from './molecules/defaultValueToggle';

import styles from './addFieldDrawer.module.scss';

const getFieldType = ({ mode, isPricingField }) => ({
  id: FORM_FIELD_KEYS.FIELD_TYPE,
  renderer: Radio,
  renderOptions: {
    radios: getFieldTypeRadioOptionsConfig(isPricingField),
    type: RADIO_TYPES.RADIO_BUTTON,
    fieldClassName: 'm-0',
    className: styles.fieldTypeRadioGroup,
    disabled: mode === MODES.EDIT,
  },
});

const getNameField = ({ type, mode, fieldNameOptions, allFieldNames, currentLanguageId }) => {
  const allFieldsPresent = _isEmpty(fieldNameOptions) && mode === MODES.ADD;
  const validators = [isRequiredRule];
  const dealerDefaultLanguage = DealerConfigReader.getDealerLanguage();
  if (_isEqual(dealerDefaultLanguage, currentLanguageId)) validators.push(isUniqueFieldName(allFieldNames));

  return {
    id: FORM_FIELD_KEYS.FIELD_NAME,
    renderer: type === FIELD_TYPES.GENERAL ? SelectInput : TextInput,
    renderOptions: {
      label: __('Field Name'),
      ...(type === FIELD_TYPES.GENERAL
        ? {
            options: fieldNameOptions || EMPTY_ARRAY,
            shouldConvertValueToArray: true,
            isDisabled: mode === MODES.EDIT || allFieldsPresent,
            validators: [isRequiredRule],
            description: allFieldsPresent ? MESSAGES.SYSTEM_FIELDS_ALREADY_PRESENT : EMPTY_STRING,
          }
        : {
            triggerChangeOnBlur: true,
            validators,
            isMultiLingual: isMultiLingualEnabled(),
          }),
      required: true,
      size: 6,
    },
  };
};

const DESCRIPTION = {
  id: FORM_FIELD_KEYS.DESCRIPTION,
  renderer: TextInput,
  renderOptions: {
    label: __('Description (Max. 120 characters)'),
    placeholder: __('Type Here'),
    triggerChangeOnBlur: true,
    parsers: [_trim],
    rows: 3,
    size: 12,
    inputComponentType: InputWrapper.INPUT_TYPE.TEXTAREA,
    className: styles.descriptionInput,
    maxLength: 120,
  },
};

const VISIBILITY = {
  id: FORM_FIELD_KEYS.VISIBILITY,
  renderer: Checkbox,
  renderOptions: {
    label: __('Show Field'),
    description: __('Conditions: Show this field only when selected parameters are met'),
    position: POSITIONS.RIGHT,
    containerClassName: 'flex-row',
    fieldLabelClassName: 'm-l-8',
    labelClassName: styles.fontBlack,
    fieldClassName: 'm-b-8',
  },
};

const VISIBILITY_TARGETING = {
  id: FORM_FIELD_KEYS.VISIBILITY_TARGETING,
  renderer: TargetingTableField,
  renderOptions: {
    parameters: [TARGETING_PARAMETERS.STOCK_TYPE, TARGETING_PARAMETERS.STOCK_SUBTYPE, TARGETING_PARAMETERS.SOURCE],
    validators: [
      tableValidator({
        [TARGETING_COLUMN_KEYS.PARAMETER]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.OPERATOR]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.VALUES]: [isRequiredRule],
      }),
    ],
    size: 12,
  },
};

const getMandatoryField = ({ readOnly, isMandatoryBySystem }) => {
  let message = EMPTY_STRING;
  if (readOnly) {
    message = MESSAGES.MANDATORY_READONLY_CONFLICT_MESSAGE;
  } else if (isMandatoryBySystem) {
    message = MESSAGES.MANDATORY_DISABLED_MESSAGE;
  }
  return {
    id: FORM_FIELD_KEYS.MANDATORY,
    renderer: CheckboxWithTooltip,
    renderOptions: {
      label: __('Make Field Mandatory'),
      description: __(
        'Conditions: Specify parameters when the field is mandatory. If nothing is selected, the field will be mandatory in all cases.'
      ),
      position: POSITIONS.RIGHT,
      containerClassName: 'flex-row',
      fieldLabelClassName: 'm-l-8',
      labelClassName: styles.fontBlack,
      fieldClassName: 'm-b-8',
      disabled: readOnly || isMandatoryBySystem || PROGRAM_CONFIG.disableMandatoryField(),
      tooltipTitle: message,
    },
  };
};

const getMandatoryTargeting = ({ fieldData }) => ({
  id: FORM_FIELD_KEYS.MANDATORY_TARGETING,
  renderer: TargetingTableField,
  renderOptions: {
    parameters: getTargetingParameters(fieldData),
    validators: [
      tableValidator({
        [TARGETING_COLUMN_KEYS.PARAMETER]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.OPERATOR]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.VALUES]: [isRequiredRule],
      }),
    ],
    size: 12,
  },
});

const getCustomFieldTypes = ({ isPricingField }) => ({
  id: FORM_FIELD_KEYS.CUSTOM_FIELD_TYPE,
  renderer: SelectInput,
  renderOptions: {
    options: getCustomFieldTypeOptions(isPricingField),
    isDisabled: isPricingField,
    shouldConvertValueToArray: true,
    required: true,
    validators: [isRequiredRule],
    fieldClassName: 'm-b-8',
    size: 6,
  },
});

const getCustomFieldValues = ({ customFieldType }) => ({
  id: FORM_FIELD_KEYS.CUSTOM_FIELD_VALUES,
  renderer: CustomFieldValues,
  renderOptions: {
    customFieldType,
    validators: [
      tableValidator({
        [CUSTOM_FIELD_VALUES_COLUMN_KEYS.NAME]: [isRequiredRule],
      }),
    ],
    fieldClassName: 'm-b-8',
    size: 8,
  },
});

const ADD_CUSTOM_FIELD_VALUE_BUTTON = {
  id: FORM_FIELD_KEYS.ADD_CUSTOM_FIELD_VALUE_BUTTON,
  renderer: Button,
  renderOptions: {
    label: __('Add Value'),
    view: Button.VIEW.TERTIARY,
    className: styles.addButtonCTA,
  },
};

const DEFAULT_VALUE_ENABLED = {
  id: FORM_FIELD_KEYS.DEFAULT_VALUE_ENABLED,
  renderer: DefaultValueToggle,
  renderOptions: {
    fieldClassName: 'm-b-8',
    disabled: PROGRAM_CONFIG.disableDefaultValueField(),
  },
};

const getDefaultValueField = ({ defaultValueOptions }) => ({
  id: FORM_FIELD_KEYS.DEFAULT_VALUE,
  renderer: SelectInput,
  renderOptions: {
    options: defaultValueOptions,
    shouldConvertValueToArray: true,
    required: true,
    validators: [isRequiredRule],
    size: 6,
    isDisabled: PROGRAM_CONFIG.disableDefaultValueField(),
  },
});

const SOURCE_OPTIONS = {
  id: FORM_FIELD_KEYS.SOURCE_OPTIONS,
  renderer: SourceFieldTable,
  renderOptions: {
    validators: [
      tableValidator({
        [SOURCE_TABLE_COLUMN_KEYS.DETAILS]: [isValidSourceDisplayName],
      }),
    ],
    size: 12,
  },
};

const getCustomOptionsField = ({ fieldData }) => ({
  id: FORM_FIELD_KEYS.CUSTOM_OPTIONS,
  renderer: CustomOptions,
  renderOptions: {
    fieldKey: fieldData?.key,
    validators: [
      tableValidator({
        [CUSTOM_OPTIONS_COLUMN_KEYS.NAME]: [isRequiredRule, isTableColumnNameUniqueIgnoringCase],
      }),
    ],
    fieldClassName: 'm-b-8',
    size: 8,
  },
});

const ADD_CUSTOM_OPTION_BUTTON = {
  id: FORM_FIELD_KEYS.ADD_CUSTOM_OPTION_BUTTON,
  renderer: Button,
  renderOptions: {
    label: __('Add Option'),
    view: Button.VIEW.TERTIARY,
    className: styles.addButtonCTA,
  },
};

const getReadOnlyField = ({ formEditable, mandatory }) => {
  // _isNil(formEditable) is needed when no field is selected or when opting for a custom field in Add Field Drawer.
  const isReadOnlyDisabled = PROGRAM_CONFIG.disableReadOnlyField();
  const shouldDisable = isReadOnlyDisabled || mandatory || (_isNil(formEditable) ? false : !formEditable);
  const errMessage = mandatory ? MESSAGES.MANDATORY_READONLY_CONFLICT_MESSAGE : MESSAGES.READ_ONLY_DISABLED_MESSAGE;
  return {
    id: FORM_FIELD_KEYS.READ_ONLY,
    renderer: CheckboxWithTooltip,
    renderOptions: {
      label: __('Make Field Read-only'),
      description: __(
        'Conditions: Specify parameters when the field is Read-Only. If nothing is specified the field will be Read-Only in all cases.'
      ),
      position: POSITIONS.RIGHT,
      containerClassName: 'flex-row',
      fieldLabelClassName: 'm-l-8',
      labelClassName: styles.fontBlack,
      fieldClassName: 'm-b-8',
      disabled: shouldDisable,
      tooltipTitle: shouldDisable && !isReadOnlyDisabled ? errMessage : EMPTY_STRING,
    },
  };
};

const READ_ONLY_TARGETING = {
  id: FORM_FIELD_KEYS.READ_ONLY_TARGETING,
  renderer: TargetingTableField,
  renderOptions: {
    additional: {
      addAdditionalStatusOptions: true,
      additionalStatusOptions: AT_THE_TIME_OF_CREATION_STATUS_OPTION,
    },
    parameters: getReadOnlyTargetingParameters(),
    validators: [
      tableValidator({
        [TARGETING_COLUMN_KEYS.PARAMETER]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.OPERATOR]: [isRequiredRule],
        [TARGETING_COLUMN_KEYS.VALUES]: [isRequiredRule],
      }),
    ],
    size: 12,
  },
};

const INCLUDE_IN_MOBILE_RECEIVING = {
  id: FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_RECEIVING,
  renderer: Checkbox,
  renderOptions: {
    label: __('Include in mobile receiving'),
    position: POSITIONS.RIGHT,
    containerClassName: 'flex-row',
    fieldLabelClassName: 'm-l-8',
    labelClassName: styles.fontBlack,
    fieldClassName: 'm-b-8',
  },
};

const INCLUDE_IN_MOBILE_INVENTORY = {
  id: FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_INVENTORY,
  renderer: Checkbox,
  renderOptions: {
    label: __('Include in mobile inventory'),
    position: POSITIONS.RIGHT,
    containerClassName: 'flex-row',
    fieldLabelClassName: 'm-l-8',
    labelClassName: styles.fontBlack,
    fieldClassName: 'm-b-8',
  },
};

const getPacksConfiguration = () => {
  const { label, description } = getPacksConfigurationLabelConfig();
  return {
    id: FORM_FIELD_KEYS.PACKS_CONFIGURATION,
    renderer: Checkbox,
    renderOptions: {
      label,
      description,
      position: POSITIONS.RIGHT,
      containerClassName: 'flex-row',
      fieldLabelClassName: 'm-l-8',
      labelClassName: styles.fontBlack,
      fieldClassName: 'm-b-8 align-items-center',
    },
  };
};

export const getFields = ({
  type,
  mode,
  fieldData,
  customFieldType,
  defaultValueOptions,
  fieldNameOptions,
  allFieldNames,
  formEditable,
  readOnly,
  mandatory,
  isMandatoryBySystem,
  isPricingField,
  currentLanguageId,
}) => ({
  [FORM_FIELD_KEYS.FIELD_TYPE]: getFieldType({ mode, isPricingField }),
  [FORM_FIELD_KEYS.FIELD_NAME]: getNameField({
    type,
    mode,
    fieldNameOptions,
    allFieldNames,
    currentLanguageId,
  }),
  [FORM_FIELD_KEYS.DESCRIPTION]: DESCRIPTION,
  [FORM_FIELD_KEYS.VISIBILITY]: VISIBILITY,
  [FORM_FIELD_KEYS.VISIBILITY_TARGETING]: VISIBILITY_TARGETING,
  [FORM_FIELD_KEYS.MANDATORY]: getMandatoryField({ readOnly, isMandatoryBySystem }),
  [FORM_FIELD_KEYS.MANDATORY_TARGETING]: getMandatoryTargeting({ fieldData }),
  [FORM_FIELD_KEYS.CUSTOM_FIELD_TYPE]: getCustomFieldTypes({ isPricingField }),
  [FORM_FIELD_KEYS.CUSTOM_FIELD_VALUES]: getCustomFieldValues({ customFieldType }),
  [FORM_FIELD_KEYS.ADD_CUSTOM_FIELD_VALUE_BUTTON]: ADD_CUSTOM_FIELD_VALUE_BUTTON,
  [FORM_FIELD_KEYS.DEFAULT_VALUE_ENABLED]: DEFAULT_VALUE_ENABLED,
  [FORM_FIELD_KEYS.DEFAULT_VALUE]: getDefaultValueField({ defaultValueOptions }),
  [FORM_FIELD_KEYS.SOURCE_OPTIONS]: SOURCE_OPTIONS,
  [FORM_FIELD_KEYS.CUSTOM_OPTIONS]: getCustomOptionsField({ fieldData }),
  [FORM_FIELD_KEYS.ADD_CUSTOM_OPTION_BUTTON]: ADD_CUSTOM_OPTION_BUTTON,
  [FORM_FIELD_KEYS.READ_ONLY]: getReadOnlyField({ formEditable, mandatory }),
  [FORM_FIELD_KEYS.READ_ONLY_TARGETING]: READ_ONLY_TARGETING,
  [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_RECEIVING]: INCLUDE_IN_MOBILE_RECEIVING,
  [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_INVENTORY]: INCLUDE_IN_MOBILE_INVENTORY,
  [FORM_FIELD_KEYS.PACKS_CONFIGURATION]: getPacksConfiguration(),
  ...getApplicationVisibilityFields(),
});

export const getSections = ({
  type,
  fieldData,
  mandatory,
  isMandatoryBySystem,
  defaultValueEnabled,
  customFieldType,
  isPricingField,
  readOnly,
  formEditable,
}) => [
  {
    header: {
      hashId: ANCHOR_IDS.NAME_AND_DESCRIPTION,
    },
    className: 'p-t-24 p-b-0',
    rows: [
      {
        columns: [FORM_FIELD_KEYS.FIELD_TYPE],
      },
    ],
  },
  {
    header: {
      label: __('Name'),
      size: 4,
    },
    className: 'p-t-24 p-b-0',
    rows: [
      {
        columns: [FORM_FIELD_KEYS.FIELD_NAME],
      },
      {
        columns: [FORM_FIELD_KEYS.DESCRIPTION],
      },
    ],
  },
  ...(type === FIELD_TYPES.CUSTOM
    ? [
        {
          header: {
            hashId: ANCHOR_IDS.TYPE_AND_VALUE,
            label: __('Type and Value'),
            size: 4,
          },
          className: styles.targetingSection,
          rows: [
            {
              columns: [FORM_FIELD_KEYS.CUSTOM_FIELD_TYPE],
            },
            ...(shouldShowCustomValuesTable(customFieldType)
              ? [
                  {
                    columns: [FORM_FIELD_KEYS.CUSTOM_FIELD_VALUES],
                  },
                  {
                    columns: [FORM_FIELD_KEYS.ADD_CUSTOM_FIELD_VALUE_BUTTON],
                  },
                ]
              : EMPTY_ARRAY),
          ],
        },
      ]
    : EMPTY_ARRAY),

  // NOTE: Enable once visibility targeting supported from BE
  // {
  //   header: {
  //     hashId: ANCHOR_IDS.VISIBILITY,
  //     label: __('Visibility'),
  //     size: 4,
  //   },
  //   className: styles.targetingSection,
  //   rows: [
  //     {
  //       columns: [FORM_FIELD_KEYS.VISIBILITY],
  //     },
  //     ...(visibility ? [{ columns: [FORM_FIELD_KEYS.VISIBILITY_TARGETING] }] : EMPTY_ARRAY),
  //   ],
  // },
  ...(PROGRAM_CONFIG.shouldShowSetupChangesForBuildVehicle()
    ? [
        {
          header: {
            hashId: ANCHOR_IDS.USAGE_MODULE_TARGETING,
            label: __('Usage Module Targeting'),
            size: 4,
            className: styles.usageModuleTargetingHeader,
          },
          subHeader: {
            label: __('Select the applications where you would like this field to be displayed'),
            className: styles.usageModuleTargetingSubheader,
          },
          className: 'p-t-16 p-b-0',
          rows: [
            {
              columns: [
                USAGE_MODULE_TARGETING_IDS.LEADS_VISIBILITY,
                USAGE_MODULE_TARGETING_IDS.DEALS_BUILD_VISIBILITY,
                USAGE_MODULE_TARGETING_IDS.DEALS_EDIT_VISIBILITY,
                ...(PROGRAM_CONFIG.shouldShowMobileOptions()
                  ? [
                      USAGE_MODULE_TARGETING_IDS.MOBILE_INVENTORY_VISIBILITY,
                      USAGE_MODULE_TARGETING_IDS.MOBILE_RECEIVING_VISIBILITY,
                    ]
                  : EMPTY_ARRAY),
              ],
            },
          ],
        },
      ]
    : EMPTY_ARRAY),
  {
    header: {
      hashId: ANCHOR_IDS.MANDATORY,
      label: __('Mandatory'),
      size: 4,
    },
    className: 'p-t-16 p-b-0',
    rows: [
      {
        columns: [FORM_FIELD_KEYS.MANDATORY],
      },
      ...(mandatory && !isMandatoryBySystem ? [{ columns: [FORM_FIELD_KEYS.MANDATORY_TARGETING] }] : EMPTY_ARRAY),
    ],
  },
  {
    header: {
      hashId: ANCHOR_IDS.READ_ONLY,
      label: __('Read-only'),
      size: 4,
    },
    className: 'p-t-16 p-b-0',
    rows: [
      {
        columns: [FORM_FIELD_KEYS.READ_ONLY],
      },
      ...(shouldShowReadOnlyParams(readOnly, formEditable)
        ? [{ columns: [FORM_FIELD_KEYS.READ_ONLY_TARGETING] }]
        : EMPTY_ARRAY),
    ],
  },
  ...(shouldShowPacksConfiguration({ isPricingField, type })
    ? [
        {
          header: {
            hashId: ANCHOR_IDS.PACKS_CONFIGURATION,
            label: getPacksConfigurationLabelConfig()?.heading,
            size: 4,
          },
          className: 'p-t-16 p-b-0',
          rows: [
            {
              columns: [FORM_FIELD_KEYS.PACKS_CONFIGURATION],
            },
          ],
        },
      ]
    : EMPTY_ARRAY),
  ...(shouldShowDefaultValue({ type, fieldData })
    ? [
        {
          header: { hashId: ANCHOR_IDS.DEFAULT_VALUE },
          className: 'p-t-16',
          rows: [
            {
              columns: [FORM_FIELD_KEYS.DEFAULT_VALUE_ENABLED],
            },
            ...(defaultValueEnabled
              ? [
                  {
                    columns: [FORM_FIELD_KEYS.DEFAULT_VALUE],
                  },
                ]
              : EMPTY_ARRAY),
          ],
        },
      ]
    : EMPTY_ARRAY),
  ...(PROGRAM_CONFIG.shouldShowMobileOptions() && !PROGRAM_CONFIG.shouldShowSetupChangesForBuildVehicle()
    ? [
        {
          header: { hashId: ANCHOR_IDS.MOBILE_OPTIONS, label: __('Mobile Options'), size: 4 },
          className: 'p-t-16',
          rows: [
            { columns: [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_RECEIVING] },
            { columns: [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_INVENTORY] },
          ],
        },
      ]
    : EMPTY_ARRAY),
  ...(shouldShowSources({ fieldData })
    ? [
        {
          header: {
            hashId: ANCHOR_IDS.SOURCES,
            label: getSourceName(),
            size: 4,
          },
          className: 'p-t-16',
          rows: [
            {
              columns: [FORM_FIELD_KEYS.SOURCE_OPTIONS],
            },
          ],
        },
      ]
    : EMPTY_ARRAY),
  ...(shouldShowCustomOptions({ type, fieldData })
    ? [
        {
          header: {
            hashId: ANCHOR_IDS.CUSTOM_OPTIONS,
            label: fieldData?.name,
            className: 'm-b-8',
            size: 4,
          },
          className: 'p-t-16',
          rows: [
            {
              columns: [FORM_FIELD_KEYS.CUSTOM_OPTIONS],
            },
            ...(isAddingExtraOptionsSupported(fieldData?.key)
              ? [{ columns: [FORM_FIELD_KEYS.ADD_CUSTOM_OPTION_BUTTON] }]
              : EMPTY_ARRAY),
          ],
        },
      ]
    : EMPTY_ARRAY),
];
