import _compact from 'lodash/compact';
import _map from 'lodash/map';
import _includes from 'lodash/includes';
import _isNaN from 'lodash/isNaN';
import _toLower from 'lodash/toLower';
import _toNumber from 'lodash/toNumber';
import _trim from 'lodash/trim';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { tget } from 'tbase/utils/general';
import { isRequiredRule } from 'tbase/utils/formValidators';
import { ERRORS } from 'twidgets/constants/vi.constants';

import { FORM_CONTAINER_ID } from './addFieldDrawer.constants';
import SourceFieldReader from './molecules/sourceFieldTable/SourceFieldTable.readers';

export const getFormContainer = () => document.getElementById(FORM_CONTAINER_ID);

const isDisplayNameUnique = (
  valueToTest,
  { allValues = EMPTY_OBJECT, pathToUpdate = 0, tableFieldId = EMPTY_STRING }
) => {
  const tableValues = tget(allValues, tableFieldId, EMPTY_ARRAY);
  const filteredValueToTest = _toLower(_trim(valueToTest));
  const tableFieldValues = _compact(
    _map(
      tableValues,
      (item, index) => index !== _toNumber(pathToUpdate) && _toLower(_trim(SourceFieldReader.displayName(item)))
    )
  );
  return _includes(tableFieldValues, filteredValueToTest)
    ? { isValid: false, message: { displayName: __('Cannot have duplicate values') } }
    : { isValid: true };
};

export const isValidSourceDisplayName = (fieldId, valueToTest, params) => {
  const { displayName, enable: isSourceEnabled } = valueToTest || EMPTY_OBJECT;

  const uniqueValidation = isDisplayNameUnique(displayName, params);
  if (!isSourceEnabled) return uniqueValidation;

  const { isValid } = isRequiredRule(fieldId, displayName);
  if (!isValid) return { isValid, message: { displayName: __('This field is mandatory') } };
  return uniqueValidation;
};

const isNumberString = (valueToTest = EMPTY_STRING) => {
  const numberString = valueToTest.replace(/ /g, '');
  return !_isNaN(_toNumber(numberString));
};

export const isUniqueFieldName = (existingFieldNames = EMPTY_ARRAY) => (_, valueToTest) => {
  if (valueToTest && isNumberString(valueToTest)) {
    return { isValid: false, message: ERRORS.INVALID_FIELD_NAME };
  }
  if (_includes(existingFieldNames, _toLower(valueToTest))) {
    return { isValid: false, message: ERRORS.DUPLICATE_NAME };
  }
  return { isValid: true };
};
