import _reject from 'lodash/reject';
import _find from 'lodash/find';
import _filter from 'lodash/filter';
import _flatMap from 'lodash/flatMap';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _toLower from 'lodash/toLower';
import _isNil from 'lodash/isNil';
import _reduce from 'lodash/reduce';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { uuid } from 'tbase/utils/general';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { FIELD_TYPES_KEY } from 'twidgets/constants/vi.constants';
import { getLabelFromKey } from 'twidgets/appServices/sales/helpers/viFormFields';
import CheckboxWithTooltip from 'twidgets/molecules/CheckboxWithTooltip';
import { POSITIONS } from 'tcomponents/organisms/FormBuilder/fieldRenderers/checkbox/Checkbox.constants';

import { FIELD_TYPES_KEY_VS_LABEL as CUSTOM_FIELD_TYPES_KEY_VS_LABEL } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import { PARAMETERS as TARGETING_PARAMETERS } from 'molecules/TargetingTableField/targetingTableField.constants';
import { getUsageModuleTargetingConfig } from 'pages/SettingsV2/Settings.helpers';

import { shouldShowSources } from './addFieldDrawer.constraints';

import styles from './addFieldDrawer.module.scss';

export const getTargetTypeData = (targetRules, targetType) => {
  const targetTypeData = _find(targetRules, { targetType });
  const { enabled = false, rules = EMPTY_ARRAY, defaultValue } = targetTypeData || EMPTY_OBJECT;
  const updatedRules = _isNil(rules) ? EMPTY_ARRAY : rules;
  return { enabled, rules: updatedRules, defaultValue };
};

export const getCustomFieldTypeOptions = isPricingField => {
  if (isPricingField) return [{ label: __('Pricing'), value: FIELD_TYPES_KEY.PRICE_OR_COST }];
  const fieldTypeConfig = isInchcapeOrRRG()
    ? { ...CUSTOM_FIELD_TYPES_KEY_VS_LABEL, [FIELD_TYPES_KEY.BOOLEAN]: __('Yes/No') }
    : CUSTOM_FIELD_TYPES_KEY_VS_LABEL;
  return _map(fieldTypeConfig, (label, key) => ({ label, value: key }));
};

export const getFieldNameOptions = (fields = EMPTY_ARRAY) =>
  _map(fields, field => {
    const { name, key } = field || EMPTY_OBJECT;
    const label = getLabelFromKey(key) || name;
    return { label, value: key, data: field };
  });

export const getCustomOptionsAnchorLabel = ({ fieldData }) => fieldData?.name;

export const getTargetingParameters = fieldData => {
  if (shouldShowSources({ fieldData })) return [TARGETING_PARAMETERS.STOCK_TYPE, TARGETING_PARAMETERS.STOCK_SUBTYPE];
  return [TARGETING_PARAMETERS.STOCK_TYPE, TARGETING_PARAMETERS.STOCK_SUBTYPE, TARGETING_PARAMETERS.SOURCE];
};

export const getApplicableFieldsToBeAdded = ({ allSections, isPricingField, generalFields, pricingFields }) => {
  const existingFieldIds = _flatMap(allSections, 'fields');
  const fields = isPricingField ? pricingFields : generalFields;
  return _filter(fields, ({ id }) => !_includes(existingFieldIds, id));
};

export const getAllFieldNamesList = fields => _map(fields, field => _toLower(field?.name));

export const getCustomOptionsWithDefaultRows = options =>
  _isEmpty(options) ? [{ id: uuid(), name: EMPTY_STRING, isNewRow: true }] : options;

export const getReadOnlyValue = (formEditable, readOnly) => {
  // _isNil(formEditable) is needed when no field is selected or when opting for a custom field in Add Field Drawer.
  if (_isNil(formEditable)) return false;
  return formEditable ? readOnly : true;
};

export const getReadOnlyTargetingParameters = () => [TARGETING_PARAMETERS.STATUS, TARGETING_PARAMETERS.STOCK_TYPE];

export const getApplicationVisibilityFields = () =>
  _reduce(
    getUsageModuleTargetingConfig(),
    (acc, module) => {
      const { id, label, fieldClassName } = module;
      return {
        ...acc,
        [id]: {
          id,
          renderer: CheckboxWithTooltip,
          renderOptions: {
            label,
            position: POSITIONS.RIGHT,
            containerClassName: 'flex-row',
            fieldLabelClassName: 'm-l-8',
            labelClassName: styles.fontBlack,
            fieldClassName,
            disabled: PROGRAM_CONFIG.disableUsageModuleTargetingField(),
          },
        },
      };
    },
    {}
  );

export const shouldShowReadOnlyParams = (readOnly, formEditable) => readOnly && (_isNil(formEditable) || formEditable);

export const getValidTabFieldsWithoutTheCurrentField = ({
  fieldData,
  isPricingField,
  pricingFields,
  customPricingFields,
  generalFields,
  customGeneralFields,
}) =>
  _reject(isPricingField ? [...pricingFields, ...customPricingFields] : [...generalFields, ...customGeneralFields], {
    id: fieldData?.id,
  });
