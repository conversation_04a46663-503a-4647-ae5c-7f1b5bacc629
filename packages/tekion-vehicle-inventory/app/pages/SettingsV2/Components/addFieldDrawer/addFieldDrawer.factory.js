import _concat from 'lodash/concat';
import _map from 'lodash/map';
import _omit from 'lodash/omit';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { tget } from 'tbase/utils/general';

import { ASSET_SUB_TYPES } from 'constants/settings';
import { USAGE_MODULE_TARGETING_IDS } from 'pages/SettingsV2/settings.constants';

import {
  FIELD_TYPES,
  FORM_FIELD_KEYS,
  KEYS_TO_OMIT_IN_FIELD_OPTIONS,
  MODES,
  TARGET_TYPES,
} from './addFieldDrawer.constants';
import {
  shouldShowSources,
  shouldShowCustomOptions,
  shouldShowDefaultValue,
  shouldShowTypeAndValue,
  shouldShowPacksConfiguration,
} from './addFieldDrawer.constraints';
import { getCustomOptionsWithDefaultRows, getTargetTypeData, getReadOnlyValue } from './addFieldDrawer.helpers';
import FieldReader from './addFieldDrawer.reader';

export const getFormValuesFromField = ({ fieldData }) => {
  const fieldKey = FieldReader.key(fieldData);
  const targetRules = FieldReader.targetRules(fieldData);
  const systemDefined = FieldReader.systemDefined(fieldData) ?? true;
  const options = FieldReader.options(fieldData) || EMPTY_ARRAY;
  const fieldEditable = FieldReader.formEditable(fieldData);
  const isMandatoryBySystem = FieldReader.isMandatoryBySystem(fieldData);
  const { enabled: mandatory, rules: mandatoryTargeting } = getTargetTypeData(targetRules, TARGET_TYPES.MANDATORY);
  const { enabled: readOnly, rules: readOnlyTargeting } = getTargetTypeData(targetRules, TARGET_TYPES.READ_ONLY);
  const { enabled: defaultValueEnabled, defaultValue } = getTargetTypeData(targetRules, TARGET_TYPES.DEFAULT_VALUE);
  const { enabled: packsConfiguration } = getTargetTypeData(targetRules, TARGET_TYPES.PACKS_CONFIGURATION);
  const type = systemDefined ? FIELD_TYPES.GENERAL : FIELD_TYPES.CUSTOM;
  return {
    languages: FieldReader.languages(fieldData),
    [FORM_FIELD_KEYS.FIELD_TYPE]: type,
    [FORM_FIELD_KEYS.FIELD_NAME]: systemDefined ? fieldKey : FieldReader.name(fieldData),
    [FORM_FIELD_KEYS.DESCRIPTION]: FieldReader.description(fieldData),
    [FORM_FIELD_KEYS.MANDATORY]: mandatory || isMandatoryBySystem,
    [FORM_FIELD_KEYS.MANDATORY_TARGETING]: mandatoryTargeting,
    isMandatoryBySystem,
    [FORM_FIELD_KEYS.CUSTOM_FIELD_TYPE]: FieldReader.fieldType(fieldData),
    [FORM_FIELD_KEYS.CUSTOM_FIELD_VALUES]: options,
    [FORM_FIELD_KEYS.DEFAULT_VALUE_ENABLED]: defaultValueEnabled,
    [FORM_FIELD_KEYS.DEFAULT_VALUE]: defaultValue,
    ...(shouldShowSources({ fieldData }) ? { [FORM_FIELD_KEYS.SOURCE_OPTIONS]: options } : EMPTY_OBJECT),
    ...(shouldShowCustomOptions({ type, fieldData })
      ? { [FORM_FIELD_KEYS.CUSTOM_OPTIONS]: getCustomOptionsWithDefaultRows(options) }
      : EMPTY_OBJECT),
    [FORM_FIELD_KEYS.READ_ONLY]: getReadOnlyValue(fieldEditable, readOnly),
    [FORM_FIELD_KEYS.READ_ONLY_TARGETING]: readOnlyTargeting,
    [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_RECEIVING]: FieldReader.includeInMobileReceiving(fieldData),
    [FORM_FIELD_KEYS.INCLUDE_IN_MOBILE_INVENTORY]: FieldReader.includeInMobileInventory(fieldData),
    [FORM_FIELD_KEYS.PACKS_CONFIGURATION]: packsConfiguration,
    [USAGE_MODULE_TARGETING_IDS.LEADS_VISIBILITY]: FieldReader.leadsVisibility(fieldData),
    [USAGE_MODULE_TARGETING_IDS.DEALS_BUILD_VISIBILITY]: FieldReader.dealsBuildVisibility(fieldData),
    [USAGE_MODULE_TARGETING_IDS.DEALS_EDIT_VISIBILITY]: FieldReader.dealsEditVisibility(fieldData),
    [USAGE_MODULE_TARGETING_IDS.MOBILE_INVENTORY_VISIBILITY]: FieldReader.mobileInventoryVisibility(fieldData),
    [USAGE_MODULE_TARGETING_IDS.MOBILE_RECEIVING_VISIBILITY]: FieldReader.mobileReceivingVisibility(fieldData),
  };
};

export const getMandatoryTargetRulesPayload = ({ mandatory, mandatoryTargeting }) => ({
  targetType: TARGET_TYPES.MANDATORY,
  enabled: mandatory,
  rules: mandatoryTargeting,
});

export const getReadOnlyTargetRulesPayload = ({ readOnly, readOnlyTargeting }) => ({
  targetType: TARGET_TYPES.READ_ONLY,
  enabled: readOnly,
  rules: readOnlyTargeting,
});

export const getDefaultValueTargetRulesPayload = ({ defaultValue, defaultValueEnabled }) => ({
  targetType: TARGET_TYPES.DEFAULT_VALUE,
  enabled: defaultValueEnabled,
  rules: EMPTY_ARRAY,
  defaultValue,
});

export const getPacksConfigurationTargetRulesPayload = ({ packsConfiguration }) => ({
  targetType: TARGET_TYPES.PACKS_CONFIGURATION,
  enabled: packsConfiguration,
  rules: EMPTY_ARRAY,
});

export const getTargetRulesPayload = ({
  type,
  fieldData,
  mandatory,
  mandatoryTargeting,
  defaultValueEnabled,
  defaultValue,
  readOnly,
  readOnlyTargeting,
  isPricingField,
  packsConfiguration,
}) => {
  const targetRules = [
    getMandatoryTargetRulesPayload({ mandatory, mandatoryTargeting }),
    getReadOnlyTargetRulesPayload({ readOnly, readOnlyTargeting }),
  ];
  if (shouldShowPacksConfiguration({ isPricingField, type })) {
    targetRules.push(getPacksConfigurationTargetRulesPayload({ packsConfiguration }));
  }
  if (shouldShowDefaultValue({ type, fieldData })) {
    targetRules.push(getDefaultValueTargetRulesPayload({ defaultValueEnabled, defaultValue }));
  }
  return targetRules;
};

export const getOptionsPayload = ({ fieldData, type, customFieldValues, sourceOptions, customOptions }) => {
  if (shouldShowSources({ fieldData })) return sourceOptions;
  if (shouldShowTypeAndValue({ type })) return customFieldValues;
  return _map(customOptions, obj => _omit(obj, KEYS_TO_OMIT_IN_FIELD_OPTIONS));
};

export const getCustomFieldData = ({ type, isPricingField, name }) => {
  if (type !== FIELD_TYPES.CUSTOM) return EMPTY_OBJECT;
  return {
    assetType: 'VI',
    vehicleType: 'AUTOMOBILE',
    assetSubType: isPricingField ? ASSET_SUB_TYPES.CUSTOM_FIELDS_PRICING : ASSET_SUB_TYPES.CUSTOM_FIELDS_GENERAL,
    name,
  };
};

export const getRequestPayload = ({ fieldData = EMPTY_OBJECT, formValues = EMPTY_OBJECT, isPricingField }) => {
  const {
    type,
    name,
    description,
    customFieldType,
    customFieldValues,
    defaultValue,
    defaultValueEnabled,
    mandatory,
    mandatoryTargeting,
    sourceOptions,
    customOptions,
    languages,
    readOnly,
    readOnlyTargeting,
    includeInMobileReceiving,
    includeInMobileInventory,
    leadsVisibility,
    dealsBuildVisibility,
    dealsEditVisibility,
    mobileInventoryVisibility,
    mobileReceivingVisibility,
    packsConfiguration,
  } = formValues;
  const targetRules = getTargetRulesPayload({
    type,
    fieldData,
    mandatory,
    mandatoryTargeting,
    defaultValueEnabled,
    defaultValue,
    readOnly,
    readOnlyTargeting,
    isPricingField,
    packsConfiguration,
  });
  const options = getOptionsPayload({ fieldData, type, customFieldValues, sourceOptions, customOptions });
  const customFieldData = getCustomFieldData({ type, isPricingField, name });

  return {
    ...fieldData,
    ...customFieldData,
    description,
    fieldType: customFieldType,
    options,
    targetRules,
    languages,
    readOnly,
    includeInMobileReceiving,
    includeInMobileInventory,
    applicationVisibilityMap: {
      LEADS: leadsVisibility,
      DEALS_BUILD: dealsBuildVisibility,
      DEALS_EDIT: dealsEditVisibility,
      MOBILE_INVENTORY: mobileInventoryVisibility,
      MOBILE_RECEIVING: mobileReceivingVisibility,
    },
  };
};

export const getInventoryUpdateFieldPayload = modifiedOptions => _map(modifiedOptions, obj => _omit(obj, 'id'));

export const getAddFieldToSectionPayload = ({ section = EMPTY_OBJECT, fieldId }) => ({
  ...section,
  fields: _concat(tget(section, 'fields', EMPTY_ARRAY), fieldId),
});

const editFieldAction = async ({ actions, requestPayload }) => {
  const response = await actions.updateDealerSystemFields(requestPayload);
  return response;
};

const addFieldAction = async ({ actions, requestPayload, section, type }) => {
  const addAction = type === FIELD_TYPES.CUSTOM ? actions.addNewCustomField : actions.updateDealerSystemFields;
  const response = await addAction(requestPayload);
  const { id: fieldId } = response || EMPTY_OBJECT;
  if (fieldId) {
    const payload = getAddFieldToSectionPayload({ section, fieldId });
    await actions.updateSectionContents(payload, false);
    await actions.fetchAllSections();
  }
  return response;
};

export const MODE_VS_SUBMIT_ACTION = {
  [MODES.ADD]: addFieldAction,
  [MODES.EDIT]: editFieldAction,
};
