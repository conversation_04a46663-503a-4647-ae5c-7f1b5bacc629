import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { uuid } from 'tbase/utils/general';

import { FETCH_FIELDS } from 'constants/mockData';
import {
  getFormValuesFromField,
  getMandatoryTargetRulesPayload,
  getDefaultValueTargetRulesPayload,
  getTargetRulesPayload,
  getOptionsPayload,
  getRequestPayload,
  getInventoryUpdateFieldPayload,
  getAddFieldToSectionPayload,
} from '../addFieldDrawer.factory';

const mockAllFields = FETCH_FIELDS;

const TEMP_UUID = 'XYZ';

jest.mock('tbase/utils/general', () => ({
  ...jest.requireActual('tbase/utils/general'),
  uuid: jest.fn(),
}));

describe('Test AddFieldDrawer factory', () => {
  beforeEach(() => {
    uuid.mockReturnValue(TEMP_UUID);
  });

  describe('Test getFormValuesFromField', () => {
    it('should get form values from field', () => {
      const mockYearFieldData = mockAllFields[1];
      const result = getFormValuesFromField({ fieldData: mockYearFieldData });
      expect(result).toEqual({
        type: 'GENERAL',
        name: 'YEAR',
        description: null,
        mandatory: false,
        mandatoryTargeting: [],
        customFieldType: 'SINGLE_SELECT',
        customFieldValues: [],
        defaultValueEnabled: true,
        defaultValue: ['2023'],
        languages: null,
        readOnly: true,
        dealsBuildVisibility: false,
        dealsEditVisibility: false,
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        isMandatoryBySystem: false,
        leadsVisibility: false,
        mobileInventoryVisibility: false,
        mobileReceivingVisibility: false,
        readOnlyTargeting: [],
        packsConfiguration: false,
      });
    });

    it('should consider field type as GENERAL if the corresponding value in fieldData is nullish', () => {
      const result = getFormValuesFromField({ fieldData: EMPTY_OBJECT });
      expect(result).toHaveProperty('type', 'GENERAL');
    });

    it('should have field type as CUSTOM and name as actual Field name if the systemDefined in fieldData is false', () => {
      const result = getFormValuesFromField({ fieldData: { systemDefined: false, name: 'Test Field' } });
      expect(result).toHaveProperty('type', 'CUSTOM');
      expect(result).toHaveProperty('name', 'Test Field');
    });

    it('should have sourceOptions in formValues if Source Field is selected', () => {
      const mockSourceField = mockAllFields[11];
      const result = getFormValuesFromField({ fieldData: mockSourceField });
      expect(result).toHaveProperty('sourceOptions', mockSourceField.options);
    });

    it('should have default customOption in formValues if field from FIELDS_WITH_CUSTOM_OPTIONS is selected with Empty options', () => {
      const mockLocationCodeField = mockAllFields[9];
      const result = getFormValuesFromField({ fieldData: mockLocationCodeField });

      expect(mockLocationCodeField.options).toEqual(EMPTY_ARRAY);
      expect(result).toHaveProperty('customOptions', [{ id: TEMP_UUID, isNewRow: true, name: '' }]);
    });

    it('should have field options as customOptions in formValues if field from FIELDS_WITH_CUSTOM_OPTIONS is selected', () => {
      const mockLocationCodeField = { ...mockAllFields[9], options: [{ name: 'A' }] };
      const result = getFormValuesFromField({ fieldData: mockLocationCodeField });
      expect(result).toHaveProperty('customOptions', mockLocationCodeField.options);
    });
  });

  describe('Test getMandatoryTargetRulesPayload', () => {
    it('should get mandatory target rules payload', () => {
      const mandatory = true;
      const mandatoryTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const result = getMandatoryTargetRulesPayload({ mandatory, mandatoryTargeting });
      expect(result).toEqual({
        targetType: 'MANDATORY',
        enabled: mandatory,
        rules: mandatoryTargeting,
      });
    });
  });

  describe('Test getDefaultValueTargetRulesPayload', () => {
    it('should get default value target rules payload', () => {
      const defaultValueEnabled = true;
      const defaultValue = ['2022'];
      const result = getDefaultValueTargetRulesPayload({ defaultValue, defaultValueEnabled });
      expect(result).toEqual({
        targetType: 'DEFAULT_VALUE',
        enabled: defaultValueEnabled,
        rules: EMPTY_ARRAY,
        defaultValue,
      });
    });
  });

  describe('Test getTargetRulesPayload', () => {
    it('should get target rules payload without default value', () => {
      const mandatory = true;
      const readOnly = false;
      const mandatoryTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const readOnlyTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['USED'],
        },
      ];
      const result = getTargetRulesPayload({
        type: 'GENERAL',
        fieldData: { key: 'SOURCE' },
        mandatory,
        mandatoryTargeting,
        defaultValueEnabled: false,
        defaultValue: null,
        readOnly,
        readOnlyTargeting,
      });

      expect(result).toEqual([
        {
          targetType: 'MANDATORY',
          enabled: mandatory,
          rules: mandatoryTargeting,
        },
        { targetType: 'READ_ONLY', enabled: readOnly, rules: readOnlyTargeting },
      ]);
    });

    it('should get target rules payload with default value', () => {
      const mandatory = true;
      const readOnly = false;
      const mandatoryTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const readOnlyTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['USED'],
        },
      ];
      const defaultValueEnabled = true;
      const defaultValue = ['2022'];

      const result = getTargetRulesPayload({
        type: 'GENERAL',
        fieldData: { key: 'YEAR' },
        mandatory,
        mandatoryTargeting,
        defaultValueEnabled,
        defaultValue,
        readOnlyTargeting,
        readOnly,
      });

      expect(result).toEqual([
        {
          targetType: 'MANDATORY',
          enabled: mandatory,
          rules: mandatoryTargeting,
        },
        { targetType: 'READ_ONLY', enabled: readOnly, rules: readOnlyTargeting },
        {
          targetType: 'DEFAULT_VALUE',
          enabled: defaultValueEnabled,
          rules: EMPTY_ARRAY,
          defaultValue,
        },
      ]);
    });

    it('should include packs configuration in target rules when isPricingField is true and Custom Field is updated', () => {
      const mandatory = true;
      const readOnly = false;
      const mandatoryTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const readOnlyTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['USED'],
        },
      ];

      const result = getTargetRulesPayload({
        type: 'CUSTOM',
        fieldData: { key: 'PRICING_CUSTOM_FIELD' },
        mandatory,
        mandatoryTargeting,
        defaultValueEnabled: false,
        defaultValue: null,
        readOnly,
        readOnlyTargeting,
        isPricingField: true, // Set isPricingField to true
        packsConfiguration: true, // Packs configuration is enabled
      });

      expect(result).toEqual(
        expect.arrayContaining([
          {
            targetType: 'PACKS_CONFIGURATION',
            enabled: true,
            rules: EMPTY_ARRAY,
          },
        ])
      );
    });

    it('should not include packs configuration in target rules when isPricingField is false', () => {
      const mandatory = true;
      const readOnly = false;
      const mandatoryTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const readOnlyTargeting = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['USED'],
        },
      ];

      const result = getTargetRulesPayload({
        type: 'CUSTOM',
        fieldData: { key: 'PRICING_CUSTOM_FIELD' },
        mandatory,
        mandatoryTargeting,
        defaultValueEnabled: false,
        defaultValue: null,
        readOnly,
        readOnlyTargeting,
        isPricingField: false, // Set isPricingField to false
      });

      expect(result).not.toEqual(
        expect.arrayContaining([
          {
            targetType: 'PACKS_CONFIGURATION',
            enabled: false,
            rules: EMPTY_ARRAY,
          },
        ])
      );
    });
  });

  describe('Test getOptionsPayload', () => {
    const customFieldValues = [{ name: 'A' }];
    const sourceOptions = [{ name: 'B' }];
    const customOptions = [{ name: 'C' }];

    it('should get options payload from customOptions as default is Field is neither SOURCE nor FIELDS_WITH_CUSTOM_OPTIONS', () => {
      const result = getOptionsPayload({
        fieldData: { key: 'YEAR' },
        customFieldValues,
        sourceOptions,
        customOptions,
      });
      expect(result).toEqual(customOptions);
    });

    it('should get options payload from sourceOptions is Field is SOURCE', () => {
      const result = getOptionsPayload({
        fieldData: { key: 'SOURCE' },
        customFieldValues,
        sourceOptions,
        customOptions,
      });
      expect(result).toEqual(sourceOptions);
    });

    it('should get options payload from customOptions is Field is FIELDS_WITH_CUSTOM_OPTIONS', () => {
      const result = getOptionsPayload({
        fieldData: { key: 'LOCATION_CODE' },
        customFieldValues,
        sourceOptions,
        customOptions,
      });
      expect(result).toEqual(customOptions);
    });
  });

  describe('Test getRequestPayload', () => {
    it('should generate request payload for a general system-defined field', () => {
      const mockFormValues = {
        type: 'GENERAL',
        name: 'YEAR',
        description: 'Field description',
        mandatory: true,
        readOnly: false,
        mandatoryTargeting: EMPTY_ARRAY,
        readOnlyTargeting: EMPTY_ARRAY,
        customFieldType: 'SINGLE_SELECT',
        customFieldValues: EMPTY_ARRAY,
        defaultValueEnabled: true,
        defaultValue: ['2023'],
        leadsVisibility: false,
        dealsBuildVisibility: false,
        dealsEditVisibility: false,
        mobileInventoryVisibility: false,
        mobileReceivingVisibility: false,
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        languages: 'en_GB',
      };

      const result = getRequestPayload({ fieldData: { id: '5_YEAR', key: 'YEAR' }, formValues: mockFormValues });

      expect(result).toEqual({
        id: '5_YEAR',
        key: 'YEAR',
        description: 'Field description',
        fieldType: 'SINGLE_SELECT',
        options: [],
        targetRules: [
          { targetType: 'MANDATORY', enabled: true, rules: EMPTY_ARRAY },
          { targetType: 'READ_ONLY', enabled: false, rules: EMPTY_ARRAY },
          {
            targetType: 'DEFAULT_VALUE',
            enabled: true,
            rules: EMPTY_ARRAY,
            defaultValue: ['2023'],
          },
        ],
        applicationVisibilityMap: {
          DEALS_BUILD: false,
          DEALS_EDIT: false,
          MOBILE_INVENTORY: false,
          MOBILE_RECEIVING: false,
          LEADS: false,
        },
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        readOnly: false,
        languages: 'en_GB',
      });
    });

    it('should generate request payload for a custom general field', () => {
      const mockFormValues = {
        type: 'CUSTOM',
        name: 'Test Field',
        description: 'Field description 1',
        mandatory: true,
        mandatoryTargeting: EMPTY_ARRAY,
        readOnlyTargeting: EMPTY_ARRAY,
        customFieldType: 'SINGLE_SELECT',
        customFieldValues: [{ name: 'Option 1' }, { name: 'Option 2' }],
        leadsVisibility: false,
        dealsBuildVisibility: false,
        dealsEditVisibility: false,
        mobileInventoryVisibility: false,
        mobileReceivingVisibility: false,
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        readOnly: false,
        languages: 'en_GB',
      };

      const result = getRequestPayload({ fieldData: EMPTY_OBJECT, formValues: mockFormValues });

      expect(result).toEqual({
        assetType: 'VI',
        vehicleType: 'AUTOMOBILE',
        assetSubType: 'CUSTOM_FIELDS_GENERAL',
        name: 'Test Field',
        description: 'Field description 1',
        fieldType: 'SINGLE_SELECT',
        options: [{ name: 'Option 1' }, { name: 'Option 2' }],
        targetRules: [
          { targetType: 'MANDATORY', enabled: true, rules: EMPTY_ARRAY },
          { targetType: 'READ_ONLY', enabled: false, rules: EMPTY_ARRAY },
        ],
        applicationVisibilityMap: {
          DEALS_BUILD: false,
          DEALS_EDIT: false,
          MOBILE_INVENTORY: false,
          MOBILE_RECEIVING: false,
          LEADS: false,
        },
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        readOnly: false,
        languages: 'en_GB',
      });
    });

    it('should generate request payload for a custom pricing field', () => {
      const mockFormValues = {
        type: 'CUSTOM',
        name: 'Test Pricing Field',
        description: 'Custom Field description 1',
        mandatory: true,
        mandatoryTargeting: EMPTY_ARRAY,
        readOnlyTargeting: EMPTY_ARRAY,
        customFieldType: 'PRICE_OR_COST',
        customFieldValues: [],
        leadsVisibility: false,
        dealsBuildVisibility: false,
        dealsEditVisibility: false,
        mobileInventoryVisibility: false,
        mobileReceivingVisibility: false,
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        packsConfiguration: true, // PACKS Configuration Checkbox
        readOnly: false,
        languages: 'en_GB',
      };

      const result = getRequestPayload({ fieldData: EMPTY_OBJECT, formValues: mockFormValues, isPricingField: true });

      expect(result).toEqual({
        assetType: 'VI',
        vehicleType: 'AUTOMOBILE',
        assetSubType: 'CUSTOM_FIELDS_PRICING',
        name: 'Test Pricing Field',
        description: 'Custom Field description 1',
        fieldType: 'PRICE_OR_COST',
        options: [],
        targetRules: [
          { targetType: 'MANDATORY', enabled: true, rules: EMPTY_ARRAY },
          { targetType: 'READ_ONLY', enabled: false, rules: EMPTY_ARRAY },
          { targetType: 'PACKS_CONFIGURATION', enabled: true, rules: EMPTY_ARRAY },
        ],
        applicationVisibilityMap: {
          DEALS_BUILD: false,
          DEALS_EDIT: false,
          MOBILE_INVENTORY: false,
          MOBILE_RECEIVING: false,
          LEADS: false,
        },
        includeInMobileInventory: false,
        includeInMobileReceiving: false,
        readOnly: false,
        languages: 'en_GB',
      });
    });

    it('should handle nullish values', () => {
      const result = getRequestPayload({});
      expect(result).toEqual({
        description: undefined,
        fieldType: undefined,
        languages: undefined,
        options: [],
        targetRules: [
          { targetType: 'MANDATORY', enabled: undefined, rules: undefined },
          { targetType: 'READ_ONLY', enabled: undefined, rules: undefined },
        ],
        applicationVisibilityMap: {
          DEALS_BUILD: undefined,
          DEALS_EDIT: undefined,
          MOBILE_INVENTORY: undefined,
          MOBILE_RECEIVING: undefined,
          LEADS: undefined,
        },
        includeInMobileInventory: undefined,
        includeInMobileReceiving: undefined,
        readOnly: undefined,
      });
    });
  });

  describe('Test getInventoryUpdateFieldPayload', () => {
    it('should get inventory update field payload without id key', () => {
      const modifiedOptions = [
        { id: '1', oldValue: 'A', newValue: 'A1' },
        { id: '2', oldValue: 'B', newValue: 'B2' },
      ];
      const result = getInventoryUpdateFieldPayload(modifiedOptions);
      expect(result).toEqual([
        { oldValue: 'A', newValue: 'A1' },
        { oldValue: 'B', newValue: 'B2' },
      ]);
    });
  });

  describe('Test getAddFieldToSectionPayload', () => {
    it('should get add field to section payload', () => {
      const section = { id: '1', name: 'Basic Details', fields: ['5_YEAR', '5_MAKE'] };
      const fieldId = '5_MODEL';
      const result = getAddFieldToSectionPayload({ section, fieldId });
      expect(result.fields).toEqual(['5_YEAR', '5_MAKE', '5_MODEL']);
    });

    it('should handle nullish values', () => {
      const fieldId = '5_MODEL';
      const result = getAddFieldToSectionPayload({ fieldId });
      expect(result).toEqual({ fields: ['5_MODEL'] });
    });
  });
});
