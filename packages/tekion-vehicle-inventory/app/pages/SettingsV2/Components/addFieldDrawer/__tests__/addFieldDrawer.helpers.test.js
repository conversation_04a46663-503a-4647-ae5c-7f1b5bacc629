import * as DealerProgramUtils from 'tbase/utils/sales/dealerProgram.utils';
import { FIELD_TYPES_KEY } from 'twidgets/constants/vi.constants';

import { FETCH_FIELDS } from 'constants/mockData';
import { getGeneralFieldsSelector, getPricingFieldsSelector } from 'selectors/settings.fields';

import {
  getTargetTypeData,
  getCustomFieldTypeOptions,
  getFieldNameOptions,
  getTargetingParameters,
  getApplicableFieldsToBeAdded,
  getAllFieldNamesList,
  getCustomOptionsWithDefaultRows,
  getReadOnlyValue,
  shouldShowReadOnlyParams,
  getValidTabFieldsWithoutTheCurrentField,
} from '../addFieldDrawer.helpers';
import { FETCH_SETUP } from '../__mocks__/fetchSetup.mock';

const mockAllSections = FETCH_SETUP;
const mockAllFields = FETCH_FIELDS;

const mockViState = { settings: { fields: mockAllFields } };
const mockGeneralFields = getGeneralFieldsSelector(mockViState);
const mockPricingFields = getPricingFieldsSelector(mockViState);

describe('Test AddFieldDrawer Helper Functions', () => {
  describe('Test getTargetTypeData', () => {
    it('should return correct targetType data when targetType is found', () => {
      const mockRules = [
        {
          parameter: 'stockType',
          operator: 'INCLUDE',
          values: ['NEW'],
        },
      ];
      const targetRules = [
        {
          targetType: 'MANDATORY',
          enabled: true,
          rules: mockRules,
          defaultValue: '2022',
        },
      ];
      const targetType = 'MANDATORY';
      const result = getTargetTypeData(targetRules, targetType);

      expect(result).toEqual({
        enabled: true,
        rules: mockRules,
        defaultValue: '2022',
      });
    });

    it('should return default data when targetType is not found', () => {
      const targetRules = [{ targetType: 'MANDATORY', enabled: true, rules: [] }];
      const targetType = 'otherTargetType';
      const result = getTargetTypeData(targetRules, targetType);

      expect(result).toEqual({
        enabled: false,
        rules: [],
        defaultValue: undefined,
      });
    });
  });

  describe('Test getCustomFieldTypeOptions', () => {
    it('should return an array of defined custom field type options', () => {
      const result = getCustomFieldTypeOptions();

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(5);
      expect(result).toEqual([
        { label: 'Single Select', value: 'SINGLE_SELECT' },
        { label: 'Multi Select', value: 'MULTI_SELECT' },
        { label: 'Free Text', value: 'FREE_TEXT' },
        { label: 'Label', value: 'LABEL' },
        { label: 'Date', value: 'DATE' },
      ]);
    });
  });

  describe('Test getFieldNameOptions', () => {
    it('should map fields to label-value pairs with data property', () => {
      const fields = [
        { name: 'Field1', key: 'field1Key' },
        { name: 'Field2', key: 'field2Key' },
      ];
      const result = getFieldNameOptions(fields);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result).toContainEqual({ label: 'Field1', value: 'field1Key', data: fields[0] });
      expect(result).toContainEqual({ label: 'Field2', value: 'field2Key', data: fields[1] });
    });

    it('should return an empty array if no fields are provided', () => {
      const result = getFieldNameOptions();
      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(0);
    });
  });

  describe('Test getTargetingParameters', () => {
    it('should return targeting parameters with SOURCE if shouldShowSources is false', () => {
      const fieldData = { key: 'YEAR' };
      const result = getTargetingParameters(fieldData);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toContain('stockType');
      expect(result).toContain('stockSubType');
      expect(result).toContain('source');
    });

    it('should return targeting parameters without SOURCE if shouldShowSources is true', () => {
      const fieldData = { key: 'SOURCE' };
      const result = getTargetingParameters(fieldData);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toContain('stockType');
      expect(result).toContain('stockSubType');
      expect(result).not.toContain('source');
    });
  });

  describe('Test getApplicableFieldsToBeAdded', () => {
    it('should return applicable General fields to be added', () => {
      // In mockAllSections EXTERIOR_COLOR & SOURCE field is not present in any section,
      // which should be asserted to be present in result
      const isPricingField = false;
      const result = getApplicableFieldsToBeAdded({
        allSections: mockAllSections,
        isPricingField,
        generalFields: mockGeneralFields,
        pricingFields: mockPricingFields,
      });

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(2);
      expect(result[0]).toHaveProperty('key', 'EXTERIOR_COLOR');
      expect(result[1]).toHaveProperty('key', 'SOURCE');
    });

    it('should return applicable Pricing fields to be added', () => {
      // In mockAllSections MSRP field is not present in any section,
      // which should be asserted to be present in result
      const isPricingField = true;
      const result = getApplicableFieldsToBeAdded({
        allSections: mockAllSections,
        isPricingField,
        generalFields: mockGeneralFields,
        pricingFields: mockPricingFields,
      });

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(1);
      expect(result[0]).toHaveProperty('key', 'MSRP');
    });
  });

  describe('Test getAllFieldNamesList', () => {
    it('should return a list of field names in lowercase', () => {
      const generalFields = [{ name: 'Year' }, { name: 'VIN' }];
      const pricingFields = [{ name: 'MSRP' }];
      const result = getAllFieldNamesList([...generalFields, ...pricingFields]);

      expect(Array.isArray(result)).toBe(true);
      expect(result).toHaveLength(3);
      expect(result).toContain('year');
      expect(result).toContain('vin');
      expect(result).toContain('msrp');
    });
  });
});

describe('Test getCustomOptionsWithDefaultRows helper', () => {
  it('should return an array with a default row if options are empty', () => {
    const options = [];
    const result = getCustomOptionsWithDefaultRows(options);
    expect(result).toHaveLength(1);
    expect(result[0]).toHaveProperty('id');
    expect(result[0]).toHaveProperty('name', '');
    expect(result[0]).toHaveProperty('isNewRow', true);
  });

  it('should return the same array if options are not empty', () => {
    const options = [
      { id: '1', name: 'Option 1', isNewRow: false },
      { id: '2', name: 'Option 2', isNewRow: false },
    ];
    const result = getCustomOptionsWithDefaultRows(options);
    expect(result).toEqual(options);
  });

  it('should return an array with a default row if options are null', () => {
    const options = null;
    const result = getCustomOptionsWithDefaultRows(options);
    expect(result).toHaveLength(1);
    expect(result[0]).toHaveProperty('id');
    expect(result[0]).toHaveProperty('name', '');
    expect(result[0]).toHaveProperty('isNewRow', true);
  });
});

describe('Test getReadOnlyValue function', () => {
  test('should returns true when formEditable is truthy and readOnly is truthy', () => {
    const formEditable = true;
    const readOnly = true;
    const result = getReadOnlyValue(formEditable, readOnly);
    expect(result).toBe(true);
  });

  test('should returns true when formEditable is falsy and readOnly is falsy', () => {
    const formEditable = false;
    const readOnly = false;
    const result = getReadOnlyValue(formEditable, readOnly);
    expect(result).toBe(true);
  });

  test('should returns false when formEditable is undefined and readOnly is falsy', () => {
    const formEditable = undefined;
    const readOnly = false;
    const result = getReadOnlyValue(formEditable, readOnly);
    expect(result).toBe(false);
  });
});

describe('getCustomFieldTypeOptions', () => {
  let isInchcapeOrRRGSpy;

  beforeEach(() => {
    isInchcapeOrRRGSpy = jest.spyOn(DealerProgramUtils, 'isInchcapeOrRRG');
  });

  it('should return custom field type options for default environment', () => {
    isInchcapeOrRRGSpy.mockReturnValue(false);
    const fieldTypeOptions = getCustomFieldTypeOptions();
    expect(fieldTypeOptions).toEqual([
      { label: __('Single Select'), value: FIELD_TYPES_KEY.SINGLE_SELECT },
      { label: __('Multi Select'), value: FIELD_TYPES_KEY.MULTI_SELECT },
      { label: __('Free Text'), value: FIELD_TYPES_KEY.FREE_TEXT },
      { label: __('Label'), value: FIELD_TYPES_KEY.LABEL },
      { label: __('Date'), value: FIELD_TYPES_KEY.DATE },
    ]);
    expect(fieldTypeOptions).toHaveLength(5);
  });

  it('should return custom field type options for Inchcape or RRG environment', () => {
    isInchcapeOrRRGSpy.mockReturnValue(true);
    const fieldTypeOptions = getCustomFieldTypeOptions();
    expect(fieldTypeOptions).toEqual([
      { label: __('Single Select'), value: FIELD_TYPES_KEY.SINGLE_SELECT },
      { label: __('Multi Select'), value: FIELD_TYPES_KEY.MULTI_SELECT },
      { label: __('Free Text'), value: FIELD_TYPES_KEY.FREE_TEXT },
      { label: __('Label'), value: FIELD_TYPES_KEY.LABEL },
      { label: __('Date'), value: FIELD_TYPES_KEY.DATE },
      { label: __('Yes/No'), value: FIELD_TYPES_KEY.BOOLEAN },
    ]);
    expect(fieldTypeOptions).toHaveLength(6); // Additional BOOLEAN field type
  });
});

describe('Test shouldShowReadOnlyParams', () => {
  test('returns true when readOnly is true and formEditable is null', () => {
    expect(shouldShowReadOnlyParams(true, null)).toBe(true);
  });

  test('returns true when readOnly is true and formEditable is true', () => {
    expect(shouldShowReadOnlyParams(true, true)).toBe(true);
  });

  test('returns false when readOnly is false, regardless of formEditable value', () => {
    expect(shouldShowReadOnlyParams(false, true)).toBe(false);
    expect(shouldShowReadOnlyParams(false, null)).toBe(false);
    expect(shouldShowReadOnlyParams(false, false)).toBe(false);
  });

  test('returns false when readOnly is true and formEditable is false', () => {
    expect(shouldShowReadOnlyParams(true, false)).toBe(false);
  });
});

describe('Test getValidTabFieldsWithoutTheCurrentField', () => {
  const pricingFields = [
    { id: 1, name: 'Selling Price' },
    { id: 2, name: 'Base Retail' },
  ];

  const customPricingFields = [{ id: 3, name: 'Custom Price' }];

  const generalFields = [
    { id: 4, name: 'VIN' },
    { id: 5, name: 'Make' },
    { id: 6, name: 'Model' },
  ];

  const customGeneralFields = [{ id: 7, name: 'Range' }];

  test('should remove the field with matching id when isPricingField is true', () => {
    const fieldData = { id: 2 };
    const isPricingField = true;

    const result = getValidTabFieldsWithoutTheCurrentField({
      fieldData,
      isPricingField,
      pricingFields,
      customPricingFields,
      generalFields,
      customGeneralFields,
    });

    expect(result).toEqual([
      { id: 1, name: 'Selling Price' },
      { id: 3, name: 'Custom Price' },
    ]);
  });

  test('should remove the field with matching id when isPricingField is false', () => {
    const fieldData = { id: 5 };
    const isPricingField = false;

    const result = getValidTabFieldsWithoutTheCurrentField({
      fieldData,
      isPricingField,
      pricingFields,
      customPricingFields,
      generalFields,
      customGeneralFields,
    });

    expect(result).toEqual([
      { id: 4, name: 'VIN' },
      { id: 6, name: 'Model' },
      { id: 7, name: 'Range' },
    ]);
  });

  test('should return all fields when fieldData.id does not match any', () => {
    const fieldData = { id: 99 };
    const isPricingField = true;

    const result = getValidTabFieldsWithoutTheCurrentField({
      fieldData,
      isPricingField,
      pricingFields,
      customPricingFields,
      generalFields,
      customGeneralFields,
    });

    expect(result).toEqual([...pricingFields, ...customPricingFields]);
  });

  test('should return all fields when fieldData is undefined', () => {
    const fieldData = undefined;
    const isPricingField = false;

    const result = getValidTabFieldsWithoutTheCurrentField({
      fieldData,
      isPricingField,
      pricingFields,
      customPricingFields,
      generalFields,
      customGeneralFields,
    });

    expect(result).toEqual([...generalFields, ...customGeneralFields]);
  });
});
