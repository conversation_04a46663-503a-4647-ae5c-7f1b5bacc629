import _property from 'lodash/property';

const key = _property('key');
const name = _property('name');
const description = _property('description');
const options = _property('options');
const fieldType = _property('fieldType');
const targetRules = _property('targetRules');
const systemDefined = _property('systemDefined');
const languages = _property('languages');
const formEditable = _property('formEditable');
const readOnly = _property('readOnly');
const includeInMobileReceiving = _property('includeInMobileReceiving');
const includeInMobileInventory = _property('includeInMobileInventory');
const leadsVisibility =  _property('applicationVisibilityMap.LEADS');
const dealsBuildVisibility = _property('applicationVisibilityMap.DEALS_BUILD');
const dealsEditVisibility = _property('applicationVisibilityMap.DEALS_EDIT');
const mobileInventoryVisibility = _property('applicationVisibilityMap.MOBILE_INVENTORY');
const mobileReceivingVisibility = _property('applicationVisibilityMap.MOBILE_RECEIVING');
const isMandatoryBySystem = _property('mandatory');

export default {
  key,
  name,
  description,
  options,
  fieldType,
  targetRules,
  systemDefined,
  languages,
  formEditable,
  readOnly,
  includeInMobileReceiving,
  includeInMobileInventory,
  leadsVisibility,
  dealsBuildVisibility,
  dealsEditVisibility,
  mobileInventoryVisibility,
  mobileReceivingVisibility,
  isMandatoryBySystem,
};
