import PROGRAM_CONFIG from 'constants/programConfig';

import {
  getPricingTabFilterTypes,
  shouldFetchConfigurationTypeOptions,
  shouldFetchConfigurationTypeOnFieldDelete,
} from '../pricing.helpers';
import { TARGET_TYPES, FIELD_TYPES, MODES } from '../../addFieldDrawer/addFieldDrawer.constants';

jest.mock('constants/programConfig', () => ({
  ...jest.requireActual('constants/programConfig'),
  shouldShowSetupChangesForBuildVehicle: jest.fn(),
  shouldShowMobileOptions: jest.fn(),
}));

describe('Test getPricingTabFilterTypes', () => {
  it('should return common filters with usage module targeting filter when "BUILD_VEHICLE_V2_ENABLED" DP is enabled', () => {
    PROGRAM_CONFIG.shouldShowSetupChangesForBuildVehicle.mockReturnValue(true);
    PROGRAM_CONFIG.shouldShowMobileOptions.mockReturnValue(true);
    const result = getPricingTabFilterTypes();
    expect(result).toEqual([
      {
        id: 'FIELD_TYPE',
        name: 'Field Type',
        type: 'MULTI_SELECT',
        additional: {
          options: [
            { label: 'Pricing', value: 'PRICING' },
            { label: 'Custom', value: 'CUSTOM_FIELDS_PRICING' },
          ],
          operators: [{ id: 'IN', name: 'In' }],
        },
      },
      {
        id: 'MANDATORY',
        name: 'Mandatory',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
      {
        id: 'READ_ONLY',
        name: 'Read-only',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
      {
        id: 'USAGE_MODULE_TARGETING',
        name: 'Usage Module Targeting',
        type: 'MULTI_SELECT',
        additional: {
          options: [
            {
              id: 'leadsVisibility',
              value: 'LEADS',
              label: 'Leads',
              fieldClassName: 'm-b-8 m-r-8',
            },
            {
              id: 'dealsBuildVisibility',
              value: 'DEALS_BUILD',
              label: 'Deals (Build)',
              fieldClassName: 'm-b-8',
            },
            {
              id: 'dealsEditVisibility',
              value: 'DEALS_EDIT',
              label: 'Deals (Edit)',
              fieldClassName: 'm-b-8',
            },
            {
              id: 'mobileInventoryVisibility',
              value: 'MOBILE_INVENTORY',
              label: 'Mobile (Inventory)',
              fieldClassName: 'm-b-8',
            },
            {
              id: 'mobileReceivingVisibility',
              value: 'MOBILE_RECEIVING',
              label: 'Mobile (Receiving)',
              fieldClassName: 'm-b-8 m-l-16',
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
    ]);
  });

  it('should return common filters with mobile receiving and inventory filters when "BUILD_VEHICLE_V2_ENABLED" DP is disabled', () => {
    PROGRAM_CONFIG.shouldShowSetupChangesForBuildVehicle.mockReturnValue(false);
    const result = getPricingTabFilterTypes();
    expect(result).toEqual([
      {
        id: 'FIELD_TYPE',
        name: 'Field Type',
        type: 'MULTI_SELECT',
        additional: {
          options: [
            { label: 'Pricing', value: 'PRICING' },
            { label: 'Custom', value: 'CUSTOM_FIELDS_PRICING' },
          ],
          operators: [{ id: 'IN', name: 'In' }],
        },
      },
      {
        id: 'MANDATORY',
        name: 'Mandatory',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
      {
        id: 'READ_ONLY',
        name: 'Read-only',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
      {
        id: 'MOBILE_RECEIVING',
        name: 'Mobile Receiving',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
      {
        id: 'MOBILE_INVENTORY',
        name: 'Mobile Inventory',
        type: 'SINGLE_SELECT',
        additional: {
          options: [
            {
              label: 'Yes',
              value: true,
            },
            {
              label: 'No',
              value: false,
            },
          ],
          operators: [
            {
              id: 'IN',
              name: 'In',
            },
          ],
        },
      },
    ]);
  });
});

describe('Test shouldFetchConfigurationTypeOptions', () => {
  it('should return true when adding a custom pricing field with packs configuration', () => {
    const params = {
      mode: MODES.ADD,
      isPricingField: true,
      formValues: {
        packsConfiguration: true,
        type: FIELD_TYPES.CUSTOM,
      },
    };

    expect(shouldFetchConfigurationTypeOptions(params)).toBe(true);
  });

  it('should return true when editing a custom pricing field', () => {
    const params = {
      mode: MODES.EDIT,
      isPricingField: true,
      formValues: {
        type: FIELD_TYPES.CUSTOM,
      },
    };

    expect(shouldFetchConfigurationTypeOptions(params)).toBe(true);
  });

  it('should return false when not a pricing field', () => {
    const params = {
      mode: MODES.ADD,
      isPricingField: false,
      formValues: {
        packsConfiguration: true,
        type: FIELD_TYPES.CUSTOM,
      },
    };

    expect(shouldFetchConfigurationTypeOptions(params)).toBe(false);
  });

  it('should return false when field type is not CUSTOM', () => {
    const params = {
      mode: MODES.ADD,
      isPricingField: true,
      formValues: {
        packsConfiguration: true,
        type: FIELD_TYPES.GENERAL,
      },
    };

    expect(shouldFetchConfigurationTypeOptions(params)).toBe(false);
  });

  it('should return false when in ADD mode without packsConfiguration', () => {
    const params = {
      mode: MODES.ADD,
      isPricingField: true,
      formValues: {
        type: FIELD_TYPES.CUSTOM,
      },
    };

    expect(shouldFetchConfigurationTypeOptions(params)).toBe(false);
  });
});

describe('Test shouldFetchConfigurationTypeOnFieldDelete', () => {
  it('should return true when field is custom pricing field and packs configuration is enabled', () => {
    const field = {
      targetRules: [
        {
          targetType: TARGET_TYPES.PACKS_CONFIGURATION,
          enabled: true,
        },
      ],
    };

    expect(shouldFetchConfigurationTypeOnFieldDelete(field, 'CUSTOM_FIELDS_PRICING')).toBe(true);
  });

  it('should return false when field is not a custom pricing field', () => {
    const field = {
      targetRules: [
        {
          targetType: TARGET_TYPES.PACKS_CONFIGURATION,
          enabled: true,
        },
      ],
    };

    expect(shouldFetchConfigurationTypeOnFieldDelete(field, 'SOME_OTHER_TYPE')).toBe(false);
  });

  it('should return false when packs configuration is disabled', () => {
    const field = {
      targetRules: [
        {
          targetType: TARGET_TYPES.PACKS_CONFIGURATION,
          enabled: false,
        },
      ],
    };

    expect(shouldFetchConfigurationTypeOnFieldDelete(field, 'CUSTOM_FIELDS_PRICING')).toBe(false);
  });

  it('should handle undefined field', () => {
    expect(shouldFetchConfigurationTypeOnFieldDelete(undefined, 'CUSTOM_FIELDS_PRICING')).toBe(false);
  });

  it('should handle empty targetRules', () => {
    const field = {
      targetRules: [],
    };

    expect(shouldFetchConfigurationTypeOnFieldDelete(field, 'CUSTOM_FIELDS_PRICING')).toBe(false);
  });
});
