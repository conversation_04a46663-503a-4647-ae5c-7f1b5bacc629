import { compose } from 'recompose';
import produce from 'immer';

import _compact from 'lodash/compact';
import _filter from 'lodash/filter';
import _findIndex from 'lodash/findIndex';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _indexOf from 'lodash/indexOf';
import _isEmpty from 'lodash/isEmpty';
import _map from 'lodash/map';
import _orderBy from 'lodash/orderBy';
import _reduce from 'lodash/reduce';
import _set from 'lodash/set';
import _isString from 'lodash/isString';
import _isUndefined from 'lodash/isUndefined';
import _some from 'lodash/some';
import _startsWith from 'lodash/startsWith';
import _values from 'lodash/values';

import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';
import { CERTIFICATION_STATUS_CONFIG } from 'tbase/constants/vehicleInventory/certificationStatus';
import { tget } from 'tbase/utils/general';
import removeElementAtIndex from 'tbase/utils/removeElementAtIndex';

import DealerConfigReader from 'readers/dealerConfig';
import { MANDATORY_STOCK_RULE_TYPES } from 'constants/constantEnum';
import PROGRAM_CONFIG from 'constants/programConfig';

import {
  FIELD_TARGET_TYPES,
  PARAMETER_OPTIONS_VALUE,
  USAGE_MODULE_TARGETING_IDS,
  USAGE_MODULE_TARGETING_VALUES,
} from './settings.constants';

const checkIfErrorPresent = compose(_isEmpty, _compact);

// used for validation of mandatory field targeting table
export const validateTargetingTableFields = mandatoryTableData => {
  const errors = _map(mandatoryTableData, rows => {
    const { parameter, values } = rows;
    if (!_isEmpty(parameter) && _isEmpty(values)) {
      return { values: __('Values are required') };
    }
    return undefined;
  });
  const isValid = checkIfErrorPresent(errors);
  return { isValid, errors };
};

export const getMandatoryTargetingIndex = field =>
  _findIndex(_get(field, ['targetRules']), ({ targetType }) => targetType === FIELD_TARGET_TYPES.MANDATORY);

const getStockTypeToSubTypeMap = vehicleTypeSettings => {
  const enabledStockTypes = _filter(vehicleTypeSettings, 'enabled');
  return _reduce(
    enabledStockTypes,
    (acc, value) => {
      const stockType = _get(value, 'type');
      const stockSubTypes = _get(value, 'subTypes');
      const nonDeletedSubTypes = _filter(stockSubTypes, ({ deleted }) => !deleted);
      const subTypeIds = _map(nonDeletedSubTypes, 'id');
      return {
        ...acc,
        [stockType]: subTypeIds,
      };
    },
    {}
  );
};

export const handleStockTypeChange = (targetingData, { vehicleTypeSettings }) => {
  const stockTypeToSubTypes = getStockTypeToSubTypeMap(vehicleTypeSettings);
  const stockTypeTargetingIndex = _findIndex(targetingData, { parameter: [PARAMETER_OPTIONS_VALUE.STOCK_TYPE] });
  const stockSubTypeTargetingIndex = _findIndex(targetingData, { parameter: [PARAMETER_OPTIONS_VALUE.STOCK_SUBTYPE] });
  if (stockTypeTargetingIndex !== -1 && stockSubTypeTargetingIndex !== -1) {
    const selectedStockTypes = _get(targetingData, [stockTypeTargetingIndex, 'values']);
    const selectedStockSubTypes = _get(targetingData, [stockSubTypeTargetingIndex, 'values']);
    const possibleStockSubTypes = _reduce(
      stockTypeToSubTypes,
      (acc, option, key) => {
        if (_includes(selectedStockTypes, key)) {
          return [...acc, ...option];
        }
        return acc;
      },
      []
    );

    return produce(targetingData, draft => {
      _set(
        draft,
        [stockSubTypeTargetingIndex, 'values'],
        _filter(selectedStockSubTypes, id => _includes(possibleStockSubTypes, id))
      );
    });
  }
  return targetingData;
};

export const handleStockTypeDelete = (targetingData, error) => {
  const stockSubTypeTargetingIndex = _findIndex(targetingData, { parameter: [PARAMETER_OPTIONS_VALUE.STOCK_SUBTYPE] });
  const updatedTargeting = removeElementAtIndex(targetingData, stockSubTypeTargetingIndex);
  const updatedErrors = removeElementAtIndex(error, stockSubTypeTargetingIndex);
  return {
    mandatoryTargeting: updatedTargeting,
    error: updatedErrors,
  };
};

export const checkVehicleSubStatusesErrors = customStatuses => {
  const statuses = _map(customStatuses, 'status');
  const errors = _map(customStatuses, (subStatus, index) => {
    const { key, status } = subStatus;

    // NOTE: Not required once Translations from BE are done
    if (_startsWith(key, 'customStatus__')) return undefined;

    if (_isEmpty(status)) {
      return { status: __('Status is required') };
    }
    const duplicateIndex = _indexOf(statuses, status);
    if (duplicateIndex < index) {
      return { status: __('Status already Exists') };
    }
    return undefined;
  });
  const isValid = checkIfErrorPresent(errors);
  return { isValid, errors };
};

const getModifiedVehicleTypes = vehicleTypes =>
  _map(vehicleTypes, typeObj =>
    produce(typeObj, draft => {
      draft.subTypes = _map(draft?.subTypes, type =>
        produce(type, draftState => {
          _set(draftState, 'name', type?.languages?.locale?.['en_US']?.name || type?.name);
        })
      );
    })
  );

const getModifiedCustomStatuses = customStatuses =>
  _map(customStatuses, statusObj =>
    produce(statusObj, draft => {
      _set(draft, 'status', statusObj?.languages?.locale?.['en_US']?.status || statusObj?.status);
    })
  );

export const setDefaultMultilingualValues = settingsPayload =>
  produce(settingsPayload, draft => {
    draft.typeSetting.vehicleTypes = getModifiedVehicleTypes(settingsPayload?.typeSetting?.vehicleTypes);
    draft.otherSettings.customStatuses = getModifiedCustomStatuses(settingsPayload?.otherSettings?.customStatuses);
  });

export const getMultilingualFieldLanguages = ({ subTypes, nestingPath }) => {
  const dealerLanguage = DealerConfigReader.getDealerLanguage();
  const languages = tget(subTypes, [nestingPath, 'languages']);
  return {
    dealerLanguage,
    languages,
  };
};

export const getIsErrorInSyndication = (syndicationSetupErrors, accumulator = false) =>
  _reduce(
    syndicationSetupErrors,
    (acc, val) => {
      if (_isString(val)) return true;
      if (_isUndefined(val)) return acc;
      return getIsErrorInSyndication(val, acc);
    },
    accumulator
  );

export const getErrorInWarranty = warrantyData =>
  _some(warrantyData, option => !_isEmpty(_compact(_values(option.error))));

export const getErrorInCustomValuesTable = data => {
  const errors = _map(data, rows => {
    const { name } = rows;
    if (_isEmpty(name)) {
      return { name: __('Value is required') };
    }
    return null;
  });
  const isValid = checkIfErrorPresent(errors);
  return { isValid, errors };
};

export const getTabSpecificSections = (tabType, allSections) =>
  _orderBy(
    _filter(allSections, ({ assetSubType }) => assetSubType === tabType),
    'order',
    'asc'
  );

export const getErrorInStockRulesConditions = conditions =>
  _some(conditions, condition => {
    const { stockRules = EMPTY_ARRAY } = condition || EMPTY_OBJECT;
    return _some(stockRules, rule => _includes(MANDATORY_STOCK_RULE_TYPES, rule?.type) && _isEmpty(rule?.ruleValues));
  });

export const getCurrentCertificationStatus = certificationStatusOptions =>
  _map(_filter(certificationStatusOptions, 'active'), ({ key }) => {
    const statusConfig = tget(CERTIFICATION_STATUS_CONFIG, key, EMPTY_OBJECT);
    return {
      value: key,
      label: statusConfig?.label,
    };
  });

export const getErrorInOemInvoiceSetup = oemInvoiceSetting => {
  const { vendorId, invoicingSeries } = oemInvoiceSetting || EMPTY_OBJECT;
  return !(vendorId && invoicingSeries);
};

export const getUsageModuleTargetingConfig = () => [
  {
    id: USAGE_MODULE_TARGETING_IDS.LEADS_VISIBILITY,
    value: USAGE_MODULE_TARGETING_VALUES.LEADS,
    label: __('Leads'),
    fieldClassName: 'm-b-8 m-r-8',
  },
  {
    id: USAGE_MODULE_TARGETING_IDS.DEALS_BUILD_VISIBILITY,
    value: USAGE_MODULE_TARGETING_VALUES.DEALS_BUILD,
    label: __('Deals (Build)'),
    fieldClassName: 'm-b-8',
  },
  {
    id: USAGE_MODULE_TARGETING_IDS.DEALS_EDIT_VISIBILITY,
    value: USAGE_MODULE_TARGETING_VALUES.DEALS_EDIT,
    label: __('Deals (Edit)'),
    fieldClassName: 'm-b-8',
  },
  ...(PROGRAM_CONFIG.shouldShowMobileOptions()
    ? [
        {
          id: USAGE_MODULE_TARGETING_IDS.MOBILE_INVENTORY_VISIBILITY,
          value: USAGE_MODULE_TARGETING_VALUES.VI_MOBILE_INVENTORY,
          label: __('Mobile (Inventory)'),
          fieldClassName: 'm-b-8',
        },
        {
          id: USAGE_MODULE_TARGETING_IDS.MOBILE_RECEIVING_VISIBILITY,
          value: USAGE_MODULE_TARGETING_VALUES.VI_MOBILE_RECEIVING,
          label: __('Mobile (Receiving)'),
          fieldClassName: 'm-b-8 m-l-16',
        },
      ]
    : EMPTY_ARRAY),
];
