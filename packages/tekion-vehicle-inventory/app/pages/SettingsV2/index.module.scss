@import "tstyles/component.scss";

.pageHeader {
  @include full-width;
  @include flex($flex-flow: column nowrap);
  .title {
    .editTitleIcon {
      cursor: pointer;
    }
  }
}

.pageBody {
  overflow: hidden;
  .tabBody {
    @include full-width;
    overflow: auto;
  }
}

.tabBodyPadding {
  padding: 1.6rem 2.4rem 0;
}

.row {
  @include flex($flex-flow: row nowrap, $justify-content: space-between);
}

.stockRules {
  > .header {
    @include flex($flex-flow: row nowrap, $justify-content: space-between);
    margin-bottom: 1.6rem;
    .rightContainer {
      width: 30%;
    }
  }
}

.disableSettingsEdit {
  pointer-events: none;
  opacity: 0.5;
}

.enterprisePublishChangesPopup {
  position: sticky;
}

.syndicationSetupTab {
  @include is-marginless();
  padding-right: 0 !important;
}
