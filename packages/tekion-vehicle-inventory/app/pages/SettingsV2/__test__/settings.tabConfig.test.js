import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';

import { SETUP_TAB_ENUMS } from 'constants/constantEnum';
import PROGRAM_CONFIG from 'constants/programConfig';

import { getTabsConfig } from '../settings.tabConfig';

const isViArcLiteEnabledSpy = jest.spyOn(DealerPropertyHelper, 'isViArcLiteEnabled');
const isVehicleOptionsEnabledSpy = jest.spyOn(DealerPropertyHelper, 'isVehicleOptionsEnabled');
const isVehicleProfileEnabledSpy = jest.spyOn(SalesDealerPropertyHelper, 'isVehicleProfileEnabled');

jest.mock('constants/programConfig', () => ({
  __esModule: true,
  ...jest.requireActual('constants/programConfig'),
  default: {
    ...jest.requireActual('constants/programConfig').default,
    shouldShowOemPurchaseInvoiceSetup: jest.fn(),
  },
}));

describe('Test getTabsConfig', () => {
  const defaultParams = {
    actions: { fetchHardpackMetadata: jest.fn() },
    currentLanguageId: 'en',
    errors: {},
    enterpriseView: false,
    hardpackMetadata: {},
    isFeedSyndicationEnabled: false,
    settings: {},
    updateErrorStatus: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return ArcLite tabs when ViArcLiteEnabled is true', () => {
    isViArcLiteEnabledSpy.mockReturnValue(true);

    const result = getTabsConfig(defaultParams);

    expect(result).toHaveLength(2);
    expect(result[0].key).toBe(SETUP_TAB_ENUMS.GENERAL);
    expect(result[1].key).toBe(SETUP_TAB_ENUMS.PRICING);
  });

  it('should return common tabs list when no additional DP is enabled', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    isVehicleProfileEnabledSpy.mockReturnValue(false);
    isVehicleOptionsEnabledSpy.mockReturnValue(false);

    const result = getTabsConfig(defaultParams);

    expect(result).toHaveLength(7);
    expect(result[0].key).toBe(SETUP_TAB_ENUMS.STOCK_TYPE);
    expect(result[6].key).toBe(SETUP_TAB_ENUMS.MOBILE_VI);
  });

  it('should include Dealer Add-ons tab when VehicleProfileEnabled is true', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    isVehicleProfileEnabledSpy.mockReturnValue(true);

    const result = getTabsConfig(defaultParams);

    expect(result.some(tab => tab.key === SETUP_TAB_ENUMS.DEALER_ADD_ONS)).toBe(true);
  });

  it('should include Options tab when VehicleProfileEnabled is false and VehicleOptionsEnabled is true', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    isVehicleProfileEnabledSpy.mockReturnValue(false);
    isVehicleOptionsEnabledSpy.mockReturnValue(true);

    const result = getTabsConfig(defaultParams);

    expect(result.some(tab => tab.key === SETUP_TAB_ENUMS.OPTIONS)).toBe(true);
  });

  it('should include Inventory Feed tab when isFeedSyndicationEnabled is true', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    const props = { ...defaultParams, isFeedSyndicationEnabled: true };

    const result = getTabsConfig(props);

    expect(result.some(tab => tab.key === SETUP_TAB_ENUMS.INVENTORY_FEED)).toBe(true);
  });

  it('should disable Account Setup and Stock# Rules tabs when enterpriseView is true', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    const props = { ...defaultParams, enterpriseView: true };

    const result = getTabsConfig(props);

    const accountSetupTab = result.find(tab => tab.key === SETUP_TAB_ENUMS.ACCOUNT_SETUP);
    const stockRulesTab = result.find(tab => tab.key === SETUP_TAB_ENUMS.STOCK_RULES);

    expect(accountSetupTab.shouldDisable).toBe(true);
    expect(stockRulesTab.shouldDisable).toBe(true);
  });

  it('should force render Others tab when shouldShowOemPurchaseInvoiceSetup is true and settings are not empty', () => {
    isViArcLiteEnabledSpy.mockReturnValue(false);
    PROGRAM_CONFIG.shouldShowOemPurchaseInvoiceSetup.mockReturnValue(true);
    const props = {
      ...defaultParams,
      settings: {
        otherSettings: {
          otherSettings: {
            oemPurchaseInvoiceSetting: { data: ['123'] },
          },
        },
      },
    };

    const result = getTabsConfig(props);

    const othersTab = result.find(tab => tab.key === SETUP_TAB_ENUMS.OTHERS);
    expect(othersTab.forceRender).toBe(true);
  });
});
