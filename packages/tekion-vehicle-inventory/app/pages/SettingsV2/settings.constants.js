import { VEHICLE_STATUS } from 'tbase/constants/vehicleInventory/vehicleStatus';
import COLORS from 'tstyles/exports.scss';

import { VEHICLE_KEYS } from 'constants/constants';
import { INVENTORY_STATUS } from 'constants/constantEnum';
import { EMPTY_ARRAY } from 'tbase/app.constants';

import PROGRAM_CONFIG from 'constants/programConfig';

export const SETTINGS_TOP_TAB_BAR_STYLE = {
  borderBottom: `0.1rem solid ${COLORS.platinum}`,
};

export const INFO = {
  ADDITIONAL_COST_CAPTION: __('Configure default hard pack for vehicles.'),
  ADDITIONAL_COST_V2_CAPTION: __('Saved costs configuration is non-deletable'),
  GENERAL_CAPTION: __(
    'Add, remove, or edit fields for General Section in Vehicle Inventory. Mark the fields as mandatory or non mandatory.'
  ),
  TRIM_CAPTION: __('Add and edit custom Dealer Defined fields to be added for trim details in Vehicle Inventory.'),
  PRICING_CAPTION: __(
    'Add, remove, or edit fields for Pricing in Vehicle Inventory. Mark the fields as mandatory or non mandatory.'
  ),
  SETTINGS_CAPTION: __(
    'Any Inventory settings set at the company level will be locked at store level for editing or changing.'
  ),
  VEHICLE_TYPE_HEADER_CAPTION: __('Mark a Stock type enable or disable and add sub categories for enabled stock type.'),
  STOCK_RULES_CAPTION: __(
    'Configure conditions for different vehicle types to auto populate the stock number for vehicles.'
  ),
  APPEND_DUPLICATE_STOCK_RULE: __(
    'In case of stock numbers generated through the configurations are same, append with either incrementing numbers or alphabets.'
  ),
  CF_CAPTION: __('Add and edit custom fields to be added for general details in Vehicle Inventory.'),
  SCAN: __('Filter to compare against inventory on the basis of GL Balance > 0'),
};

export const MODES = {
  ADD: 'ADD',
  EDIT: 'EDIT',
  DELETE: 'DELETE',
};

export const SCAN_CONFIG = [
  {
    field: 'glBalance',
    value: 'glBalance',
    label: __('G/L Balance > 0'),
    operator: 'GT',
    values: [0],
  },
  {
    field: 'status',
    value: 'status',
    label: __('Vehicle Status'),
    values: [INVENTORY_STATUS.STOCKED_IN, VEHICLE_STATUS.RESERVED],
  },
];

export const AGING_CALCULATION_OPTIONS = [
  {
    label: __('Received Date'),
    value: VEHICLE_KEYS.RECEIVED,
  },
  {
    label: __('Stocked In Date'),
    value: VEHICLE_KEYS.STOCKED_IN,
  },
];

export const FIELD_TARGET_TYPES = {
  MANDATORY: 'MANDATORY',
};

export const PARAMETER_OPTIONS_VALUE = {
  STOCK_TYPE: 'stockType',
  STOCK_SUBTYPE: 'stockSubType',
  SOURCE: 'source',
};

export const KEYS_TO_BE_REMOVED_FOR_FIELD_DELETE = ['targetRules'];

export const SC_VEHICLE_STATUS_FIELDS = [
  { label: __('Stocked in'), value: 'STOCKED_IN', selected: true },
  { label: __('Reserved'), value: 'RESERVED', selected: true },
  { label: __('Draft'), value: 'DRAFT', selected: false },
  { label: __('Received'), value: 'RECEIVED', selected: false },
  { label: __('On-Hold'), value: 'ON_HOLD', selected: false },
];

export const REPORT_VEHICLE_STATUSES = [
  { label: __('Stocked in'), value: 'STOCKED_IN', selected: true },
  { label: __('Reserved'), value: 'RESERVED', selected: false },
  { label: __('Draft'), value: 'DRAFT', selected: false },
  { label: __('Received'), value: 'RECEIVED', selected: false },
  { label: __('On-Hold'), value: 'ON_HOLD', selected: false },
];

export const ESTIMATED_DELIVERY_DATE = 'ESTIMATED_DELIVERY_DATE';

export const ENTERPRISE_SETUP_ASSET_TYPE = 'VISETUP';

export const ENTERPRISE_SETUP_SHARED_ASSET_TYPE = 'DEALER';

export const ENTERPRISE_SETUP_STATUS = {
  DRAFT: 'DRAFT',
  PUBLISHED: 'PUBLISHED',
};

export const ENTERPRISE_DEALER_SCREEN_MODES = {
  ADD: 'add',
  MAP_AND_PUBLISH: 'mapAndPublish',
};

export const ADDITIONAL_COST_FORM_CONTEXT_ID = 'ADDITIONAL_COST_FORM_CONTEXT_ID';

export const ERROR_RESPONSE_TYPE = 'ERROR';

export const MINIMUM_DRAG_DISTANCE_FOR_SORTING = 10;

export const OEM_INVOICE_SETUP_FORM_CONTEXT_ID = 'OEM_INVOICE_SETUP_FORM_CONTEXT_ID';

export const VEHICLE_IDENTIFICATION = 'VEHICLE_IDENTIFICATION';

export const VEHICLE_IDENTIFICATION_LABEL = __('Vehicle Identification');

export const REARRANGEMENT_DISABLED_BANNER = [
  {
    info: __(
      'Fields cannot be re-arranged as some filters are applied. To get the exact preview of the VI General page reset the filters'
    ),
    infoClassName: 'm-l-16',
    showVerticalStrip: true,
    showHeading: false,
  },
];

export const PRICING_REARRANGEMENT_DISABLED_BANNER = [
  {
    info: __(
      'Fields cannot be re-arranged as some filters are applied. To get the exact preview of the VI Pricing page reset the filters'
    ),
    infoClassName: 'm-l-16',
    showVerticalStrip: true,
    showHeading: false,
  },
];

export const USAGE_MODULE_TARGETING_IDS = {
  LEADS_VISIBILITY: 'leadsVisibility',
  DEALS_BUILD_VISIBILITY: 'dealsBuildVisibility',
  DEALS_EDIT_VISIBILITY: 'dealsEditVisibility',
  MOBILE_INVENTORY_VISIBILITY: 'mobileInventoryVisibility',
  MOBILE_RECEIVING_VISIBILITY: 'mobileReceivingVisibility',
};

export const USAGE_MODULE_TARGETING_VALUES = {
  LEADS: 'LEADS',
  DEALS_BUILD: 'DEALS_BUILD',
  DEALS_EDIT: 'DEALS_EDIT',
  VI_MOBILE_RECEIVING: 'MOBILE_RECEIVING',
  VI_MOBILE_INVENTORY: 'MOBILE_INVENTORY',
};
