import _isEmpty from 'lodash/isEmpty';
import _get from 'lodash/get';

import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import SalesDealerPropertyHelper from 'twidgets/appServices/sales/helpers/salesDealerPropertyHelper';

import { SETUP_TAB_ENUMS } from 'constants/constantEnum';
import PROGRAM_CONFIG from 'constants/programConfig';
import AccountSetup from 'pages/Settings/Components/AccountSetup';
import StockRules from 'pages/Settings/Components/StockRules';
import Other from 'pages/Settings/Components/Other';
import MobileVI from 'pages/Settings/Components/Mobile_VI';
import Options from 'pages/Settings/Components/Options';
import DealerAddOns from 'pages/Settings/Components/DealerAddOns';
import SyndicationSetup from 'pages/Settings/Components/Syndication';

import StockType from './Components/stockType';
import General from './Components/General';
import Pricing from './Components/Pricing';

import styles from './index.module.scss';

export const getTabsConfig = ({
  actions,
  currentLanguageId,
  errors,
  enterpriseView,
  hardpackMetadata,
  isFeedSyndicationEnabled,
  settings,
  updateErrorStatus,
}) => {
  if (DealerPropertyHelper.isViArcLiteEnabled()) {
    return [
      {
        key: SETUP_TAB_ENUMS.GENERAL,
        tabName: __('General'),
        Component: General,
        shouldDisable: false,
      },
      {
        key: SETUP_TAB_ENUMS.PRICING,
        tabName: __('Pricing'),
        Component: Pricing,
        shouldDisable: false,
      },
    ];
  }

  return [
    {
      key: SETUP_TAB_ENUMS.STOCK_TYPE,
      tabName: __('Stock Type'),
      Component: StockType,
      shouldDisable: false,
      className: styles.tabBodyPadding,
    },
    {
      key: SETUP_TAB_ENUMS.GENERAL,
      tabName: __('General'),
      Component: General,
      shouldDisable: false,
    },
    {
      key: SETUP_TAB_ENUMS.PRICING,
      tabName: __('Pricing'),
      Component: Pricing,
      props: { hardpackMetadata },
      shouldDisable: false,
    },
    {
      key: SETUP_TAB_ENUMS.ACCOUNT_SETUP,
      tabName: __('Account Setup'),
      Component: AccountSetup,
      props: { error: _get(errors, 'accountSetup'), fetchHardpackMetadata: actions.fetchHardpackMetadata },
      shouldDisable: enterpriseView,
      className: styles.tabBodyPadding,
    },
    {
      key: SETUP_TAB_ENUMS.STOCK_RULES,
      tabName: __('Stock# Rules'),
      Component: StockRules,
      shouldDisable: enterpriseView,
      className: styles.tabBodyPadding,
    },
    ...(SalesDealerPropertyHelper.isVehicleProfileEnabled()
      ? [
          {
            key: SETUP_TAB_ENUMS.DEALER_ADD_ONS,
            tabName: __('Dealer Add-ons'),
            Component: DealerAddOns,
            props: { errors: _get(errors, 'dealerAddOnsSetup'), currentLanguageId },
          },
        ]
      : []),
    ...(!SalesDealerPropertyHelper.isVehicleProfileEnabled() && DealerPropertyHelper.isVehicleOptionsEnabled()
      ? [
          {
            key: SETUP_TAB_ENUMS.OPTIONS,
            tabName: __('Options'),
            Component: Options,
            props: { currentLanguageId },
            className: styles.tabBodyPadding,
          },
        ]
      : []),
    {
      key: SETUP_TAB_ENUMS.OTHERS,
      tabName: __('Others'),
      Component: Other,
      props: { error: _get(errors, 'other'), updateErrorStatus },
      shouldDisable: false,
      forceRender:
        PROGRAM_CONFIG.shouldShowOemPurchaseInvoiceSetup() &&
        !_isEmpty(_get(settings, 'otherSettings.otherSettings.oemPurchaseInvoiceSetting')),
      className: styles.tabBodyPadding,
    },
    {
      key: SETUP_TAB_ENUMS.MOBILE_VI,
      tabName: __('Mobile VI'),
      Component: MobileVI,
      className: styles.tabBodyPadding,
    },
    ...(isFeedSyndicationEnabled
      ? [
          {
            key: SETUP_TAB_ENUMS.INVENTORY_FEED,
            tabName: __('Inventory Feed'),
            Component: SyndicationSetup,
            className: styles.syndicationSetupTab,
          },
        ]
      : []),
  ];
};
