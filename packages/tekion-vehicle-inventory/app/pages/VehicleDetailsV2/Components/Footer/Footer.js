import React, { useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _debounce from 'lodash/debounce';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { Content, FontIcon } from 'tcomponents/atoms';
import { TOOLTIP_PLACEMENT } from 'tcomponents/atoms/tooltip';
import SaveComponent from 'tcomponents/molecules/SaveComponent';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';

import { MESSAGES } from 'constants/constants';
import PROGRAM_CONFIG from 'constants/programConfig';
import { shouldReadOnlyDetails } from 'helpers/vehicle.helper';
import OptionsPriceSummaryFooter from 'organisms/OptionsPriceSummaryFooter';

import { DEBOUNCE_DELAY, DEBOUNCE_OPTIONS } from '../../VehicleDetails.constant';
import {
  getIsPrimaryCtaSaveAsDraft,
  isSaveAndUpdateDisabledForVehicle,
  isValidStockTypeForDraftStatusSave,
} from '../../VehicleDetails.helpers';
import { getPrimaryButtonToolTipTitle } from './footer.helper';

import styles from '../../vehicleDetails.module.scss';

const Footer = ({
  vehicleDetails,
  vehicleDetailsForm,
  isEditMode,
  isCTADisabled,
  currentActiveTabKey,
  customerViewProps,
  togglePriceDetailsSummaryDrawer,
  shouldPostAccounting,
  handleSaveAction,
  handleUpdateAction,
  onCancelAction,
  showTertiaryButton,
  isTertiaryButtonClicked,
  isPostAccountingDisabled,
  isPostingToAccounting,
  isSavingVehicleDetails,
  isUpdatingVehicleDetails,
  isUploadStillInProgress,
  enterpriseV2Enabled,
  enterpriseV2Workspaces,
  lite,
  hideDrawer,
}) => {
  const vehicleType = VehicleReader.vehicleType(vehicleDetailsForm);

  const shouldShowRevampedCTA = useMemo(() => PROGRAM_CONFIG.shouldEnableRevampedCTABehaviour(), []);

  const isPrimaryCtaSaveAsDraft = useMemo(
    () => getIsPrimaryCtaSaveAsDraft({ isEditMode, vehicleType }),
    [isEditMode, vehicleType]
  );

  const onPrimaryAction = useCallback(
    _debounce(
      () => handleSaveAction(shouldShowRevampedCTA ? lite || false : isPrimaryCtaSaveAsDraft),
      DEBOUNCE_DELAY,
      DEBOUNCE_OPTIONS
    ),
    [isPrimaryCtaSaveAsDraft, lite]
  );

  const onTertiaryAction = useCallback(
    _debounce(() => (lite ? hideDrawer() : handleSaveAction(true)), DEBOUNCE_DELAY, DEBOUNCE_OPTIONS),
    [lite]
  );

  const onAdditionalAction = useCallback(_debounce(handleUpdateAction, DEBOUNCE_DELAY, DEBOUNCE_OPTIONS), []);

  const containerClassName = useMemo(
    () =>
      cx({
        [styles.viewOnly]: shouldReadOnlyDetails({
          vehicleDetails,
          isEditMode,
          enterpriseV2Enabled,
          enterpriseV2Workspaces,
          lite,
        }),
        [styles.footer]: lite,
      }),
    [vehicleDetails, isEditMode, enterpriseV2Enabled, enterpriseV2Workspaces, lite]
  );

  const footerClassName = useMemo(
    () => cx({ 'justify-content-between': PROGRAM_CONFIG.shouldShowVehicleSyndicationList() }),
    []
  );

  const primaryButtonLabel = useMemo(() => {
    if (lite) return isEditMode ? __('Update') : __('Add');
    if (shouldShowRevampedCTA) return __('Ready To Post');

    if (isPrimaryCtaSaveAsDraft) return __('Save as Draft');
    return shouldPostAccounting ? __('Ready To Post') : __('Save');
  }, [shouldPostAccounting, isPrimaryCtaSaveAsDraft, shouldShowRevampedCTA, lite]);

  const tertiaryButtonLabel = useMemo(() => {
    if (lite) return __('Cancel');
    if (shouldShowRevampedCTA) return __('Save as Draft');

    return isValidStockTypeForDraftStatusSave(vehicleType) && !isEditMode ? __('Save as Draft') : __('Save');
  }, [vehicleType, isEditMode, shouldShowRevampedCTA, lite]);

  const isPrimaryActionLoading = useMemo(
    () => (!isTertiaryButtonClicked && isSavingVehicleDetails) || isPostingToAccounting,
    [isTertiaryButtonClicked, isSavingVehicleDetails, isPostingToAccounting]
  );
  const isTertiaryActionLoading = useMemo(
    () => isTertiaryButtonClicked && isSavingVehicleDetails,
    [isTertiaryButtonClicked, isSavingVehicleDetails]
  );

  const isSaveAndUpdateDisabled = useMemo(
    () => isSaveAndUpdateDisabledForVehicle({ vehicleDetails: vehicleDetailsForm, isCTADisabled }),
    [vehicleDetailsForm, isCTADisabled]
  );

  const isPrimaryDisabled = useMemo(
    () => !lite && (isPostAccountingDisabled?.disabled || isSaveAndUpdateDisabled),
    [isPostAccountingDisabled, isSaveAndUpdateDisabled, lite]
  );

  const renderAdditionalFooterDetail = useCallback(
    () => (
      <>
        <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowVehicleSyndicationList()}>
          <div className={`d-flex justify-content-start align-items-center ${styles.syndicationInfo}`}>
            <FontIcon className="m-l-8 m-r-8">icon-syndication-2</FontIcon>
            <Content>{__('Mandatory fields for syndication')}</Content>
          </div>
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowPriceSummaryFooter()}>
          <OptionsPriceSummaryFooter
            vehicleDetailsForm={vehicleDetailsForm}
            customerViewProps={customerViewProps}
            currentActiveTabKey={currentActiveTabKey}
            togglePriceDetailsSummaryDrawer={togglePriceDetailsSummaryDrawer}
          />
        </PropertyControlledComponent>
      </>
    ),
    [vehicleDetailsForm, currentActiveTabKey, customerViewProps, togglePriceDetailsSummaryDrawer]
  );

  const primaryButtonToolTipTitle = useMemo(
    () =>
      getPrimaryButtonToolTipTitle({
        isPostAccountingDisabled,
        isUploadStillInProgress,
        isPrimaryDisabled,
      }),
    [isUploadStillInProgress, isPostAccountingDisabled, isPrimaryDisabled]
  );

  return (
    <SaveComponent
      containerClassName={containerClassName}
      className={footerClassName}
      renderAdditionalFooterDetail={renderAdditionalFooterDetail}
      primaryButtonLabel={primaryButtonLabel}
      tertiaryButtonLabel={tertiaryButtonLabel}
      additionalButtonLabel={__('Update')}
      onPrimaryAction={onPrimaryAction}
      onSecondaryAction={onCancelAction}
      onTertiaryAction={onTertiaryAction}
      onAdditionalAction={onAdditionalAction}
      isPrimaryDisabled={isPrimaryDisabled}
      isSecondaryDisabled={isCTADisabled}
      isTertiaryDisabled={isCTADisabled && !lite}
      isAdditionalDisabled={isSaveAndUpdateDisabled}
      primaryActionLoading={isPrimaryActionLoading}
      tertiaryActionLoading={isTertiaryActionLoading}
      additionalActionLoading={isUpdatingVehicleDetails}
      showTertiaryButton={showTertiaryButton || lite}
      showAdditionalButton={isEditMode && !lite}
      showSecondaryButton={!lite}
      primaryButtonToolTipTitle={primaryButtonToolTipTitle}
      tertiaryButtonToolTipTitle={isUploadStillInProgress ? MESSAGES.UPLOAD_IN_PROGRESS_MESSAGE : null}
      additionalButtonToolTipTitle={isUploadStillInProgress ? MESSAGES.UPLOAD_IN_PROGRESS_MESSAGE : null}
      primaryButtonToolTipPlacement={TOOLTIP_PLACEMENT.TOP_RIGHT}
      tertiaryButtonToolTipPlacement={TOOLTIP_PLACEMENT.TOP_RIGHT}
    />
  );
};

Footer.propTypes = {
  vehicleDetails: PropTypes.object,
  vehicleDetailsForm: PropTypes.object,
  isEditMode: PropTypes.bool,
  isCTADisabled: PropTypes.bool,
  currentActiveTabKey: PropTypes.string,
  customerViewProps: PropTypes.object,
  togglePriceDetailsSummaryDrawer: PropTypes.func,
  shouldPostAccounting: PropTypes.bool,
  handleSaveAction: PropTypes.func,
  handleUpdateAction: PropTypes.func,
  onCancelAction: PropTypes.func,
  showTertiaryButton: PropTypes.bool,
  isTertiaryButtonClicked: PropTypes.bool,
  isPostAccountingDisabled: PropTypes.object,
  isPostingToAccounting: PropTypes.bool,
  isSavingVehicleDetails: PropTypes.bool,
  isUpdatingVehicleDetails: PropTypes.bool,
  isUploadStillInProgress: PropTypes.bool,
  enterpriseV2Enabled: PropTypes.bool,
  enterpriseV2Workspaces: PropTypes.array,
};

Footer.defaultProps = {
  vehicleDetails: EMPTY_OBJECT,
  vehicleDetailsForm: EMPTY_OBJECT,
  isEditMode: false,
  isCTADisabled: false,
  currentActiveTabKey: EMPTY_STRING,
  customerViewProps: EMPTY_OBJECT,
  togglePriceDetailsSummaryDrawer: _noop,
  shouldPostAccounting: false,
  handleSaveAction: _noop,
  handleUpdateAction: _noop,
  onCancelAction: _noop,
  showTertiaryButton: false,
  isTertiaryButtonClicked: false,
  isPostAccountingDisabled: {
    disabled: false,
    disabledMessage: EMPTY_STRING,
  },
  isPostingToAccounting: false,
  isSavingVehicleDetails: false,
  isUpdatingVehicleDetails: false,
  isUploadStillInProgress: false,
  enterpriseV2Enabled: false,
  enterpriseV2Workspaces: EMPTY_ARRAY,
};

export default React.memo(Footer);
