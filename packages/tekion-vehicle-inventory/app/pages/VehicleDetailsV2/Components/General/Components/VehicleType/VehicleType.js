import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import _isEqual from 'lodash/isEqual';
import _find from 'lodash/find';
import _noop from 'lodash/noop';
import _upperCase from 'lodash/upperCase';
import _map from 'lodash/map';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import { VEHICLE_TYPES } from 'tbase/constants/vehicleInventory/vehicleTypes';
import { isInchcape } from 'tbase/utils/sales/dealerProgram.utils';
import CheckBox from 'tcomponents/atoms/checkbox';
import Heading from 'tcomponents/atoms/Heading';
import Content from 'tcomponents/atoms/Content';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import Radio from 'tcomponents/molecules/Radio/Radio';
import withIcon, { POSITIONS } from 'tcomponents/molecules/WithIcon.hoc';
import Popover, { POPOVER_PLACEMENT, POPOVER_TRIGGER } from 'tcomponents/molecules/popover';

import PROGRAM_CONFIG from 'constants/programConfig';
import CargoRadio from 'atoms/CargoRadio/CargoRadio';
import { isRVDealerEnabled, isMultiLingualEnabled, isVehicleStatusSold } from 'helpers/vehicle.helper';
import { hasVehicleSubCategoryEdit } from 'permissions/inventory.permissions';
import { MESSAGES } from 'constants/constants';

import { VEHICLE_TYPE_BUTTONS, CERTIFIED_FLAGS_VS_INFO_TEXT } from '../../general.constant';
import { getVehicleTypes, getVehicleSubTypes } from '../../general.reader';
import { VAT_QUALIFYING_OPTIONS } from './vehicleType.constants';
import CertificationStatus from '../CertificationStatus';
import TestDrivePoolSwitch from '../TestDrivePoolSwitch';

import styles from '../../general.module.scss';

const CheckBoxWithInfo = withIcon(CheckBox);

const POPOVER_STOCK_TYPE_CONTENT = (
  <Content className={styles.stockTypePopoverInfo}>{MESSAGES.RESTRCIT_STOCK_TYPE_CHANGE}</Content>
);

const POPOVER_PROPS = {
  placement: POPOVER_PLACEMENT.TOP,
  content: POPOVER_STOCK_TYPE_CONTENT,
  trigger: POPOVER_TRIGGER.CLICK,
};

class VehicleType extends PureComponent {
  static propTypes = {
    onVehicleTypeChange: PropTypes.func,
    onToggleCertified: PropTypes.func,
    onVehicleTypeSubCategoryChange: PropTypes.func,
    options: PropTypes.array,
    selectedVehicleType: PropTypes.string,
    selectedVehicleSubType: PropTypes.string,
    certified: PropTypes.bool,
    isVehicleSubTypeMandatory: PropTypes.object,
    certificationStatusProps: PropTypes.object,
    isEditMode: PropTypes.bool,
    shouldShowVATQualifying: PropTypes.bool,
    isVATQualifying: PropTypes.bool,
    onVatQualifyingChange: PropTypes.func,
    vehicleStatus: PropTypes.string,
  };

  static defaultProps = {
    onVehicleTypeChange: _noop,
    onToggleCertified: _noop,
    onVehicleTypeSubCategoryChange: _noop,
    options: EMPTY_ARRAY,
    selectedVehicleType: EMPTY_STRING,
    selectedVehicleSubType: EMPTY_STRING,
    certified: false,
    isVehicleSubTypeMandatory: EMPTY_OBJECT,
    certificationStatusProps: EMPTY_OBJECT,
    isEditMode: false,
    shouldShowVATQualifying: false,
    isVATQualifying: undefined,
    onVatQualifyingChange: _noop,
    vehicleStatus: EMPTY_STRING,
  };

  constructor(props) {
    super(props);
    this.state = {
      vehicleTypeOptions: EMPTY_ARRAY,
      additionalChilds: EMPTY_ARRAY,
      previousVehicleType: props.selectedVehicleType,
      stockTypeClicked: null,
      shouldShowStockTypePopoverVisible: false,
    };
  }

  static getDerivedStateFromProps(nextProps, prevState) {
    const { options, selectedVehicleType } = nextProps;
    if (!_isEmpty(options) && (!_isEqual(selectedVehicleType, prevState.previousVehicleType) || selectedVehicleType)) {
      const selectedType = options.find(
        option => option.type === _upperCase(selectedVehicleType) || option.type === selectedVehicleType
      );
      return {
        additionalChilds: selectedType ? getVehicleSubTypes(selectedType.subTypes) : EMPTY_ARRAY,
        selectedSubTypes: selectedType?.subTypes,
      };
    }
    return null;
  }

  componentDidMount() {
    this.init();
  }

  componentDidUpdate(prevProps) {
    const { options } = this.props;
    if (!_isEqual(prevProps.options, options)) {
      this.init();
    }
  }

  init = () => {
    const { options } = this.props;
    const vehicleTypes = options.map(option => {
      const vehicleType = VEHICLE_TYPE_BUTTONS(isRVDealerEnabled()).find(button => button.value === option.type);
      return getVehicleTypes(vehicleType, option);
    });

    this.setState({
      vehicleTypeOptions: vehicleTypes,
    });
  };

  toggleRadioButton = item => {
    const { onVehicleTypeChange, options, isEditMode } = this.props;

    if (isEditMode && !PROGRAM_CONFIG.shouldEnableStockTypeChangeV2() && isInchcape()) {
      this.setState({
        shouldShowStockTypePopoverVisible: true,
        stockTypeClicked: item.value,
      });
      return;
    }

    onVehicleTypeChange(item);
    const selectedType = _find(options, option => option?.type === item?.value);
    this.setState({
      additionalChilds: item.children || [],
      selectedSubTypes: selectedType?.subTypes,
    });
  };

  getSelectedVehicleType = vehicleType => {
    if (!isMultiLingualEnabled()) return vehicleType;
    const { selectedSubTypes } = this.state;
    const subCategoryValue = _find(
      selectedSubTypes,
      subType => subType?.languages?.locale?.[subType?.localeOrigin]?.name === vehicleType
    );
    return subCategoryValue?.name || vehicleType;
  };

  onVehicleSubCategoryChange = subCategory => {
    const { onVehicleTypeSubCategoryChange } = this.props;
    if (isMultiLingualEnabled()) {
      const { selectedSubTypes } = this.state;
      const subCategoryValue = _find(selectedSubTypes, subType => subType?.name === subCategory?.target?.value);
      onVehicleTypeSubCategoryChange(
        subCategoryValue?.languages?.locale?.[subCategoryValue?.localeOrigin]?.name || subCategory?.target?.value
      );
    } else {
      onVehicleTypeSubCategoryChange(subCategory?.target?.value);
    }
  };

  handlePopoverVisibleChange = isVisible => {
    if (!isVisible)
      this.setState({
        shouldShowStockTypePopoverVisible: false,
        stockTypeClicked: null,
      });
  };

  isStockTypePopoverVisible = item => {
    const { selectedVehicleType } = this.props;
    const { shouldShowStockTypePopoverVisible, stockTypeClicked } = this.state;
    return shouldShowStockTypePopoverVisible && stockTypeClicked === item.value && selectedVehicleType !== item.value;
  };

  render() {
    const { additionalChilds, vehicleTypeOptions } = this.state;
    const {
      selectedVehicleType,
      onToggleCertified,
      certified,
      dealerCertified,
      selectedVehicleSubType,
      isVehicleSubTypeMandatory,
      certificationStatusProps,
      shouldShowVATQualifying,
      isVATQualifying,
      onVatQualifyingChange,
      vehicleStatus,
    } = this.props;

    return (
      <>
        <Heading size={3} className={`m-b-16 ${!PROGRAM_CONFIG.disableStockType() && styles.requiredLabel}`}>
          {__('Stock Type')}
        </Heading>
        <div className={cx(styles.vehicleType, { [styles.disabled]: PROGRAM_CONFIG.disableStockType() })}>
          {_map(vehicleTypeOptions, item => (
            <Popover
              key={item.value}
              visible={this.isStockTypePopoverVisible(item)}
              onVisibleChange={this.handlePopoverVisibleChange}
              {...POPOVER_PROPS}>
              <CargoRadio
                icon={item.icon}
                iconSize={50}
                color={selectedVehicleType === item.value ? styles.onSelectColor : styles.defaultColor}
                label={item.label}
                item={item}
                isSelected={selectedVehicleType === item.value}
                handler={PROGRAM_CONFIG.disableStockType() ? _noop : this.toggleRadioButton}
              />
            </Popover>
          ))}
        </div>
        <PropertyControlledComponent
          controllerProperty={!PROGRAM_CONFIG.shouldHideStockSubType() && !_isEmpty(additionalChilds)}>
          <div>
            <Heading size={3} className={cx('m-y-16', { [styles.requiredLabel]: isVehicleSubTypeMandatory?.enable })}>
              {__('Stock SubType')}
            </Heading>

            <Radio
              radios={additionalChilds}
              value={this.getSelectedVehicleType(selectedVehicleSubType)}
              onChange={this.onVehicleSubCategoryChange}
              disabled={!hasVehicleSubCategoryEdit() || (isInchcape() && isVehicleStatusSold(vehicleStatus))}
            />
          </div>
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={selectedVehicleType === VEHICLE_TYPES.USED}>
          <PropertyControlledComponent
            controllerProperty={!PROGRAM_CONFIG.shouldShowCertificationStatusV2()}
            fallback={<CertificationStatus {...certificationStatusProps} />}>
            <div className={`${styles.checkbox} d-flex flex-row`}>
              <CheckBoxWithInfo
                iconPosition={POSITIONS.RIGHT}
                iconPopoverClassName={styles.iconPopoverClassName}
                icon="icon-info"
                iconClassName="m-r-12"
                checked={certified}
                onChange={onToggleCertified('certified')}
                label={__('Certified Pre-Owned Vehicle')}
                toolTipTitle={CERTIFIED_FLAGS_VS_INFO_TEXT.OEM_CERTIFIED}
                disabled={
                  PROGRAM_CONFIG.disablePreOwnedOptions() || (isInchcape() && isVehicleStatusSold(vehicleStatus))
                }
              />
              <CheckBoxWithInfo
                iconPosition={POSITIONS.RIGHT}
                iconPopoverClassName={styles.iconPopoverClassName}
                icon="icon-info"
                checked={dealerCertified}
                onChange={onToggleCertified('dealerCertified')}
                toolTipTitle={CERTIFIED_FLAGS_VS_INFO_TEXT.DEALER_CERTIFIED}
                label={__('Dealer Certified Vehicle')}
                disabled={
                  PROGRAM_CONFIG.disablePreOwnedOptions() || (isInchcape() && isVehicleStatusSold(vehicleStatus))
                }
              />
            </div>
          </PropertyControlledComponent>
        </PropertyControlledComponent>
        <PropertyControlledComponent controllerProperty={shouldShowVATQualifying}>
          <div>
            <Heading size={3} className={cx('m-y-16', { [styles.requiredLabel]: isVehicleSubTypeMandatory?.enable })}>
              {__('VAT Qualifying')}
            </Heading>

            <Radio
              radios={VAT_QUALIFYING_OPTIONS}
              value={isVATQualifying}
              onChange={onVatQualifyingChange}
              disabled={isInchcape() && isVehicleStatusSold(vehicleStatus)}
            />
          </div>
        </PropertyControlledComponent>

        <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldRenderTestDrivePoolSwitch()}>
          <TestDrivePoolSwitch disabled={isInchcape() && isVehicleStatusSold(vehicleStatus)} />
        </PropertyControlledComponent>
      </>
    );
  }
}

export default VehicleType;
