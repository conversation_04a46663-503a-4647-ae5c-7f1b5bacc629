import { createSelector } from 'reselect';
import _property from 'lodash/property';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _values from 'lodash/values';
import _chunk from 'lodash/chunk';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isFunction from 'lodash/isFunction';

import { EMPTY_OBJECT } from 'tbase/app.constants';
import addToRenderOptions from 'tbase/utils/addToRenderOptions';
import { isInchcapeOrRRG, isRRG } from 'tbase/utils/sales/dealerProgram.utils';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import { FORM_FIELD_LABEL_CONFIG, GENERAL_FIELD_KEYS } from 'twidgets/appServices/sales/config/viFormFields';

import { showCountryDependentFields } from 'helpers/vehicle.helper';
import speedLabelUtil from 'utils/speedLabelUtil';

import { BODY_TYPE, FORM_FIELDS, FUEL_TYPE, TRIM_MODAL_KEYS, MAX_SPEED_OF_VEHICLE, TRIM } from './trimModal.constants';

import styles from './trimModal.module.scss';
export const filteredTrimKeys = dealerMasterData =>
  _filter(_values(TRIM_MODAL_KEYS), key => {
    const shouldHide = _get(FORM_FIELDS, [key, 'shouldHide']);
    if (_isFunction(shouldHide)) return !shouldHide();
    if (key === TRIM_MODAL_KEYS.FUEL_TYPE && isInchcapeOrRRG()) {
      return false;
    }
    if (key === TRIM_MODAL_KEYS.BODY_STYLE_CODE) {
      return showCountryDependentFields({ dealerData: dealerMasterData, fieldKey: key }) || isRRG();
    }
    return true;
  });

const chunkedTrimModelKeys = dealerMasterData => _chunk(filteredTrimKeys(dealerMasterData), 2);

export const FORM_SECTIONS = ({ dealerMasterData }) => [
  {
    className: styles.section,
    subHeader: {
      label: __('Trim Details'),
    },
    rows: _map(chunkedTrimModelKeys(dealerMasterData), trimColumns => ({ columns: trimColumns })),
  },
];

export const getFormFields = (
  fuelTypeOptions,
  bodyTypeOptions,
  handleAddNewBodyType,
  isMultiLingualEnabled,
  isTrimMandatory
) => {
  const disabled = DealerPropertyHelper.isViArcLiteEnabled();
  const formFields = _reduce(
    FORM_FIELDS,
    (acc, value, key) => {
      if (FORM_FIELDS?.[key]?.shouldHide?.()) return acc;
      if (FORM_FIELDS?.[key]?.renderOptions?.isMultiLingual) {
        return {
          ...acc,
          [key]: addToRenderOptions(FORM_FIELDS?.[key], [
            {
              path: 'isMultiLingual',
              value: isMultiLingualEnabled,
            },
            { path: 'disabled', value: disabled },
          ]),
        };
      }
      return {
        ...acc,
        [key]: addToRenderOptions(FORM_FIELDS?.[key], [{ path: 'disabled', value: disabled }]),
      };
    },
    EMPTY_OBJECT
  );

  return {
    ...formFields,
    [FUEL_TYPE.id]: addToRenderOptions(FUEL_TYPE, [
      { path: 'options', value: fuelTypeOptions },
      { path: 'disabled', value: disabled },
    ]),
    [BODY_TYPE.id]: addToRenderOptions(BODY_TYPE, [
      { path: 'options', value: bodyTypeOptions },
      { path: 'onCreateOption', value: handleAddNewBodyType },
      { path: 'isDisabled', value: disabled },
    ]),
    [MAX_SPEED_OF_VEHICLE.id]: addToRenderOptions(MAX_SPEED_OF_VEHICLE, [
      {
        path: 'addonBefore',
        value: __('{{speedUnit}}', { speedUnit: speedLabelUtil.getSpeedMeasureUnitLabel() }),
      },
      { path: 'disabled', value: disabled },
    ]),
    [TRIM.id]: addToRenderOptions(formFields?.[TRIM.id], [
      { path: 'label', value: FORM_FIELD_LABEL_CONFIG[GENERAL_FIELD_KEYS.TRIM_CODE]() },
      { path: 'required', value: isTrimMandatory },
      { path: 'disabled', value: disabled },
    ]),
  };
};

const KEYS_TO_PICK_FOR_FORM_FIELDS = [
  'fuelTypeOptions',
  'bodyTypeOptions',
  'handleAddNewBodyType',
  'isMultiLingualEnabled',
  'isTrimMandatory',
];

export const createSelectorForFormFields = () =>
  createSelector(KEYS_TO_PICK_FOR_FORM_FIELDS.map(_property), getFormFields);
