import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import { compose } from 'recompose';
import cx from 'classnames';
import _get from 'lodash/get';
import _isNil from 'lodash/isNil';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _toUpper from 'lodash/toUpper';
import _isEmpty from 'lodash/isEmpty';
import _filter from 'lodash/filter';
import _stubTrue from 'lodash/stubTrue';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from 'tbase/app.constants';
import NOTES_ASSET_TYPES from 'tbase/constants/notesAssetTypes';
import { isInchcapeOrRRG } from 'tbase/utils/sales/dealerProgram.utils';
import { getVehicleIcon, getSanitizedYearMakeBrandModel, getPlaceHolderImage } from 'tbase/helpers/vehicle.helper';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import { getNewVehicleTransferRoute } from 'tbusiness/appServices/vehicleInventory/helpers/routes';
import carPlaceHolder from 'tcomponents/assets/images/carListItemPlaceholder.svg';
import rvPlaceHolder from 'tcomponents/assets/images/RV.svg';
import Content from 'tcomponents/atoms/Content';
import Ellipsis from 'tcomponents/atoms/Ellipsis';
import FontIcon, { SIZES } from 'tcomponents/atoms/FontIcon';
import Switch from 'tcomponents/molecules/Switch';
import HelperText from 'tcomponents/atoms/HelperText';
import Icon from 'tcomponents/atoms/iconAsBtn';
import KebabMenu from 'tcomponents/molecules/KebabMenu/KebabMenu';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';
import PageHeader from 'tcomponents/molecules/pageComponent/PageHeader';
import Popover, { POPOVER_TRIGGER } from 'tcomponents/molecules/popover';
import PropertyControlledComponent from 'tcomponents/molecules/PropertyControlledComponent';
import TEnvReader from 'tbase/readers/Env';
import CampaignCodePopover from 'twidgets/organisms/sales/CampaignCodePopover';
import { withExperienceEngineConsumer, FEATURE_NAME } from 'twidgets/experienceEngine';
import Button from 'tcomponents/atoms/Button';

import PROGRAM_CONFIG from 'constants/programConfig';
import { DEFAULT_TABLE_PROPS } from 'constants/table.constants';
import { VIEW_TYPE, MESSAGES } from 'constants/constants';
import { handleVehicleDetailsBackNavigation } from 'helpers/route';
import { getDealershipOptions } from 'helpers/user';
import {
  isRVDealerEnabled,
  shouldReadOnlyDetails,
  handleStartDealForVehicles,
  validateTransferEligibility,
  isCustomOrder,
  getAllEnterpriseWorkspaceIds,
  shouldShowStartDeal,
  getFormattedYearMakeBrandModel,
} from 'helpers/vehicle.helper';
import DeleteConfirmationModal, {
  showDeleteConfirmationModal,
} from 'molecules/ConfirmationModals/DeleteConfirmationModal';
import { showNotesModal } from 'molecules/NotesModal';
import NotesPopover from 'molecules/NotesPopover';
import VehicleStatusRenderer from 'molecules/VehicleStatusRenderer';
import FlagPickerRadioButtonCellRenderer from 'molecules/cellRenderers/FlagPickerRadioButtonCellRenderer';
import VehicleActionsPopover from 'organisms/VehicleActionsPopover';
import UpdateTogglesPopover from 'organisms/UpdateTogglesPopover';
import TransferVehicleModal, { showTransferVehicleModal } from 'organisms/TransferVehicleModal';
import { getDealershipName } from 'organisms/TransferDetails/transferDetails.helpers';
import UnlockPasswordModal from 'organisms/UnlockPasswordModal';
import withWorkSpaceUserAccess from 'hocs/withWorkSpaceUserAccess';
import { hasLabelPrintPermission } from 'permissions/inventory.permissions';

import { getParameterConfig, POPOVER_CONFIG, PARAMETER_CONFIG_STOCK_TYPE } from './Header.config';
import { getShareMenuConfig } from './Header.helpers';
import ACTION_TYPES from './header.actionTypes';
import { VEHICLE_DETAIL_FORM_STORE_KEY } from '../../VehicleDetails.constant';

import styles from './header.module.scss';
import vDetailsStyles from '../../vehicleDetails.module.scss';

const DEFAULT_STATE = {
  tableProps: DEFAULT_TABLE_PROPS,
  hasRenderError: false,
  status: EMPTY_STRING,
  interCompanyDealers: EMPTY_ARRAY,
  dealerAccountingSetup: EMPTY_OBJECT,
};

const groupedMenuTrigger = <Button view={Button.VIEW.ICON} iconClassName="p-8" icon="icon-share" />;

class Header extends PureComponent {
  constructor(props) {
    super(props);
    this.state = DEFAULT_STATE;
    this.canPrintLabel = hasLabelPrintPermission();
  }

  getShareMenuConfig = defaultMemoize(getShareMenuConfig);

  componentDidMount() {
    this.init();
  }

  init = async () => {
    const { actions, getFeatureValue, enterpriseV2Workspaces } = this.props;
    const enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    const fetchAccountingSetupsPayload = enterpriseV2Enabled
      ? getAllEnterpriseWorkspaceIds(enterpriseV2Workspaces)
      : EMPTY_ARRAY;
    const interCompanyDealerships = await actions.fetchInterCompanyDealerships();
    const dealerAccountingSetup = await actions.fetchAccountingSetups(fetchAccountingSetupsPayload);
    this.setState({ interCompanyDealerships, dealerAccountingSetup });
  };

  onRenderError = () => this.setState({ hasRenderError: true });

  goBackHandler = () => {
    const { navigate, location } = this.props;
    handleVehicleDetailsBackNavigation({
      navigate,
      location,
    });
  };

  showAuditLogs = () => {
    const { auditLogsRef } = this.props;
    auditLogsRef.openAuditLogs();
  };

  onSubStatusFlagChangeSuccess = (updatedVehicleResponse, isToFromReceivedSubStatus) => {
    const { actions, vehicleDetailsForm, fetchPoliceRecordData } = this.props;
    // Updating the Received Date (entryTime) in form, to avoid Data Mismatch when user Save/Update the vehicle
    const path = [VEHICLE_DETAIL_FORM_STORE_KEY, 'entryTime'].join('.');
    actions.updateVehicleObject({ [path]: _get(updatedVehicleResponse, 'data.entryTime') });
    if (isToFromReceivedSubStatus) {
      fetchPoliceRecordData(vehicleDetailsForm);
    }
  };

  getShouldReadOnlyDetails = () => {
    const { isEditMode, getFeatureValue, enterpriseV2Workspaces, vehicleDetailsForm, lite } = this.props;
    const enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    return shouldReadOnlyDetails({
      vehicleDetails: vehicleDetailsForm,
      isEditMode,
      enterpriseV2Enabled,
      enterpriseV2Workspaces,
      lite,
    });
  };

  getTrimAndYMMDetails = () => {
    const { customStatuses, actions, isEditMode, displayModelSource, vehicleDetailsInStore, vehicleDetailsForm } =
      this.props;
    const ymm = getFormattedYearMakeBrandModel(vehicleDetailsForm, displayModelSource, isRVDealerEnabled());
    const trim = _get(vehicleDetailsForm, 'trimDetails.trim') || '';

    return (
      <div className="d-flex flex-column">
        {ymm && (
          <div className="d-flex flex-row align-items-center">
            <Content highlight className={styles.spaceBetween}>
              {ymm}
            </Content>
            <div className="p-l-4 p-b-4">
              <PropertyControlledComponent controllerProperty={VehicleReader.stopDeliveryIndicator(vehicleDetailsForm)}>
                <CampaignCodePopover campaignCodes={VehicleReader.campaignCodes(vehicleDetailsForm)} />
              </PropertyControlledComponent>
            </div>
          </div>
        )}
        <div className="d-flex flex-row justify-content-between">
          {trim && <HelperText>{trim}</HelperText>}
          <PropertyControlledComponent controllerProperty={isEditMode && !PROGRAM_CONFIG.shouldHideVehicleSubStatus()}>
            <div
              className={cx('cursor-pointer m-l-8', {
                [vDetailsStyles.viewOnly]: this.getShouldReadOnlyDetails(),
              })}>
              <FlagPickerRadioButtonCellRenderer
                vehicleSubStatuses={customStatuses}
                selectedOptionKey={VehicleReader.vehicleSubStatus(vehicleDetailsInStore)}
                vehicleId={VehicleReader.id(vehicleDetailsForm)}
                showLabel={false}
                actions={actions}
                onSuccessCb={this.onSubStatusFlagChangeSuccess}
              />
            </div>
          </PropertyControlledComponent>
        </div>
      </div>
    );
  };

  getVehicleImage = () => {
    const { isEditMode, vehicleDetails } = this.props;
    const { hasRenderError } = this.state;
    const imageLink =
      getVehicleIcon(vehicleDetails) || getPlaceHolderImage(carPlaceHolder, rvPlaceHolder, isRVDealerEnabled());

    return (
      <PropertyControlledComponent controllerProperty={isEditMode && getVehicleIcon(vehicleDetails)}>
        <img
          className={styles.imageContainer}
          alt=""
          src={hasRenderError ? getPlaceHolderImage(carPlaceHolder, rvPlaceHolder, isRVDealerEnabled()) : imageLink}
          onError={this.onRenderError}
        />
      </PropertyControlledComponent>
    );
  };

  renderBasicVehicleDetails = () => (
    <>
      {this.getVehicleImage()}
      {this.getTrimAndYMMDetails()}
    </>
  );

  renderDivider = key => <span className={styles.divider} key={key}></span>;

  renderStockId = () => {
    const { vehicleDetailsForm } = this.props;
    const stockID = VehicleReader.stockId(vehicleDetailsForm);
    if (!stockID) {
      return null;
    }

    return (
      <>
        {this.renderDivider('stockId')}
        <div className="d-flex flex-column">
          <HelperText className={styles.spaceBetween}>{__('Stock#')}</HelperText>
          <Content highlight>
            <Ellipsis length={10} tooltip>
              {_toUpper(stockID)}
            </Ellipsis>
          </Content>
        </div>
      </>
    );
  };

  renderDetails = ({ key, label, formatter, shouldShow = _stubTrue }) => {
    const {
      vehicleDetailsForm,
      agingField,
      customerViewProps,
      vendorInvoiceData,
      getFormattedDateAndTime,
      getFeatureValue,
    } = this.props;
    const value = formatter({
      vehicleDetails: vehicleDetailsForm,
      agingField,
      vendorInvoiceData,
      getFormattedDateAndTime,
    });
    if (_isNil(value)) {
      return null;
    }

    const enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);
    const shouldShowData = shouldShow({ fieldKey: key, ...customerViewProps, enterpriseV2Enabled });
    return (
      <PropertyControlledComponent controllerProperty={shouldShowData}>
        <React.Fragment key={key}>
          {this.renderDivider(key)}
          <div className="d-flex flex-column">
            <HelperText className={styles.spaceBetween}>{label}</HelperText>
            <Content highlight>{value}</Content>
          </div>
        </React.Fragment>
      </PropertyControlledComponent>
    );
  };

  renderStatus = () => {
    const { vehicleDetailsForm } = this.props;
    return <VehicleStatusRenderer vehicleDetails={vehicleDetailsForm} viewType={VIEW_TYPE.DETAILS_VIEW} />;
  };

  onNotesIconClick = () => {
    showNotesModal();
  };

  handleDeleteVehicle = vehicle => {
    const { displayModelSource } = this.props;
    showDeleteConfirmationModal({ vehicleObject: vehicle, displayModelSource }, this.onConfirmVehicleDelete);
  };

  onConfirmVehicleDelete = async vehicle => {
    const { actions } = this.props;
    const id = VehicleReader.id(vehicle);
    await actions.deleteVehicles([id]);
    this.goBackHandler();
  };

  handleVehicleStatusUpdate = async (vehicle, status) => {
    const { onAction } = this.props;
    return onAction({
      type: ACTION_TYPES.VEHICLE_STATUS_UPDATE,
      payload: {
        vehicle,
        status,
      },
    });
  };

  handleVehicleSubStatusUpdate = async (vehicle, subStatus) => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SUB_STATUS,
      payload: {
        vehicle,
        subStatus,
      },
    });
  };

  handleReceiveVehicle = async vehicle => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HANDLE_VEHICLE_RECEIVING,
      payload: {
        vehicle,
      },
    });
  };

  updateVehicle = async (id, payload) => {
    const { onAction } = this.props;
    return onAction({
      type: ACTION_TYPES.VEHICLE_UPDATE,
      payload: {
        id,
        payload,
      },
    });
  };

  handleVoidStatusUpdate = async (vehicle, status) => {
    const { onAction, refetchVehicleDetails, currentLanguageId } = this.props;

    onAction({
      type: ACTION_TYPES.VOID_STATUS_UPDATE,
      payload: {
        vehicle,
        status,
        currentLanguageId,
        refetchVehicleDetails,
      },
    });
  };

  printLabel = () => {
    const { actions } = this.props;
    const { vehicleDetailsForm } = this.props;
    const { stockID, id: vehicleId } = vehicleDetailsForm || EMPTY_OBJECT;
    actions.printVehicleInfo(stockID, vehicleId);
  };

  renderPopoverContent = () => {
    const contentToDisplay = !this.canPrintLabel ? MESSAGES.LABEL_PRINT_MESSAGE : __('Vehicle Info Label');
    return <Content className={cx('p-x-16 p-y-8', styles.printLabelPopoverContent)}>{contentToDisplay}</Content>;
  };

  redirectToNewVehicleTransfer = vehicle => {
    const { navigate } = this.props;
    const redirectUrl = getNewVehicleTransferRoute();
    navigate(redirectUrl, { state: { vehicleDetails: vehicle } });
  };

  handleTransferVehicle = async vehicle => {
    const { actions, displayModelSource, workspaces, getFeatureValue, enterpriseV2Workspaces } = this.props;
    const { dealerAccountingSetup, interCompanyDealerships } = this.state;
    const enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);

    if (PROGRAM_CONFIG.shouldShowTransferPage(enterpriseV2Enabled)) {
      this.redirectToNewVehicleTransfer(vehicle);
      return;
    }

    const { data } = await actions.getDetailedActiveDeals({
      vids: [VehicleReader.id(vehicle)],
      enterpriseV2Enabled,
      enterpriseV2Workspaces,
    });
    const activeDeals = _get(data, 'hits') || EMPTY_ARRAY;

    const { dealerId: currDealerId } = TEnvReader.userInfo();
    const destinationDealers = getDealershipOptions({
      workspaces,
      enterpriseV2Enabled,
      option: { withoutCurrentDealer: true, disabledDealerIds: [VehicleReader.dealerId(vehicle)] },
    });
    const eligibleDestinationDealers = _filter(
      destinationDealers,
      ({ value: dealerId }) => !_isEmpty(_get(interCompanyDealerships, dealerId))
    );

    showTransferVehicleModal(
      {
        vehicle,
        activeDeals,
        displayModelSource,
        dealers: eligibleDestinationDealers,
        errorDetails: validateTransferEligibility({
          activeDeals,
          vehicle,
          dealerAccountingSetup,
          interCompanyDealerships,
          currDealerId,
        }),
      },
      this.validatePasscodeForTransfer
    );
  };

  validatePasscodeForTransfer = params => {
    this.unlockTransferVehicle.show({ onSubmitCB: this.onSubmitTransferVehicle }, params);
  };

  onSubmitTransferVehicle = async ({ formValues, vehicle }) => {
    const { actions, displayModelSource, enterpriseV2Workspaces } = this.props;
    const { markupInvoice, selectedDealer, selectedSite } = formValues;
    const vehicleName = getSanitizedYearMakeBrandModel(
      vehicle,
      displayModelSource,
      DealerPropertyHelper.isRVDealerEnabled()
    );
    const dealerName = getDealershipName({ enterpriseV2Enabled: this.enterpriseV2Enabled, enterpriseV2Workspaces })(
      selectedDealer
    );

    const userInfo = TEnvReader.userInfo();
    const siteId = userInfo.tekSiteId;

    const transferPayload = {
      id: VehicleReader.id(vehicle),
      markUpPrice: Number(markupInvoice),
      transferToDealerId: selectedDealer,
      transferToSiteId: selectedSite,
      sourceDealerId: VehicleReader.dealerId(vehicle),
      sourceSiteId: VehicleReader.stockedInAtSiteId(vehicle) || siteId,
    };
    const response = await actions.transferVehicle(transferPayload, vehicleName, dealerName);

    if (response) this.goBackHandler();
  };

  createLead = vehicle => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.CREATE_LEAD, payload: { vehicle } });
  };

  startDealForVehicle = () => {
    const { vehicleDetailsInStore } = this.props;
    handleStartDealForVehicles([vehicleDetailsInStore.rawVehicleData]);
  };

  renderCustomerSensitiveInfoToggle = () => {
    const { customerViewProps, customerViewToggleHandler } = this.props;
    const { customerViewToggle, isCustomerViewEnabled, customerViewAssets } = customerViewProps || EMPTY_OBJECT;
    const contentText = customerViewToggle ? __('Turn off Customer View') : __('Turn on Customer View');
    return (
      <PropertyControlledComponent
        controllerProperty={
          !PROGRAM_CONFIG.shouldHideToggleCustomerView() && isCustomerViewEnabled && !_isEmpty(customerViewAssets)
        }>
        <div className="d-flex align-items-center m-r-16">
          <FontIcon size={SIZES.MD} className="m-r-8">
            icon-user
          </FontIcon>
          <Popover
            trigger={POPOVER_TRIGGER.HOVER}
            arrowPointAtCenter
            content={contentText}
            overlayClassName={styles.customerToggleOverlay}>
            <Switch
              checked={customerViewToggle}
              onChange={customerViewToggleHandler}
              checkedChildren={<FontIcon size={SIZES.XS}>icon-tick1</FontIcon>}
              unCheckedChildren={<FontIcon size={SIZES.XS}>icon-cross</FontIcon>}
            />
          </Popover>
        </div>
      </PropertyControlledComponent>
    );
  };

  shouldDisableStartDeal = () => {
    const { vehicleDetailsForm } = this.props;
    return isCustomOrder({ vehicle: vehicleDetailsForm });
  };

  render() {
    const {
      assetId,
      isEditMode,
      notes,
      getVehicleDetails,
      currentLanguageId,
      shareMenuHandler,
      viActionsConfig,
      actions,
      customerViewProps,
      customerViewToggleHandler,
      workspaces,
      getFeatureValue,
      enterpriseV2Workspaces,
      vehicleDetailsInStore,
      vinValidateAcrossWorkSpace,
    } = this.props;
    const { vehicleDetailsForm } = this.props;
    const { stockedInAtSiteId, licensePlateNumber } = vehicleDetailsForm;
    const shareMenuConfig = getShareMenuConfig();
    const initialVehicleData = vehicleDetailsInStore?.rawVehicleData;

    const enterpriseV2Enabled = !!getFeatureValue(FEATURE_NAME.ENTERPRISE_V2_ENABLED);

    const shouldShowStartDealButton = shouldShowStartDeal({
      vehicle: initialVehicleData,
      isEditMode,
      viActionsConfig,
    });

    return (
      <PageHeader hasBack goBackHandler={this.goBackHandler}>
        <div className="d-flex justify-content-between full-width">
          <div className="d-flex justify-content-around align-items-center">
            {this.renderBasicVehicleDetails()}
            {this.renderStockId()}
            {this.renderDetails(PARAMETER_CONFIG_STOCK_TYPE)}
            {isEditMode && this.renderStatus()}

            {_map(getParameterConfig(licensePlateNumber), this.renderDetails)}
          </div>
          {isEditMode && (
            <div className="d-flex flex-start align-items-center">
              {this.renderCustomerSensitiveInfoToggle()}
              {shouldShowStartDealButton && (
                <Button
                  className={cx('mr-4', {
                    [vDetailsStyles.viewOnly]: this.getShouldReadOnlyDetails(),
                  })}
                  view={Button.VIEW.PRIMARY}
                  onClick={this.startDealForVehicle}
                  disabled={this.shouldDisableStartDeal()}>
                  {__('Start A Deal')}
                </Button>
              )}
              <PropertyControlledComponent
                controllerProperty={!PROGRAM_CONFIG.shouldHideVehicleHeaderActions() && !isInchcapeOrRRG()}>
                <span className={cx({ [vDetailsStyles.viewOnly]: this.getShouldReadOnlyDetails() })}>
                  <Popover placement="topRight" trigger="hover" content={this.renderPopoverContent()}>
                    <Icon onClick={this.printLabel} className="m-r-24" disabled={!this.canPrintLabel}>
                      icon-printer
                    </Icon>
                  </Popover>
                </span>
              </PropertyControlledComponent>
              <div className="d-flex align-items-center justify-content-between">
                <PropertyControlledComponent controllerProperty={!PROGRAM_CONFIG.shouldHideVehicleHeaderActions()}>
                  <NotesPopover
                    assetId={assetId}
                    assetType={NOTES_ASSET_TYPES.VEHICLE}
                    notes={notes}
                    onNotesIconClick={this.onNotesIconClick}
                    popoverPlacement="bottomLeft"
                    iconContainerClassName={cx('m-r-16', {
                      [vDetailsStyles.viewOnly]: this.getShouldReadOnlyDetails(),
                    })}
                  />
                  <PropertyControlledComponent controllerProperty={!_isEmpty(shareMenuConfig)}>
                    <KebabMenu
                      onClickAction={shareMenuHandler}
                      menuItems={shareMenuConfig}
                      triggerElement={groupedMenuTrigger}
                    />
                  </PropertyControlledComponent>
                </PropertyControlledComponent>
                <VehicleActionsPopover
                  vehicle={getVehicleDetails(vehicleDetailsForm)}
                  initialVehicleData={initialVehicleData}
                  handleDeleteVehicle={this.handleDeleteVehicle}
                  handleVehicleStatusUpdate={this.handleVehicleStatusUpdate}
                  handleReceiveVehicle={this.handleReceiveVehicle}
                  updateVehicle={this.updateVehicle}
                  showAuditLogs={this.showAuditLogs}
                  handleCreateLead={this.createLead}
                  extraActions={POPOVER_CONFIG}
                  handleStartDealForVehicles={handleStartDealForVehicles}
                  handleTransferVehicle={this.handleTransferVehicle}
                  currentLanguageId={currentLanguageId}
                  handleVehicleSubStatusUpdate={this.handleVehicleSubStatusUpdate}
                  viActionsConfig={viActionsConfig}
                  isDetailPage
                  enterpriseV2Enabled={enterpriseV2Enabled}
                  workspaces={workspaces}
                  getSyndicationDetail={actions.getSyndicationDetail}
                  enterpriseV2Workspaces={enterpriseV2Workspaces}
                  onVoidStatusUpdate={this.handleVoidStatusUpdate}
                  vinValidateAcrossWorkSpace={vinValidateAcrossWorkSpace}
                />

                <PropertyControlledComponent controllerProperty={PROGRAM_CONFIG.shouldShowToggleGearIcon()}>
                  <span
                    className={cx({
                      [vDetailsStyles.viewOnly]: this.getShouldReadOnlyDetails(),
                    })}>
                    <UpdateTogglesPopover
                      actions={actions}
                      customerViewProps={customerViewProps}
                      customerViewToggleHandler={customerViewToggleHandler}
                      isEditMode={isEditMode}
                    />
                  </span>
                </PropertyControlledComponent>
              </div>
            </div>
          )}
          <DeleteConfirmationModal />
          <UnlockPasswordModal
            ref={ref => {
              this.unlockTransferVehicle = ref;
            }}
          />
          <TransferVehicleModal />
        </div>
      </PageHeader>
    );
  }
}

Header.propTypes = {
  assetId: PropTypes.string,
  auditLogsRef: PropTypes.object,
  isEditMode: PropTypes.bool.isRequired,
  notes: PropTypes.array.isRequired,
  agingField: PropTypes.string,
  customStatuses: PropTypes.array,
  customerViewToggleHandler: PropTypes.func,
  customerViewProps: PropTypes.object,
  shareMenuHandler: PropTypes.func,
  vendorInvoiceData: PropTypes.array,
  getFormattedDateAndTime: PropTypes.func,
  onAction: PropTypes.func,
  vehicleDetailsForm: PropTypes.object,
  vehicleDetails: PropTypes.object,
  actions: PropTypes.object,
  fetchPoliceRecordData: PropTypes.func,
  workspaces: PropTypes.array,
  getFeatureValue: PropTypes.func,
  refetchVehicleDetails: PropTypes.func,
  navigate: PropTypes.func.isRequired,
  location: PropTypes.object.isRequired,
  vinValidateAcrossWorkSpace: PropTypes.bool,
};

Header.defaultProps = {
  assetId: undefined,
  auditLogsRef: EMPTY_OBJECT,
  agingField: undefined,
  customStatuses: EMPTY_ARRAY,
  customerViewToggleHandler: _noop,
  customerViewProps: EMPTY_OBJECT,
  shareMenuHandler: _noop,
  vendorInvoiceData: EMPTY_ARRAY,
  getFormattedDateAndTime: _noop,
  onAction: _noop,
  vehicleDetailsForm: EMPTY_OBJECT,
  vehicleDetails: EMPTY_OBJECT,
  actions: EMPTY_OBJECT,
  fetchPoliceRecordData: _noop,
  workspaces: EMPTY_ARRAY,
  getFeatureValue: _noop,
  refetchVehicleDetails: _noop,
  vinValidateAcrossWorkSpace: false,
};

export default compose(withExperienceEngineConsumer, withWorkSpaceUserAccess)(Header);
