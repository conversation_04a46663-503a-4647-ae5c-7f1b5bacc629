import { createSelector } from 'reselect';
import _filter from 'lodash/filter';
import _find from 'lodash/find';
import _get from 'lodash/get';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _keys from 'lodash/keys';
import _map from 'lodash/map';
import _omit from 'lodash/omit';
import _reduce from 'lodash/reduce';

import { EMPTY_OBJECT, EMPTY_STRING, EMPTY_ARRAY } from 'tbase/app.constants';
import { DISTANCE_VS_SPEED_UNIT } from 'tbase/constants/vehicleInventory/vehicle';
import { toMoment, getDifferenceAsDaysFromToday } from 'tbase/utils/dateUtils';
import { tget } from 'tbase/utils/general';
import VehicleReader from 'tbusiness/appServices/vehicleInventory/readers/vehicle';
import DealerPropertyHelper from 'tcomponents/helpers/sales/dealerPropertyHelper';

import { VEHICLE_TYPE } from 'constants/constantEnum';
import { ASSET_SUB_TYPES } from 'constants/settings';
import { getStockedInSiteFallback } from 'helpers/vehicle.helper';
import DealerConfigReader from 'readers/dealerConfig';
import SettingsReader from 'readers/settings';
import { getFormattedDealsList } from 'helpers/deal';

import { getFilteredFields, getFilteredSection } from './VehicleDetails.helpers';

const getVehicle = state => _get(state, 'vehicleDetails.rawVehicleData');

const getVehicledetails = state => _omit(_get(state, 'vehicleDetails') || EMPTY_OBJECT, ['valuation', 'leads']);

const getUserTimezone = state => _get(state, 'bootstrap.dealerMasterData.timeZone');

const getUserSettings = state => _get(state, 'userSettings') || EMPTY_OBJECT;

export const getVehicleByIdWithAge = createSelector(getVehicle, vehicle => {
  const { entryTime } = vehicle || {};

  if (entryTime) {
    return {
      ...vehicle,
      age: getDifferenceAsDaysFromToday(toMoment(vehicle.entryTime)),
    };
  }
  return vehicle;
});

export const getVehicleIdWithAgeAndInvoice = createSelector(
  [getVehicle, getUserSettings, getVehicledetails, getUserTimezone],
  (vehicle, userSettings) => {
    const { pricingDetails = {}, roDetails = [], vehicleType } = vehicle || EMPTY_OBJECT;
    const defaultOpcodes =
      VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.USED
        ? _get(userSettings, 'roSettings.usedVehicleDefaultOpcodes')
        : _get(userSettings, 'roSettings.newVehicleDefaultOpcodes');
    const applicabilityFunc =
      VEHICLE_TYPE[vehicleType] === VEHICLE_TYPE.USED ? VehicleReader.uviApplicable : VehicleReader.pdiApplicable;
    const pdiDetails = applicabilityFunc(vehicle) ? defaultOpcodes : [];
    let targetRoDetail = {};
    const isEditMode = !_isEmpty(VehicleReader.id(vehicle));
    const defaultStockedInSite = getStockedInSiteFallback();
    const stockedInSite = VehicleReader.stockedInAtSiteId(vehicle);
    const mileageType = VehicleReader.mileageType(vehicle) || DealerConfigReader.getDealerMilageMeasure();
    const trimDetails = VehicleReader.trimDetails(vehicle);
    const maximumSpeedMeasure = VehicleReader.maximumSpeedMeasure(vehicle);
    const maxSpeedUnitCode = VehicleReader.maxSpeedUnitCode(vehicle) || DISTANCE_VS_SPEED_UNIT[mileageType];
    const marketable = isEditMode ? VehicleReader.marketable(vehicle) : true;

    if (!_isEmpty(roDetails)) {
      targetRoDetail = _reduce(
        roDetails,
        (details, ro) => {
          const { recallJobDetails = EMPTY_ARRAY, pdiJobDetail = EMPTY_ARRAY, uviJobDetail = EMPTY_ARRAY } = ro;
          return {
            ...details,
            recallJobDetails: [...(details.recallJobDetails || EMPTY_ARRAY), ...(recallJobDetails || EMPTY_ARRAY)],
            pdiJobDetail: [...(details.pdiJobDetail || EMPTY_ARRAY), ...(pdiJobDetail || EMPTY_ARRAY)],
            uviJobDetail: [...(details.uviJobDetail || EMPTY_ARRAY), ...uviJobDetail],
          };
        },
        {}
      );
    } else {
      targetRoDetail = {};
    }

    return {
      ...vehicle,
      ..._get(vehicle, 'general'),
      trimDetails: {
        ...trimDetails,
        maximumSpeedMeasure: {
          ...maximumSpeedMeasure,
          unitCode: maxSpeedUnitCode,
        },
      },
      status: _get(vehicle, 'status') || EMPTY_STRING,
      pricingDetails,
      pdiDetails,
      roJobDetail: targetRoDetail,
      mileageType,
      stockedInAtSiteId: isEditMode ? stockedInSite : stockedInSite || defaultStockedInSite,
      unladenWeightUnit: VehicleReader.unladenWeightUnit(vehicle) || DealerConfigReader.getDealerWeightMeasureUnit(),
      grossWeightUnit: VehicleReader.grossWeightUnit(vehicle) || DealerConfigReader.getDealerWeightMeasureUnit(),
      marketable,
    };
  }
);

export const getFormState = (state, form) =>
  (state && state.vehicleDetails && state.vehicleDetails[form]) || state.vehicleDetails;

export const getVehicleDetailsInStore = state => ({
  ...getVehicledetails(state),
  ..._get(state.vehicleDetails, 'general.trimDetails'),
});

export const transactionsListFormatter = state => {
  const transactionList = _get(state, 'vehicleDetails.transactionList', []) || [];
  const newTransactionList = transactionList.map(transaction => ({
    ...transaction,
    account: transaction.journalId,
    amount: transaction.amount,
    transactionDesc: transaction.description || '-',
  }));
  return newTransactionList;
};

export const getFormatedGlAccountList = state => {
  const glAccountObj = _get(state, 'bootstrap.glAccount', {}) || {};
  const glAccountKeys = Object.keys(glAccountObj);
  const formatedGlAccountList = glAccountKeys.map(glAccount => {
    const glAccountId = _get(glAccountObj[glAccount], 'id');
    const glAccountName = _get(glAccountObj[glAccount], 'accountName', '') || '';
    return {
      label: `${glAccount} - ${glAccountName}`,
      value: glAccountId,
      data: glAccountObj[glAccount],
      account: glAccount,
    };
  });
  return formatedGlAccountList;
};

export const getAllDeals = state => _get(state, 'vehicleDetails.deals.data.hits', []) || [];

export const getDealCount = state => _get(state, 'vehicleDetails.deals.data.count', 0) || 0;

export const getAllAssigneesAsObject = (allAssignees = []) => {
  const assigneeObject = {};
  allAssignees.forEach(assinee => {
    const { id } = assinee;
    assigneeObject[id] = assinee;
  });
  return assigneeObject;
};

export const getDealsWithAssignees = createSelector([getAllDeals], getFormattedDealsList);

export const getGlAccountDetails = state => _get(state, 'bootstrap.applicableGlAccount', {}) || {};
export const getAllGlAccountDetails = state =>
  _map(_keys(_get(state, 'bootstrap.glAccount', {}) || {}), account =>
    _get(_get(state, 'bootstrap.glAccount'), [account])
  );

export const getAssociatedGlAccountsList = createSelector(
  [getGlAccountDetails, getAllGlAccountDetails, getVehicleDetailsInStore],
  (glAccountDetails, allGlAccounts, vDetails) => {
    const associatedAccounts = _get(glAccountDetails, 'associatedAccounts') || [];
    const accountId = _get(glAccountDetails, 'accountInfo.accountId');
    const existingGlAccountId = _get(vDetails, 'general.accountId');
    const existingGlAccountDetails = _find(allGlAccounts, account => account?.id === existingGlAccountId);
    const associatedGlAccounts = _map(
      _filter(allGlAccounts, glAcc => associatedAccounts.some(data => data.accountId === glAcc.id)),
      acc => ({ ...acc, label: `${_get(acc, 'accountNumber')}-${_get(acc, 'accountName')}` })
    );
    const defaultGlAccount = _isEmpty(existingGlAccountDetails)
      ? _head(
          _map(
            _filter(allGlAccounts, glAcc => glAcc.id === accountId),
            acc => ({ ...acc, label: `${_get(acc, 'accountNumber')}-${_get(acc, 'accountName')}` })
          )
        )
      : {
          ...existingGlAccountDetails,
          label: `${_get(existingGlAccountDetails, 'accountNumber')}-${_get(existingGlAccountDetails, 'accountName')}`,
        };
    if (_isEmpty(_find(associatedGlAccounts, account => account?.id === defaultGlAccount?.id))) {
      associatedGlAccounts.push(defaultGlAccount);
    }
    return {
      associatedGlAccounts,
      defaultGlAccount,
    };
  }
);

export const getViSettingsData = state => tget(state, 'settings.viSettings.settingsData', EMPTY_OBJECT);

export const getCustomerSensitiveAssets = state => tget(state, 'vehicleDetails.customerViewAssets', EMPTY_ARRAY);

export const getCustomerViewProps = createSelector(
  [getViSettingsData, getCustomerSensitiveAssets],
  (viSettingData, customerViewAssets) => ({
    isCustomerViewEnabled: _get(SettingsReader.enableCustomerView(viSettingData), 'enable'),
    customerViewAssets,
  })
);

export const getGeneralSection = state =>
  getFilteredSection(
    ASSET_SUB_TYPES.GENERAL,
    tget(state, 'vehicleDetails.sections', EMPTY_ARRAY),
    tget(state, 'vehicleDetails.fields', EMPTY_ARRAY)
  );

export const getPricingSection = state =>
  getFilteredSection(
    ASSET_SUB_TYPES.PRICING,
    tget(state, 'vehicleDetails.sections', EMPTY_ARRAY),
    tget(state, 'vehicleDetails.fields', EMPTY_ARRAY)
  );

export const getAllFields = state => tget(state, 'vehicleDetails.fields', EMPTY_ARRAY);

export const getGeneralFields = state =>
  getFilteredFields(tget(state, 'vehicleDetails.fields', EMPTY_ARRAY), getGeneralSection(state));

export const getPricingFields = state =>
  getFilteredFields(tget(state, 'vehicleDetails.fields', EMPTY_ARRAY), getPricingSection(state));

export const getEnableStockNumberGenerationOnSave = createSelector(getViSettingsData, viSettingData =>
  _get(SettingsReader.enableStockNumberGenerationOnSave(viSettingData), 'enable')
);
