import React, { useCallback, useEffect, useMemo } from 'react';
import PropTypes from 'prop-types';
import { compose } from 'recompose';

// lodash
import _noop from 'lodash/noop';

// containers
import withActions from 'tcomponents/connectors/withActions';

// components
import Modal from 'tcomponents/molecules/Modal';
import TableManager from 'tcomponents/organisms/TableManager';
import ScheduleRunHistoryHeader from './molecules/scheduleRunHistoryHeader';

// constants
import PARENT_ACTION_TYPES from '../baseContentsList/constants/baseContentsList.actionTypes';
import ACTION_TYPES from './constants/scheduleRunHistoryModal.actionTypes';
import { INITIAL_STATE, TABLE_MANAGER_PROPS } from './constants/scheduleRunHistoryModal.general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from 'tbase/app.constants';

// helpers
import ACTION_HANDLERS from './helpers/scheduleRunHistoryModal.actionHandlers';
import { getTableProps } from './helpers/scheduleRunHistoryModal.general';
import RUN_HISTORY_COLUMNS from './constants/scheduleRunHistoryModal.columns';
import { getFormattedDateTime } from 'tbase/utils/dateUtils';

const ScheduleRunHistoryModal = props => {
  const {
    clickedContentItem,
    onParentAction,
    onAction,
    isScheduleHistoryVisible,
    scheduleRunHistory,
    isloadingRunHistory,
  } = props;

  const onCancel = useCallback(() => {
    onParentAction({
      type: PARENT_ACTION_TYPES.CANCEL_RUN_HISTORY_MODAL,
    });
  }, [onParentAction]);

  useEffect(() => {
    if (!isScheduleHistoryVisible) return;

    onAction({
      type: ACTION_TYPES.FETCH_SCHEDULE_RUN_HISTORY,
    });
  }, EMPTY_ARRAY);

  const tableProps = useMemo(
    () => getTableProps({ tableData: scheduleRunHistory, loading: isloadingRunHistory, onAction }),
    [isloadingRunHistory, onAction, scheduleRunHistory]
  );

  const columns = useMemo(() => RUN_HISTORY_COLUMNS, []);

  return (
    <Modal
      visible={isScheduleHistoryVisible}
      title={__('Monthly Sales Run History')}
      width={Modal.SIZES.MD}
      minWidth={Modal.SIZES.MD}
      centered
      destroyOnClose
      onCancel={onCancel}
      hideSubmit
      secondaryBtnText={__('Cancel')}>
      <ScheduleRunHistoryHeader frequency="Weekly" every={['Mon', 'Thu']} time="1717862400000" />
      <TableManager
        tableProps={tableProps}
        tableManagerProps={TABLE_MANAGER_PROPS}
        columns={columns}
        data={scheduleRunHistory}
        onAction={onAction}
        disableHeight
      />
    </Modal>
  );
};

ScheduleRunHistoryModal.propTypes = {
  clickedContentItem: PropTypes.object,
  onParentAction: PropTypes.func,
  onAction: PropTypes.func,
  isScheduleHistoryVisible: PropTypes.bool,
  scheduleRunHistory: PropTypes.array,
  isloadingRunHistory: PropTypes.bool,
};

ScheduleRunHistoryModal.defaultProps = {
  clickedContentItem: EMPTY_OBJECT,
  onParentAction: _noop,
  onAction: _noop,
  isScheduleHistoryVisible: false,
  scheduleRunHistory: EMPTY_ARRAY,
  isloadingRunHistory: false,
};

export default compose(withActions(INITIAL_STATE, ACTION_HANDLERS))(ScheduleRunHistoryModal);
