import { toMoment } from 'tbase/utils/dateUtils';

export const getFormattedFrequency = (frequency, every) => {
  if (frequency === 'Hourly') {
    return __('Hourly');
  }
  if (frequency === 'Daily') {
    return __('Daily');
  }
  if (frequency === 'Weekly') {
    return __('Weekly - {{every}}', { every: every.join(', ') });
  }
  if (frequency === 'Monthly') {
    return __('Monthly - {{every}}', { every: every.join(', ') });
  }
  return ' -- ';
};

export const getFormattedTime = dateTime => {
  const timestamp = typeof dateTime === 'string' ? parseInt(dateTime, 10) : dateTime;

  const momentObj = toMoment(timestamp);

  const formattedTime = momentObj.format('h:mm a');
  return `. ${formattedTime}`;
};
