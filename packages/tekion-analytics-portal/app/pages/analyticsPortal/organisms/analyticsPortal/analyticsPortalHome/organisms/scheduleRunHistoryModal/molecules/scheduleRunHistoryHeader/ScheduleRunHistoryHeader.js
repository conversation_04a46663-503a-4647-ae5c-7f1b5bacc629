import React from 'react';
import PropTypes from 'prop-types';

// components
import IconWithText from 'tcomponents/atoms/IconWithText';

// helpers
import { getFormattedFrequency, getFormattedTime } from './helpers/scheduleRunHistoryHeader.general';

const ScheduleRunHistoryHeader = props => {
  const { frequency, every, time } = props;

  return (
    <div>
      <span>Schedule: </span>
      <IconWithText
        icon="icon-calendar"
        textContent={getFormattedFrequency(frequency, every)}
        // className={complianceIconClassName}
        iconClassName="mr-0"
      />
      <IconWithText
        icon="icon-clock-in"
        textContent={getFormattedTime(time)}
        // className={complianceIconClassName}
        iconClassName="mr-0"
      />
    </div>
  );
};

ScheduleRunHistoryHeader.propTypes = {
  dateTime: PropTypes.string,
};

ScheduleRunHistoryHeader.defaultProps = {
  dateTime: '',
};

export default ScheduleRunHistoryHeader;
