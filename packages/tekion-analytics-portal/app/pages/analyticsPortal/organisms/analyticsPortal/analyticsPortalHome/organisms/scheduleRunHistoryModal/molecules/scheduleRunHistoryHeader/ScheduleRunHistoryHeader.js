import React from 'react';
import PropTypes from 'prop-types';

// components
import IconWithText from 'tcomponents/atoms/IconWithText';

// constants
import { EMPTY_ARRAY, EMPTY_STRING } from 'tbase/app.constants';

// helpers
import { getFormattedFrequency, getFormattedTime } from './helpers/scheduleRunHistoryHeader.general';

// styles
import styles from './scheduleRunHistoryHeader.module.scss';

const ScheduleRunHistoryHeader = props => {
  const { frequency, every, time } = props;

  return (
    <div className={styles.headerContainer}>
      <span className={styles.scheduleText}>Schedule: </span>
      <IconWithText
        icon="icon-calendar"
        textContent={getFormattedFrequency(frequency, every)}
        className={styles.frequencyText}
        iconClassName="mr-0"
      />
      <IconWithText
        icon="icon-clock-in"
        textContent={getFormattedTime(time)}
        className={styles.timeText}
        iconClassName="mr-0"
      />
    </div>
  );
};

ScheduleRunHistoryHeader.propTypes = {
  frequency: PropTypes.string,
  every: PropTypes.array,
  time: PropTypes.string,
};

ScheduleRunHistoryHeader.defaultProps = {
  frequency: EMPTY_STRING,
  every: EMPTY_ARRAY,
  time: EMPTY_STRING,
};

export default ScheduleRunHistoryHeader;
