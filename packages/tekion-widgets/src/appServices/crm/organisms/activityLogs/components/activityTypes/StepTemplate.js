import React from 'react';
import PropTypes from 'prop-types';
import { withProps } from 'recompose';
import _noop from 'lodash/noop';

// Components
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { ACTIVITY_TYPES } from '../../activityLogs.constants';
import AISummary from '../../organisms/AISummary';
import ActivityTemplateContainer from './ActivityTemplateContainer';
import CallTemplate from './call';
import ChatTranscript from './ChatTranscript';
import CheckInTemplate from './CheckIn';
import CheckInCancelledTemplate from './CheckInCancelled';
import CheckOutTemplate from './CheckOut';
import { LeadCreationTemplate } from './LeadCreation';
import MailTemplate from './mail';
import AssignmentTemplate from './Assignment';
import MarkAsShownTemplate from './MarkAsShown';
import { NoteTemplate } from './Note';
import OwnershipHistoryTemplate from './ownershipHistory';
import StageTemplate from './Stage';
import StatusTemplate from './Status';
import VehicleInterestTemplate from './VehicleInterest';
import FinanceQualification from './FinanceQualification';
import LeadMergedTemplate from './leadMerged';
import AdditionalVehicleInterest from './AdditionalVehicleInterest';
import LeadUnmergedTemplate from './LeadUnmerged';
import BuyerCoBuyerSwitched from './molecules/buyerCoBuyerSwitched';
import QuickQuoteTemplate from './molecules/quickQuoteTemplate';
import QuoteSheet from './molecules/quoteSheet';
import TransactionTemplate from './Transaction';
import TextTemplate from './Text';
import UpcomingTemplate from './upcoming';
import ConciergeTemplate from './Concierge';
import CreditTemplate from './Credit';
import LeadUpdateTemplate from './leadUpdate';
import { OptInOptOut } from './optInOptOut/OptInOptOut';

// Styles
import styles from './activityTypes.module.scss';
import DealEvents from './DealEvents';

const {
  CALL,
  CHAT_TRANSCRIPT,
  CHECK_IN,
  CHECK_IN_CANCELLED,
  MAIL,
  LEAD_CREATION,
  ASSIGNMENT,
  MARK_AS_SHOWN,
  NOTE,
  OWNERSHIP_HISTORY,
  CHECK_OUT,
  STAGE,
  STATUS,
  VEHICLE_INTEREST_CHANGE,
  ADDITIONAL_VEHICLE_INTEREST_CHANGE,
  LEAD_MERGED,
  LEAD_UNMERGED,
  LIVE_CHAT_TRANSCRIPT,
  QUICK_QUOTES,
  CUSTOM_STAGE,
  TEXT,
  TRANSACTION,
  UPCOMING,
  CONCIERGE,
  CREDIT_APPLICATION,
  DEAL_ATTACH,
  DEAL_DETACH,
  DEAL_DELETED,
  BUYER_UPDATE,
  CO_BUYER_UPDATE,
  OWNED_VEHICLE_UPDATE,
  LEAD_QUALIFICATION_STATUS,
  OPT_IN,
  OPT_OUT,
  UNKNOWN,
  OPT_IN_REQUEST_SENT,
  OPT_IN_CONFIRMATION,
  OPT_OUT_CONFIRMATION,
  FINANCE_QUALIFICATION_UPDATE,
  QUOTE_SHEET,
  BUYER_CO_BUYER_SWITCHED,
} = ACTIVITY_TYPES;

const ActivityTemplateMapping = {
  [CALL]: CallTemplate,
  [CHAT_TRANSCRIPT]: ChatTranscript,
  [CHECK_IN]: CheckInTemplate,
  [CHECK_OUT]: CheckOutTemplate,
  [CHECK_IN_CANCELLED]: CheckInCancelledTemplate,
  [CONCIERGE]: ConciergeTemplate,
  [CUSTOM_STAGE]: StageTemplate,
  [MAIL]: MailTemplate,
  [LEAD_CREATION]: LeadCreationTemplate,
  [LIVE_CHAT_TRANSCRIPT]: withProps({ isLiveChatTranscript: true })(ChatTranscript),
  [ASSIGNMENT]: AssignmentTemplate,
  [MARK_AS_SHOWN]: MarkAsShownTemplate,
  [NOTE]: NoteTemplate,
  [OWNERSHIP_HISTORY]: OwnershipHistoryTemplate,
  [STAGE]: StageTemplate,
  [STATUS]: StatusTemplate,
  [VEHICLE_INTEREST_CHANGE]: VehicleInterestTemplate,
  [ADDITIONAL_VEHICLE_INTEREST_CHANGE]: AdditionalVehicleInterest,
  [LEAD_MERGED]: LeadMergedTemplate,
  [LEAD_UNMERGED]: LeadUnmergedTemplate,
  [QUICK_QUOTES]: QuickQuoteTemplate,
  [TEXT]: TextTemplate,
  [TRANSACTION]: TransactionTemplate,
  [UPCOMING]: UpcomingTemplate,
  [CREDIT_APPLICATION]: CreditTemplate,
  [DEAL_ATTACH]: DealEvents,
  [DEAL_DETACH]: DealEvents,
  [DEAL_DELETED]: DealEvents,
  [BUYER_UPDATE]: LeadUpdateTemplate,
  [CO_BUYER_UPDATE]: LeadUpdateTemplate,
  [OWNED_VEHICLE_UPDATE]: LeadUpdateTemplate,
  [LEAD_QUALIFICATION_STATUS]: LeadUpdateTemplate,
  [FINANCE_QUALIFICATION_UPDATE]: FinanceQualification,
  [OPT_IN]: OptInOptOut,
  [OPT_OUT]: OptInOptOut,
  [UNKNOWN]: OptInOptOut,
  [OPT_IN_REQUEST_SENT]: TextTemplate,
  [OPT_IN_CONFIRMATION]: TextTemplate,
  [OPT_OUT_CONFIRMATION]: TextTemplate,
  [QUOTE_SHEET]: QuoteSheet,
  [BUYER_CO_BUYER_SWITCHED]: BuyerCoBuyerSwitched,
};

const StepTemplate = props => {
  const {
    activity,
    actions,
    leadDetails,
    getActivity,
    create,
    refresh,
    onAction,
    users,
    viewType,
    isCustomerOneViewEnabled,
    showChronologicalViewFooter,
    getDealerPropertyValue,
    isTestDriveCrmSetupEnabled,
    settings,
    dealerInfo,
    communicationOptinPreference,
    AISummaryContainerViewType,
    hasAISummaryFeedbackSubmitted,
    updateFilterPreferences,
    paymentOptionConfigs,
    isCTCDisabled,
  } = props;
  const { activityType } = activity;
  const TemplateComponent = ActivityTemplateMapping[activityType] || (() => () => EMPTY_OBJECT);
  return (
    <ActivityTemplateContainer className={`${styles.activityTemplateContainer} margin-bottom-20`}>
      <TemplateComponent
        isCTCDisabled={isCTCDisabled}
        data={activity}
        isCustomerOneViewEnabled={isCustomerOneViewEnabled}
        viewType={viewType}
        showChronologicalViewFooter={showChronologicalViewFooter}
        actions={actions}
        leadDetails={leadDetails}
        getActivity={getActivity}
        create={create}
        refresh={refresh}
        onAction={onAction}
        users={users}
        isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
        getDealerPropertyValue={getDealerPropertyValue}
        settings={settings}
        dealerInfo={dealerInfo}
        communicationOptinPreference={communicationOptinPreference}
        AISummaryContainerViewType={AISummaryContainerViewType}
        hasAISummaryFeedbackSubmitted={hasAISummaryFeedbackSubmitted}
        updateFilterPreferences={updateFilterPreferences}
        paymentOptionConfigs={paymentOptionConfigs}
      />
    </ActivityTemplateContainer>
  );
};

StepTemplate.propTypes = {
  activity: PropTypes.object.isRequired,
  actions: PropTypes.object.isRequired,
  leadDetails: PropTypes.object.isRequired,
  getActivity: PropTypes.func,
  create: PropTypes.func,
  refresh: PropTypes.func,
  onAction: PropTypes.func,
  users: PropTypes.object.isRequired,
  viewType: PropTypes.string.isRequired,
  isCustomerOneViewEnabled: PropTypes.bool.isRequired,
  showChronologicalViewFooter: PropTypes.bool.isRequired,
  getDealerPropertyValue: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  settings: PropTypes.object,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  AISummaryContainerViewType: PropTypes.string,
  hasAISummaryFeedbackSubmitted: PropTypes.bool,
  updateFilterPreferences: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  isCTCDisabled: PropTypes.bool,
};

StepTemplate.defaultProps = {
  getActivity: _noop,
  create: _noop,
  refresh: _noop,
  onAction: _noop,
  getDealerPropertyValue: _noop,
  isTestDriveCrmSetupEnabled: false,
  settings: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  AISummaryContainerViewType: undefined,
  hasAISummaryFeedbackSubmitted: false,
  updateFilterPreferences: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  isCTCDisabled: false,
};

export default React.memo(StepTemplate);
