import React, { useMemo, useState, useCallback, useEffect, useContext } from 'react';
import PropTypes from 'prop-types';
import _isEmpty from 'lodash/isEmpty';
import _head from 'lodash/head';
import _noop from 'lodash/noop';

// Components
import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { millisecondsToSeconds } from '@tekion/tekion-base/utils/dateUtils';
import { hasMakeCall } from '@tekion/tekion-business/src/appServices/crm/permissions/crm.permissions';
import Content from '@tekion/tekion-components/src/atoms/Content';
import Button from '@tekion/tekion-components/src/atoms/Button';
import Label from '@tekion/tekion-components/src/atoms/Label';
import FontIcon from '@tekion/tekion-components/src/atoms/FontIcon';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import isNumberZeroOrNil from '../../../../../utils/isNumberZeroOrNil';
import TextWithReadMore from '../../../../../molecules/textWithReadMore';
import { getCoordinateSelectorInfo } from '../../../../../molecules/coordinateSelector/helpers/coordinateSelector.helpers';
import useCommunicationActions from '../../../../../hooks/useCommunicationActions';
import useCoordinateInfo from '../../../../../hooks/useCoordinateInfo';
import DefaultTemplate from '../DefaultTemplate';
import MergedTag from '../../mergedTag';
import {
  getActivityTypeFromData,
  getCallInfo,
  getFormattedDuration,
  redirectToZultysApp,
} from '../../../activityLogs.helper';
import { ACTIVITY_TYPES_TEMPLATE_VIEW } from '../../../../../constants/leadActivities';
import InlineActions from '../../../../inlineActions';
import { NDCContext } from '../../../../ndc/ndcContextProvider';
import { CAMPAIGN_NAMES } from '../../../constants/lead';
import ICONS from '../../../../../constants/icons';
import { ACTION_TYPES } from '../../../activityLogs.constants';
import { getCoordinateDetails } from '../../../../../helpers/coordinateDetails';
import { isDefaultView } from '../../../helpers/communicationView';
import { zultysConfig, getZultysConfigValues } from './call.config';
import { isInboundCall } from './helpers/call.general';

import {
  CLASS_NAMES_DEFAULT_PROPS,
  CLASS_NAMES_PROP_TYPE,
} from '../../../../../constants/communications/communications.classNames';
import { CallRecordingExternalLink } from '../../../../../molecules/callRecordingExternalLink';
import ActivityDataReader from '../../../../../readers/ActivityData';
import AudioContent from '../../../../audioContent';
import { communicationInfoReader } from '../../../activityLogs.reader';

import s from '../activityTypes.module.scss';

const Call = props => {
  const {
    data,
    view,
    showChronologicalViewFooter,
    classNames,
    onAction,
    leadDetails,
    getDealerPropertyValue,
    dealerInfo,
    communicationOptinPreference,
    mediaPlayerFullWidth,
    isCTCDisabled,
  } = props;

  const activityData = communicationInfoReader.activityData(data);
  const messageId = ActivityDataReader.id(activityData);
  const mediaItems = ActivityDataReader.mediaItems(activityData);
  const fromPhoneCountryCode = ActivityDataReader.fromPhoneCountryCode(activityData);
  const fromPhoneNumber = ActivityDataReader.fromPhoneNumber(activityData);
  const toPhoneCountryCode = ActivityDataReader.toPhoneCountryCode(activityData);
  const toPhoneNumber = ActivityDataReader.toPhoneNumber(activityData);
  const callLogged = ActivityDataReader.callLogged(activityData);
  const duplicateLeadInfo = ActivityDataReader.duplicateLeadInfo(activityData);
  const callDescription = ActivityDataReader.callDescription(activityData);
  const isTranscriptAvailable = ActivityDataReader.isTranscriptAvailable(activityData);
  const duration = ActivityDataReader.duration(activityData);
  const durationInSeconds = millisecondsToSeconds(duration);

  const formattedDuration = getFormattedDuration(durationInSeconds);

  const summary = _head(ActivityDataReader.summaries(activityData));
  const callSource = ActivityDataReader.callSource(activityData);

  const type = getActivityTypeFromData(data);
  const { activitySource } = data;
  const [currentMediaId, setCurrentMediaId] = useState('');
  const { ndcPreferences } = useContext(NDCContext);
  const { getFormattedPhoneNumber } = useTekionConversion();
  const { phoneNumber, countryCode } = getCoordinateDetails({
    type,
    fromPhoneCountryCode,
    fromPhoneNumber,
    toPhoneCountryCode,
    toPhoneNumber,
  });

  useEffect(() => {
    if (!_isEmpty(mediaItems)) {
      setCurrentMediaId(tget(_head(mediaItems), 'id', EMPTY_STRING));
    }
  }, [mediaItems]);

  const onTranscriptNoteClick = useCallback(() => {
    onAction({
      type: ACTION_TYPES.SHOW_TRANSCRIPT_MODAL,
      payload: {
        response: {
          messageId,
        },
      },
    });
  }, [messageId]);

  const renderAudioPlayer = useMemo(() => {
    if (!_isEmpty(currentMediaId)) {
      return (
        <AudioContent
          currentMediaId={currentMediaId}
          isTranscriptAvailable={isTranscriptAvailable}
          OnClickDrawer={onTranscriptNoteClick}
        />
      );
    }
    return <></>;
  }, [currentMediaId, classNames.mediaPlayer, mediaPlayerFullWidth]);

  const showActions = isDefaultView(view) && hasMakeCall() && !callLogged && isInboundCall(data);

  const coordinateInfo = useCoordinateInfo({
    communicationType: ACTION_TYPES.CALL,
    leadDetails,
    phoneNumber,
    countryCode,
    getDealerPropertyValue,
    dealerInfo,
    communicationOptinPreference,
  });
  const { isItemDisabled } = getCoordinateSelectorInfo(coordinateInfo);

  const {
    rightActions,
    onClickAction = _noop,
    kebabActionProps,
    hideKebabMenu,
  } = useCommunicationActions({
    data,
    additionalInfo: {
      onAction,
      actionType: ACTION_TYPES.CALL,
      isItemDisabled,
      phoneNumber,
      countryCode,
      getDealerPropertyValue,
      isCTCDisabled,
      leadDetails,
    },
  });

  const content = source => {
    const text = getCallInfo({ data, config: ndcPreferences, leadDetails, getFormattedPhoneNumber });
    switch (source) {
      case CAMPAIGN_NAMES.ZULTYS:
        return zultysCampaignContent();
      default:
        return (
          <>
            <div className={`flex align-items-center justify-content-between ${s.heading}`}>
              <Content>{text}</Content>
              {callSource && <Content>{__('Call Source: {{callSource}}', { callSource })}</Content>}
            </div>

            {!_isEmpty(summary) && <TextWithReadMore heading={__('Call Summary')} text={summary} />}
            {currentMediaId && renderAudioPlayer}
            {!isNumberZeroOrNil(duration) && <Content>{__(`Duration ${formattedDuration}`)}</Content>}
            {!_isEmpty(callDescription) && <TextWithReadMore heading={__('Note')} text={callDescription} />}
            <div className="flex align-items-center">
              <CallRecordingExternalLink mediaItems={mediaItems} />
              {showActions && (
                <InlineActions
                  isCTCDisabled={isCTCDisabled}
                  className="m-t-8 m-b-8"
                  onClickAction={onClickAction}
                  rightActions={rightActions}
                  kebabActionProps={kebabActionProps}
                  hideKebabMenu={hideKebabMenu}
                />
              )}
            </div>
          </>
        );
    }
  };

  const zultysCampaignContent = useCallback(() => {
    const mediaUrl = tget(_head(mediaItems), 'externalUrl', EMPTY_STRING);
    const zultysValues = getZultysConfigValues({ activityData: data?.activityData });
    return (
      <>
        {zultysConfig.map(({ label, valueKey }) => (
          <PropertyControlledComponent controllerProperty={zultysValues[valueKey]} key={valueKey}>
            <Content className={s.description}>
              {label}
              {zultysValues[valueKey]}
            </Content>
          </PropertyControlledComponent>
        ))}
        <div>
          <Button view={Button.VIEW.TERTIARY} onClick={redirectToZultysApp(mediaUrl)} className={s.button}>
            <Label size={1}>{__('Call Recording')}</Label>
            <FontIcon>{ICONS.ICON_EXPORT}</FontIcon>
          </Button>
        </div>
      </>
    );
  }, [data?.activityData, mediaItems]);

  const responded = useMemo(() => tget(data, 'activityData.responded'), [data]);

  const renderRightContainer = () => (
    <>
      {callLogged && <Content className="m-x-4">({__('Logged')})</Content>}
      <PropertyControlledComponent controllerProperty={responded === false}>
        <div className={`${s.unrespondedLabel} mx-3`}>{__('Unresponded')}</div>
      </PropertyControlledComponent>
      <PropertyControlledComponent controllerProperty={!_isEmpty(duplicateLeadInfo)}>
        <MergedTag data={duplicateLeadInfo} />
      </PropertyControlledComponent>
    </>
  );

  return (
    <DefaultTemplate
      data={data}
      view={view}
      type={type}
      content={content(activitySource?.activitySource)}
      addOnAfterHeading={renderRightContainer}
      showChronologicalViewFooter={showChronologicalViewFooter}
      classNames={classNames}
    />
  );
};

Call.propTypes = {
  data: PropTypes.object.isRequired,
  view: PropTypes.string,
  showChronologicalViewFooter: PropTypes.bool.isRequired,
  classNames: CLASS_NAMES_PROP_TYPE,
  onAction: PropTypes.func,
  leadDetails: PropTypes.object.isRequired,
  getDealerPropertyValue: PropTypes.func,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  mediaPlayerFullWidth: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

Call.defaultProps = {
  view: ACTIVITY_TYPES_TEMPLATE_VIEW.DEFAULT,
  classNames: CLASS_NAMES_DEFAULT_PROPS,
  onAction: _noop,
  getDealerPropertyValue: _noop,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  mediaPlayerFullWidth: false,
  isCTCDisabled: false,
};

export default React.memo(Call);
