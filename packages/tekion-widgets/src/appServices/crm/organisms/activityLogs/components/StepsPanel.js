import React from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _map from 'lodash/map';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import Steps from '@tekion/tekion-components/src/molecules/steps';

import StepTemplate from './activityTypes/StepTemplate';
import StepIcon from './activityTypes/StepIcon';
import NoActivityLogs from './NoActivityLogs';
import { ACTIVITY_TYPES } from '../activityLogs.constants';

const StepsPanel = props => {
  const {
    activityLogs,
    actions,
    leadDetails,
    getActivity,
    create,
    refresh,
    onAction,
    users,
    noActivityConfig,
    children,
    viewType,
    isCustomerOneViewEnabled,
    showChronologicalViewFooter,
    getDealerPropertyValue,
    isTestDriveCrmSetupEnabled,
    settings,
    dealerInfo,
    communicationOptinPreference,
    AISummaryContainerViewType,
    hasAISummaryFeedbackSubmitted,
    updateFilterPreferences,
    stepsPanelContainerClassName,
    paymentOptionConfigs,
    tabKey,
    isCTCDisabled,
  } = props;
  const { Step } = Steps;
  return (
    <div className={cx('d-flex justify-content-center flex-column', stepsPanelContainerClassName)}>
      {activityLogs.length ? (
        <Steps direction="vertical m-t-12">
          {_map(
            activityLogs,
            activity =>
              Object.values(ACTIVITY_TYPES).includes(activity?.activityType) && (
                <Step
                  isCTCDisabled={isCTCDisabled}
                  key={activity?.id}
                  icon={<StepIcon activity={activity} />}
                  template={
                    <StepTemplate
                      isCTCDisabled={isCTCDisabled}
                      isCustomerOneViewEnabled={isCustomerOneViewEnabled}
                      activity={activity}
                      actions={actions}
                      leadDetails={leadDetails}
                      getActivity={getActivity}
                      create={create}
                      refresh={refresh}
                      onAction={onAction}
                      users={users}
                      showChronologicalViewFooter={showChronologicalViewFooter}
                      viewType={viewType}
                      getDealerPropertyValue={getDealerPropertyValue}
                      isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
                      settings={settings}
                      dealerInfo={dealerInfo}
                      communicationOptinPreference={communicationOptinPreference}
                      AISummaryContainerViewType={AISummaryContainerViewType}
                      hasAISummaryFeedbackSubmitted={hasAISummaryFeedbackSubmitted}
                      updateFilterPreferences={updateFilterPreferences}
                      paymentOptionConfigs={paymentOptionConfigs}
                      activeTab={tabKey}
                    />
                  }
                />
              )
          )}
        </Steps>
      ) : (
        <NoActivityLogs {...noActivityConfig} />
      )}
      {children}
    </div>
  );
};

StepsPanel.propTypes = {
  activityLogs: PropTypes.array.isRequired,
  actions: PropTypes.object.isRequired,
  leadDetails: PropTypes.object.isRequired,
  getActivity: PropTypes.func,
  create: PropTypes.func,
  refresh: PropTypes.func,
  onAction: PropTypes.func,
  users: PropTypes.object.isRequired,
  isCustomerOneViewEnabled: PropTypes.bool.isRequired,
  showChronologicalViewFooter: PropTypes.bool.isRequired,
  viewType: PropTypes.string.isRequired,
  noActivityConfig: PropTypes.shape({
    image: PropTypes.string,
    text: PropTypes.string,
  }),
  getDealerPropertyValue: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  settings: PropTypes.object,
  children: PropTypes.node.isRequired,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  AISummaryContainerViewType: PropTypes.string,
  hasAISummaryFeedbackSubmitted: PropTypes.bool,
  updateFilterPreferences: PropTypes.func,
  stepsPanelContainerClassName: PropTypes.string,
  paymentOptionConfigs: PropTypes.array,
  isCTCDisabled: PropTypes.bool,
};

StepsPanel.defaultProps = {
  getActivity: _noop,
  create: _noop,
  refresh: _noop,
  onAction: _noop,
  noActivityConfig: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  isTestDriveCrmSetupEnabled: false,
  settings: EMPTY_OBJECT,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  AISummaryContainerViewType: undefined,
  hasAISummaryFeedbackSubmitted: false,
  updateFilterPreferences: _noop,
  stepsPanelContainerClassName: undefined,
  paymentOptionConfigs: EMPTY_ARRAY,
  isCTCDisabled: false,
};

export default React.memo(StepsPanel);
