import React, { useMemo, useRef, useLayoutEffect, useState } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import NullComponent from '@tekion/tekion-components/src/atoms/nullComponent/NullComponent';
import withSize from '@tekion/tekion-components/src/hoc/withSize';

import LeadGroupedStepsPanel from '../leadGroupedStepsPanel';
import ScrollableStepsPanel from '../ScrollableStepsPanel';
import { VIEW_FILTER_TYPES } from '../../activityLogs.constants';
import {
  isLeadViewSupported,
  isMultiViewSupported,
  shouldShowChronologicalViewFooter,
} from '../../activityLogs.helper';
import { shouldShowAISummary } from '../../helpers/activityLogs.general';

const TabBody = React.memo(props => {
  const {
    render,
    activityLogs,
    actions,
    leadDetails,
    leftActions,
    rightActions,
    create,
    refresh,
    onAction,
    users,
    noActivityConfig,
    tabKey,
    pastLeadsData,
    viewType,
    filter,
    isCustomerOneViewEnabled,
    createBulkActivityLogsPayload,
    createBulkActivityLogsPayloadV2,
    hasMoreActivity,
    isTestDriveCrmSetupEnabled,
    getDealerPropertyValue,
    settings,
    wrapperClassname,
    hideDefaultScrollableList,
    dealerInfo,
    communicationOptinPreference,
    AISummaryContainerViewType,
    hasAISummaryFeedbackSubmitted,
    updateFilterPreferences,
    setHasMoreActivities,
    setActivityLogsGetterFns,
    paymentOptionConfigs,
    isCTCDisabled,
  } = props;
  const showLeadGroupedStepsPanel = useMemo(
    () => isLeadViewSupported(tabKey) && isCustomerOneViewEnabled,
    [tabKey, isCustomerOneViewEnabled]
  );

  const [shouldDisplayRightActionText, setShouldDisplayRightActionText] = useState(true);

  const actionWrapperRef = useRef(null);

  useLayoutEffect(() => {
    if (!actionWrapperRef.current) {
      return;
    }

    const isContentOverflowing = actionWrapperRef.current.scrollWidth > actionWrapperRef.current.clientWidth;

    if (isContentOverflowing) {
      setShouldDisplayRightActionText(false);
      return;
    }

    setShouldDisplayRightActionText(true);
  }, []);

  let Component = ScrollableStepsPanel;
  let showChronologicalViewFooter = false;

  if (showLeadGroupedStepsPanel) {
    showChronologicalViewFooter = shouldShowChronologicalViewFooter(viewType);
    if (isMultiViewSupported(tabKey) && viewType !== VIEW_FILTER_TYPES.LEAD_VIEW) {
      Component = ScrollableStepsPanel;
    } else {
      Component = LeadGroupedStepsPanel;
    }
  }

  if (hideDefaultScrollableList) {
    Component = NullComponent;
  }

  const showAISummary = shouldShowAISummary({ activeTab: tabKey, getDealerPropertyValue, leadDetails });

  return (
    <>
      <div ref={actionWrapperRef} className="d-flex align-items-center justify-content-between p-r-24">
        {leftActions()}
        {rightActions(shouldDisplayRightActionText)}
      </div>
      <div className={cx('p-r-24', wrapperClassname)}>{render()}</div>
      <Component
        isCTCDisabled={isCTCDisabled}
        activityLogs={activityLogs}
        actions={actions}
        leadDetails={leadDetails}
        create={create}
        refresh={refresh}
        onAction={onAction}
        users={users}
        noActivityConfig={noActivityConfig}
        pastLeadsData={pastLeadsData}
        viewType={viewType}
        filter={filter}
        isCustomerOneViewEnabled={isCustomerOneViewEnabled}
        createBulkActivityLogsPayload={createBulkActivityLogsPayload}
        createBulkActivityLogsPayloadV2={createBulkActivityLogsPayloadV2}
        tabKey={tabKey}
        hasMoreActivity={hasMoreActivity}
        showChronologicalViewFooter={showChronologicalViewFooter}
        getDealerPropertyValue={getDealerPropertyValue}
        isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
        settings={settings}
        showAISummary={showAISummary}
        dealerInfo={dealerInfo}
        communicationOptinPreference={communicationOptinPreference}
        AISummaryContainerViewType={AISummaryContainerViewType}
        hasAISummaryFeedbackSubmitted={hasAISummaryFeedbackSubmitted}
        updateFilterPreferences={updateFilterPreferences}
        setHasMoreActivities={setHasMoreActivities}
        setActivityLogsGetterFns={setActivityLogsGetterFns}
        paymentOptionConfigs={paymentOptionConfigs}
      />
    </>
  );
});

TabBody.propTypes = {
  actions: PropTypes.object.isRequired,
  activityLogs: PropTypes.object.isRequired,
  create: PropTypes.func,
  leadDetails: PropTypes.object.isRequired,
  render: PropTypes.func.isRequired,
  leftActions: PropTypes.func,
  rightActions: PropTypes.func,
  refresh: PropTypes.func,
  onAction: PropTypes.func,
  users: PropTypes.object.isRequired,
  noActivityConfig: PropTypes.shape({
    image: PropTypes.string,
    text: PropTypes.string,
  }),
  pastLeadsData: PropTypes.array,
  viewType: PropTypes.string.isRequired,
  createBulkActivityLogsPayload: PropTypes.func,
  createBulkActivityLogsPayloadV2: PropTypes.func,
  hasMoreActivity: PropTypes.bool,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  getDealerPropertyValue: PropTypes.func,
  settings: PropTypes.object,
  wrapperClassname: PropTypes.string,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  AISummaryContainerViewType: PropTypes.string,
  hasAISummaryFeedbackSubmitted: PropTypes.bool,
  updateFilterPreferences: PropTypes.func,
  setHasMoreActivities: PropTypes.func,
  setActivityLogsGetterFns: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  isCTCDisabled: PropTypes.bool,
};

TabBody.defaultProps = {
  create: _noop,
  leftActions: _noop,
  rightActions: _noop,
  refresh: _noop,
  onAction: _noop,
  noActivityConfig: EMPTY_OBJECT,
  pastLeadsData: EMPTY_ARRAY,
  createBulkActivityLogsPayload: _noop,
  createBulkActivityLogsPayloadV2: _noop,
  hasMoreActivity: false,
  isTestDriveCrmSetupEnabled: false,
  getDealerPropertyValue: _noop,
  settings: EMPTY_OBJECT,
  wrapperClassname: undefined,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  AISummaryContainerViewType: undefined,
  hasAISummaryFeedbackSubmitted: false,
  updateFilterPreferences: _noop,
  setHasMoreActivities: _noop,
  setActivityLogsGetterFns: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  isCTCDisabled: false,
};

export default withSize()(TabBody);
