import React, { useCallback, useState, useContext, useEffect, useMemo } from 'react';
import cx from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import isLeadDetailsEnhancementEnabled from '@tekion/tekion-business/src/appServices/crm/helpers/isLeadDetailsEnhancementEnabled';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import withInfiniteScroll from '@tekion/tekion-components/src/molecules/withInfiniteScroll';

import { isLeadViewSupported } from '../activityLogs.helper';
import { VIEW_FILTER_TYPES } from '../activityLogs.constants';
import ActivityLogsContext from '../activityLogs.context';
import { getActivityLogsLoader } from '../helpers/activityLogs.components';
import { setActivityLogsFetchMoreFn, setActivityLogsHasMore } from '../helpers/activityLogs.fetchMoreActivitiesSetters';
import { getStepsPanelComponent } from '../helpers/activityLogs.general';
import StepsPanelWithAISummary from './StepsPanelWithAISummary';

import styles from './scrollableStepsPanel.module.scss';

const InfiniteScrollContainer = withInfiniteScroll()(StepsPanelWithAISummary);

const ScrollableStepsPanel = props => {
  const {
    activityLogs,
    hasMoreActivity,
    leadDetails,
    createBulkActivityLogsPayload,
    createBulkActivityLogsPayloadV2,
    actions,
    viewType,
    getDealerPropertyValue,
    isTestDriveCrmSetupEnabled,
    settings,
    showChronologicalViewFooter,
    showAISummary,
    noActivityConfig,
    dealerInfo,
    communicationOptinPreference,
    setHasMoreActivities,
    setActivityLogsGetterFns,
    paymentOptionConfigs,
    tabKey,
    isCustomerOneViewEnabled,
    isCTCDisabled,
    ...restProps
  } = props;
  const leadDetailsEnhancementEnabled = isLeadDetailsEnhancementEnabled(getDealerPropertyValue);
  const Component = useMemo(
    () => getStepsPanelComponent(getDealerPropertyValue, InfiniteScrollContainer, StepsPanelWithAISummary),
    [getDealerPropertyValue, InfiniteScrollContainer, StepsPanelWithAISummary]
  );
  const [isFetching, setIsFetching] = useState(false);
  const { getPageNo, setPageNo, getNDCPreferences } = useContext(ActivityLogsContext);

  const hasActivityData = _isEmpty(noActivityConfig);

  const fetchMore = useCallback(async () => {
    setIsFetching(true);
    const pageNo = getPageNo();
    const leadId = LeadReader.leadId(leadDetails);
    if (isLeadViewSupported(tabKey) && isCustomerOneViewEnabled) {
      const payload = createBulkActivityLogsPayloadV2(pageNo);
      await actions.getBulkActivityLogsV2(payload, false, {
        chronologicalView: true,
        activityLogs,
        viewType,
        leadId,
        getNDCPreferences,
      });
    } else {
      const payload = createBulkActivityLogsPayload(pageNo);
      await actions.fetchMoreBulkActivities(payload, {
        activeTab: tabKey,
        activityLogs,
        getNDCPreferences,
        leadId,
      });
    }

    setIsFetching(false);
    setPageNo(pageNo + 1);
  }, [
    getPageNo,
    setPageNo,
    createBulkActivityLogsPayload,
    createBulkActivityLogsPayloadV2,
    actions,
    activityLogs,
    getNDCPreferences,
    leadDetails,
    viewType,
    tabKey,
    isCustomerOneViewEnabled,
  ]);

  useEffect(
    () => setActivityLogsFetchMoreFn({ setActivityLogsGetterFns, fetchMore }),
    [setActivityLogsGetterFns, fetchMore]
  );

  useEffect(() => setActivityLogsHasMore({ setHasMoreActivities, hasMoreActivity }), [hasMoreActivity]);

  const loader = getActivityLogsLoader(leadDetailsEnhancementEnabled);

  return (
    <Component
      {...restProps}
      isCTCDisabled={isCTCDisabled}
      activityLogs={activityLogs}
      containerClassName={`full-width overflow-y-auto full-height ${styles.infiniteScroller}`}
      hasMore={hasMoreActivity}
      leadDetails={leadDetails}
      isFetching={isFetching}
      fetchMore={fetchMore}
      viewType={viewType}
      getDealerPropertyValue={getDealerPropertyValue}
      isTestDriveCrmSetupEnabled={isTestDriveCrmSetupEnabled}
      settings={settings}
      showChronologicalViewFooter={showChronologicalViewFooter}
      noActivityConfig={noActivityConfig}
      dealerInfo={dealerInfo}
      communicationOptinPreference={communicationOptinPreference}
      hasActivityData={hasActivityData}
      showAISummary={showAISummary}
      actions={actions}
      stepsPanelContainerClassName={cx({ 'p-r-24': leadDetailsEnhancementEnabled })}
      paymentOptionConfigs={paymentOptionConfigs}>
      <PropertyControlledComponent controllerProperty={isFetching}>{loader}</PropertyControlledComponent>
    </Component>
  );
};

ScrollableStepsPanel.propTypes = {
  onAction: PropTypes.func,
  activityLogs: PropTypes.array,
  createBulkActivityLogsPayload: PropTypes.func,
  createBulkActivityLogsPayloadV2: PropTypes.func,
  actions: PropTypes.object.isRequired,
  hasMoreActivity: PropTypes.bool,
  getDealerPropertyValue: PropTypes.func,
  isTestDriveCrmSetupEnabled: PropTypes.bool,
  leadDetails: PropTypes.object,
  settings: PropTypes.object,
  viewType: PropTypes.string,
  showChronologicalViewFooter: PropTypes.bool,
  showAISummary: PropTypes.bool,
  dealerInfo: PropTypes.object,
  communicationOptinPreference: PropTypes.object,
  setHasMoreActivities: PropTypes.func,
  setActivityLogsGetterFns: PropTypes.func,
  paymentOptionConfigs: PropTypes.array,
  tabKey: PropTypes.string,
  isCustomerOneViewEnabled: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

ScrollableStepsPanel.defaultProps = {
  onAction: _noop,
  activityLogs: EMPTY_ARRAY,
  createBulkActivityLogsPayload: _noop,
  createBulkActivityLogsPayloadV2: _noop,
  hasMoreActivity: false,
  getDealerPropertyValue: _noop,
  isTestDriveCrmSetupEnabled: false,
  leadDetails: EMPTY_OBJECT,
  settings: EMPTY_OBJECT,
  viewType: VIEW_FILTER_TYPES.CHRONOLOGICAL_VIEW,
  showChronologicalViewFooter: false,
  showAISummary: false,
  dealerInfo: EMPTY_OBJECT,
  communicationOptinPreference: EMPTY_OBJECT,
  setHasMoreActivities: _noop,
  setActivityLogsGetterFns: _noop,
  paymentOptionConfigs: EMPTY_ARRAY,
  tabKey: '',
  isCustomerOneViewEnabled: false,
  isCTCDisabled: false,
};

export default React.memo(ScrollableStepsPanel);
