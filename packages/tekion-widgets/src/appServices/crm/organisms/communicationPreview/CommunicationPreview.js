import React, { useEffect, useMemo, useState } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _head from 'lodash/head';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _get from 'lodash/get';

import { EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';
import CommonAPI from '@tekion/tekion-base/services/crm/common.api';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { getCTCDisabled } from '@tekion/tekion-business/src/helpers/crm/communication';
import { getCoordinateToTypeToPrefMap } from '../../molecules/communicationIcon/communicationIcon.helpers';
import MessageReader from '../../readers/message.reader';
import PreviewContent from './components/previewContent';
import PreviewFooter from './components/previewFooter';
import PreviewHeader from './components/previewHeader';
import { ENTITY_VS_API_CONFIG } from './communicationPreview.config';
import { shouldShowPreviewContent } from './helpers/communicationPreview.general';
import { getCommunicationPreference } from '../../helpers/communication';

import sx from './communicationPreview.module.scss';

const CommunicationPreview = ({
  viewByConversation,
  actions,
  selectedCommunicationDetails,
  header: Header,
  previewContainerClass,
  infoBanner,
  isDataRestricted,
  isStopDataProcessing,
  isDeceased,
  aiBdcUser,
  handleOpenDrawer,
  communicationSetupConfig,
}) => {
  const [selectedEntityDetails, setSelectedEntityDetails] = useState(EMPTY_OBJECT);

  const entityIds = useMemo(
    () => _get(selectedCommunicationDetails, 'entityInfo.entityIds'),
    [selectedCommunicationDetails]
  );

  const entityId = MessageReader.entityId(selectedCommunicationDetails);
  const entityType = MessageReader.entityType(selectedCommunicationDetails);
  const showPreviewContent = shouldShowPreviewContent({
    isDataRestricted,
    isStopDataProcessing,
    isDeceased,
  });
  const isCTCDisabled = getCTCDisabled(communicationSetupConfig);

  useEffect(() => {
    actions.toggleViewByConversation(true);
  }, [entityId]);

  useEffect(() => {
    (async () => {
      try {
        if (entityId || !_isEmpty(entityIds)) {
          const selectedEntityId = entityId || _head(entityIds);
          const resp = await tget(ENTITY_VS_API_CONFIG, [entityType, 'api'], 'LEAD')(selectedEntityId);
          const entityData = tget(resp, `${ENTITY_VS_API_CONFIG[entityType].respPath}`, EMPTY_OBJECT);
          if (_isEmpty(entityData) || entityData.error) throw new Error();
          setSelectedEntityDetails(entityData);
          const getPreferencePayloadFunc = tget(ENTITY_VS_API_CONFIG, [entityType, 'makePreferencePayload']);
          if (getPreferencePayloadFunc) {
            const preferencePayload = getPreferencePayloadFunc(entityData);

            const response = await CommonAPI.getCommunicationPreferencesV2(preferencePayload);
            const communicationPreferences = getCommunicationPreference(response);
            setSelectedEntityDetails(prev => ({
              ...prev,
              communicationPreferences,
              coordinateToModeToCommPrefs: getCoordinateToTypeToPrefMap(communicationPreferences),
            }));
          }
        }
      } catch (error) {
        setSelectedEntityDetails(EMPTY_OBJECT);
        toaster(TOASTER_TYPE.ERROR, __('Failed to fetch lead data'));
      }
    })();
  }, [selectedCommunicationDetails]);

  return (
    <div className={cx('full-height d-flex flex-column', sx.container, previewContainerClass)}>
      <Header
        selectedCommunicationDetails={selectedCommunicationDetails}
        viewByConversation={viewByConversation}
        toggleViewByConversation={actions?.toggleViewByConversation}
        selectedEntityDetails={selectedEntityDetails}
        allowActions={ENTITY_VS_API_CONFIG[entityType]?.allowActions}
      />
      {infoBanner}
      <div className="flex-grow-1 relative">
        <div className="absolute inset-0">
          <PreviewContent
            isCTCDisabled={isCTCDisabled}
            viewByConversation={viewByConversation}
            selectedCommunication={selectedCommunicationDetails}
            selectedEntityDetails={selectedEntityDetails}
            allowActions={showPreviewContent}
            aiBdcUser={aiBdcUser}
            handleOpenDrawer={handleOpenDrawer}
          />
        </div>
      </div>
      {!viewByConversation && !_isEmpty(selectedCommunicationDetails) && (
        <PreviewFooter
          selectedCommunicationDetails={selectedCommunicationDetails}
          selectedEntityDetails={selectedEntityDetails}
        />
      )}
    </div>
  );
};

CommunicationPreview.propTypes = {
  viewByConversation: PropTypes.bool.isRequired,
  actions: PropTypes.object.isRequired,
  selectedCommunicationDetails: PropTypes.object.isRequired,
  header: PropTypes.element,
  previewContainerClass: PropTypes.string,
  infoBanner: PropTypes.elementType,
  isDataRestricted: PropTypes.bool,
  aiBdcUser: PropTypes.object,
  handleOpenDrawer: PropTypes.func,
  isStopDataProcessing: PropTypes.bool,
  isDeceased: PropTypes.bool,
  communicationSetupConfig: PropTypes.object,
};

CommunicationPreview.defaultProps = {
  header: PreviewHeader,
  previewContainerClass: EMPTY_STRING,
  infoBanner: null,
  isDataRestricted: false,
  aiBdcUser: undefined,
  handleOpenDrawer: _noop,
  isStopDataProcessing: false,
  isDeceased: false,
  communicationSetupConfig: EMPTY_OBJECT,
};

export default CommunicationPreview;
