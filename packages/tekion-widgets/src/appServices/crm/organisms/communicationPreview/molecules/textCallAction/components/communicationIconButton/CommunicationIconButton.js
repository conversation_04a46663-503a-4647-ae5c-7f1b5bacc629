import React from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import Label from '@tekion/tekion-components/src/atoms/Label/Label';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

const CommunicationIconButton = ({
  className,
  icon,
  isDisabled,
  isMinimized,
  name,
  onFontIconClickHandler,
  leadDetails,
  isCTCDisabled,
}) => {
  const fontSize = isMinimized ? SIZES.S : SIZES.MD_S;

  return (
    <div
      className={cx('d-flex align-items-center', className, {
        'is-disabled': isDisabled,
        'cursor-pointer': !isDisabled,
      })}>
      <FontIcon onClick={onFontIconClickHandler(isCTCDisabled, leadDetails)} size={fontSize} disabled={isDisabled}>
        {icon}
      </FontIcon>
      {name && !isMinimized && <Label className="m-l-8">{name}</Label>}
    </div>
  );
};

CommunicationIconButton.defaultProps = {
  className: undefined,
  onFontIconClickHandler: _noop,
  leadDetails: EMPTY_OBJECT,
  isCTCDisabled: false,
};

CommunicationIconButton.propTypes = {
  className: PropTypes.string,
  icon: PropTypes.string.isRequired,
  isDisabled: PropTypes.bool.isRequired,
  isMinimized: PropTypes.bool.isRequired,
  name: PropTypes.string.isRequired,
  onFontIconClickHandler: PropTypes.func,
  leadDetails: PropTypes.object,
  isCTCDisabled: PropTypes.bool,
};

export default CommunicationIconButton;
