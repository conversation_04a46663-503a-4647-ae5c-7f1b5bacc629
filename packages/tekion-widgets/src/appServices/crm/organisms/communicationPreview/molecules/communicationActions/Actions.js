import React, { useCallback, useMemo, useState } from 'react';
import PropTypes from 'prop-types';
import _forEach from 'lodash/forEach';
import _map from 'lodash/map';
import _noop from 'lodash/noop';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import useEmailActions from '../../../../../../customHooks/templateBuilder/useEmailActions';
import MessageReader from '../../../../readers/message.reader';
import { ACTIONS_CONFIG, KEBAB_ACTION } from './actions.constants';
import sx from './actions.module.scss';
import { makeEmailAdditionalConfig } from './actions.helper';

export const renderItems = (config, store, showDivider = true, isMinimized = false, extraData) => {
  _forEach(config, (item, index) => {
    if (Array.isArray(item)) {
      renderItems(item, store, false, isMinimized, extraData);
    } else {
      const { renderer: Component, action, menuItems, ...rest } = item;
      let updatedMenuList = menuItems;
      if (action === KEBAB_ACTION) {
        updatedMenuList = _map(menuItems, menuitem => ({ ...menuitem, ...extraData }));
      }
      if (showDivider && index > 0 && !isMinimized) store.push(<div className={sx.divider}></div>);
      store.push(
        <Component
          action={action}
          isMinimized={isMinimized}
          {...rest}
          {...extraData}
          className={sx.actionItem}
          menuItems={updatedMenuList}
        />
      );
    }
  });
};

const Actions = ({ type, message, selectedEntityDetails, isMinimized = false, isCTCDisabled }) => {
  const config = useMemo(() => ACTIONS_CONFIG[type], [type]);

  const [isKebabMenuVisible, setIsKebabMenuMenuVisible] = useState(false);

  const handleKebabManuVisibility = useCallback(() => {
    setIsKebabMenuMenuVisible(!isKebabMenuVisible);
  }, [isKebabMenuVisible]);

  const [setTrigger] = useEmailActions({
    message,
    messageReader: MessageReader,
    additionalConfig: makeEmailAdditionalConfig(message),
  });

  const onEmailAction = useCallback(action => setTrigger(action), [setTrigger]);

  const communicationActionsJSX = useMemo(() => {
    const store = [];
    renderItems(config, store, true, isMinimized, {
      entityDetails: selectedEntityDetails,
      onAction: onEmailAction,
      onVisibleChange: handleKebabManuVisibility,
      onClickAction: _noop,
      isVisible: isKebabMenuVisible,
      isCTCDisabled,
    });
    return store;
  }, [
    config,
    selectedEntityDetails,
    isMinimized,
    isKebabMenuVisible,
    onEmailAction,
    handleKebabManuVisibility,
    isCTCDisabled,
  ]);

  return isMinimized ? (
    communicationActionsJSX
  ) : (
    <div className={`d-flex justify-content-around ${sx.container}`}>{communicationActionsJSX}</div>
  );
};

Actions.propTypes = {
  type: PropTypes.string,
  message: PropTypes.string,
  selectedEntityDetails: PropTypes.object,
  isMinimized: PropTypes.bool.isRequired,
  isCTCDisabled: PropTypes.bool,
};

Actions.defaultProps = {
  type: undefined,
  message: undefined,
  selectedEntityDetails: EMPTY_OBJECT,
  isCTCDisabled: false,
};

export default Actions;
