import React, { useMemo } from 'react';
import PropTypes from 'prop-types';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { tget } from '@tekion/tekion-base/utils/general';

import { useTekionConversion } from '@tekion/tekion-conversion-web';
import usePermissions from '../../../../../../customHooks/usePermissions';
import CommunicationIcon from '../../../../molecules/communicationIcon';
import { ALLOWED_PERMISSIONS_MAP } from '../../../../molecules/communicationIcon/communicationIcon.constants';
import { onClickLog, isLogTaskAllowed } from '../../../../helpers/callLog';

import CommunicationIconButton from './components/communicationIconButton';

const CommunicationActions = ({
  action,
  secondaryAction,
  selectedCommunicationDetails,
  icon,
  name,
  entityDetails,
  isMinimized,
  className,
  isCTCDisabled,
}) => {
  const phoneNumber = tget(selectedCommunicationDetails, 'communicationData.customerPhoneNumber');
  const communicationAction = secondaryAction || action;
  const hasPermission = usePermissions({ validFor: ALLOWED_PERMISSIONS_MAP[communicationAction] });
  const { getFormattedPhoneNumber } = useTekionConversion();

  const commIconButtonProps = useMemo(
    () => ({
      isMinimized,
      name,
      icon,
      className,
      isCTCDisabled,
    }),
    [isMinimized, name, icon, className, isCTCDisabled]
  );

  const onFontIconClickHandler = (isCTCDisabled, leadDetails) => () => {
    if (isLogTaskAllowed(communicationAction, isCTCDisabled)) {
      onClickLog({ leadDetails, getFormattedPhoneNumber });
    }
  };

  return hasPermission ? (
    <CommunicationIcon
      isCTCDisabled={isCTCDisabled}
      key={communicationAction}
      communicationType={communicationAction}
      leadDetails={entityDetails}
      phoneNumber={phoneNumber}
      render={({ isDisabled }) => (
        <CommunicationIconButton
          {...commIconButtonProps}
          isDisabled={isDisabled}
          onFontIconClickHandler={onFontIconClickHandler}
          leadDetails={entityDetails}
        />
      )}
    />
  ) : (
    <CommunicationIconButton {...commIconButtonProps} isDisabled />
  );
};

CommunicationActions.propTypes = {
  action: PropTypes.string,
  secondaryAction: PropTypes.string,
  selectedCommunicationDetails: PropTypes.object,
  icon: PropTypes.string,
  name: PropTypes.string,
  entityDetails: PropTypes.object,
  isMinimized: PropTypes.bool.isRequired,
  className: PropTypes.string,
  isCTCDisabled: PropTypes.bool,
};

CommunicationActions.defaultProps = {
  action: undefined,
  secondaryAction: undefined,
  selectedCommunicationDetails: EMPTY_OBJECT,
  className: undefined,
  icon: undefined,
  name: undefined,
  entityDetails: EMPTY_OBJECT,
  isCTCDisabled: false,
};

export default CommunicationActions;
