import React, { useState, useEffect, useCallback, useRef } from 'react';
import cx from 'classnames';
import PropTypes from 'prop-types';
import _debounce from 'lodash/debounce';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';

import { tget } from '@tekion/tekion-base/utils/general';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import Loader from '@tekion/tekion-components/src/molecules/loader';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { useTekionConversion } from '@tekion/tekion-conversion-web';
import resolver from '@tekion/tekion-base/bulkResolvers';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import { getMessageTimeToDisplay } from '../../../../../../organisms/communications/helpers/communications.messages';
import TOASTER_MESSAGES from '../../../../../../organisms/communications/constants/communications.toasterMessages';
import COMMUNICATION_HUB_ENTITY from '../../../../../../organisms/communications/nonEnterpriseCommunication/resolvers/communicationHubEntity';

import MessageReader from '../../../../readers/message.reader';
import CommunicationHubApi from '../../../../services/communicationHub';
import { TYPE_VS_COMPONENT } from './previewContent.config';
import {
  getDayWiseCommunication,
  getCommunicationsPayload,
  mergeCommunications,
  scrollToBottom,
  getInboundState,
  getMessageCount,
} from './previewContent.helpers';
import { SCROLL_OFFSET } from './previewContent.constants';
import TileFooter from './components/tileFooter';
import ListView from './components/listView/ListView';

import sx from './previewContent.module.scss';

const renderDayTimeRow = (time, getFormattedDateAndTime) => (
  <div className={`text-center pb-4 ${sx.dayTimeRow}`}>{getMessageTimeToDisplay(time, getFormattedDateAndTime)}</div>
);

const PreviewContent = ({
  selectedCommunication,
  viewByConversation = true,
  selectedEntityDetails,
  allowActions = true,
  aiBdcUser,
  handleOpenDrawer,
  isCTCDisabled,
}) => {
  const [listData, setListData] = useState(new Map()); // used for viewByConversation view
  const [showLoader, setShowLoader] = useState(false);
  const [loadingMessages, setLoadingMessages] = useState(false);
  const previewContentRef = useRef();
  const bottomDivRef = useRef();
  const page = useRef(0); // page = num of sets of messages received
  const cancelCall = useRef(false);
  const totalMessagesCount = useRef(0);
  const entityId = MessageReader.entityId(selectedCommunication);
  const aiBdcMetadata = LeadReader.aiBdcMetadata(selectedEntityDetails);

  const fetchCommunicationsList = async () => {
    setShowLoader(true);
    const apiPayload = getCommunicationsPayload(selectedCommunication);
    try {
      const response = await CommunicationHubApi.fetchCommunications(apiPayload);
      if (!response || response.error) throw new Error();
      const count = tget(response, 'count', 0);
      const hits = tget(response, 'hits', EMPTY_ARRAY);
      const resolvedResponse = await resolver.getResolvedData(undefined, hits, {
        entityAdditionalProps: COMMUNICATION_HUB_ENTITY,
      });
      totalMessagesCount.current = count;
      setListData(getDayWiseCommunication(resolvedResponse));
    } catch (e) {
      toaster(TOASTER_TYPE.ERROR, TOASTER_MESSAGES.CONVERSATION_FETCH_ERROR);
    }

    setShowLoader(false);
    scrollToBottom(bottomDivRef.current);
  };

  const onScrollAtTop = useCallback(async () => {
    if (cancelCall.current) return;
    const prevContainerHeight = previewContentRef.current?.scrollHeight;
    page.current += 1;
    setLoadingMessages(true);

    const apiPayload = getCommunicationsPayload(selectedCommunication, page.current);
    try {
      const response = await CommunicationHubApi.fetchCommunications(apiPayload);
      const resolvedResponse = await resolver.getResolvedData(undefined, tget(response, 'hits', EMPTY_ARRAY), {
        entityAdditionalProps: COMMUNICATION_HUB_ENTITY,
      });
      const newCommunicationMap = getDayWiseCommunication(resolvedResponse);
      setListData(prevMap => mergeCommunications(newCommunicationMap, prevMap));
    } catch (e) {
      toaster(TOASTER_TYPE.ERROR, TOASTER_MESSAGES.CONVERSATION_FETCH_ERROR);
    }

    setLoadingMessages(false);
    requestAnimationFrame(() => {
      if (previewContentRef.current) {
        const difference = previewContentRef.current.scrollHeight - prevContainerHeight;
        previewContentRef.current.scrollTop = difference - SCROLL_OFFSET;
      }
    });
  }, [entityId, selectedCommunication]);

  const handleScroll = useCallback(
    _debounce(e => {
      const element = e.target;
      if (element === previewContentRef.current && element.scrollTop <= SCROLL_OFFSET) {
        onScrollAtTop();
      }
    }, 300),
    [onScrollAtTop]
  );
  const { getFormattedDateAndTime } = useTekionConversion();
  useEffect(() => {
    page.current = 0;
    totalMessagesCount.current = 0;
    setListData(new Map());
    if (viewByConversation) {
      setShowLoader(true);
      fetchCommunicationsList();
    } else {
      setShowLoader(false);
      setLoadingMessages(false);
    }
  }, [entityId]);

  useEffect(() => {
    if (viewByConversation && previewContentRef.current) {
      const allReceivedMsgsCount = getMessageCount(listData);
      if (totalMessagesCount.current > allReceivedMsgsCount) {
        previewContentRef.current.addEventListener('scroll', handleScroll);
      }
    }
    return () => {
      if (previewContentRef.current) previewContentRef.current.removeEventListener('scroll', handleScroll);
    };
  }, [handleScroll, viewByConversation, listData]);

  useEffect(() => {
    cancelCall.current = !viewByConversation;
    if (viewByConversation) {
      if (listData.size > 0) {
        // if already list data is present for lead
        scrollToBottom(bottomDivRef.current);
        return;
      }

      fetchCommunicationsList();
    } else {
      // reset subscription and state/ref.
      setLoadingMessages(false);
    }
  }, [viewByConversation]);

  const renderConversation = () => {
    const { type } = selectedCommunication;
    const Component = TYPE_VS_COMPONENT[type];
    const isInbound = getInboundState(selectedCommunication);
    return (
      <div className={cx('full-height overflow-auto', sx.container)}>
        {renderDayTimeRow(MessageReader.createdTime(selectedCommunication), getFormattedDateAndTime)}
        <div
          className={cx(sx.tileContainer, 'pb-4', {
            'ml-auto': !isInbound,
            'mr-auto': isInbound,
          })}
        >
          <Component
            isCTCDisabled={isCTCDisabled}
            viewByConversation={viewByConversation}
            message={selectedCommunication}
            key={selectedCommunication.id}
            isInbound={isInbound}
            handleOpenDrawer={handleOpenDrawer}
          />
          <TileFooter
            message={selectedCommunication}
            isInbound={isInbound}
            viewByConversation={viewByConversation}
            aiBdcMetadata={aiBdcMetadata}
            aiBdcUser={aiBdcUser}
          />
        </div>
      </div>
    );
  };

  if (_isEmpty(selectedCommunication)) {
    return <div className="flex-center mt-2">{__('No Preview Found!')}</div>;
  }

  return (
    <div className={cx('full-height overflow-auto', sx.content)} ref={previewContentRef}>
      {viewByConversation && loadingMessages && <Loader className={sx.msgLoader} />}
      <PropertyControlledComponent controllerProperty={!showLoader} fallback={<Loader />}>
        {viewByConversation ? (
          <ListView
            isCTCDisabled={isCTCDisabled}
            previewData={listData}
            viewByConversation={viewByConversation}
            selectedEntityDetails={selectedEntityDetails}
            allowActions={allowActions}
            aiBdcMetadata={aiBdcMetadata}
            aiBdcUser={aiBdcUser}
            handleOpenDrawer={handleOpenDrawer}
          />
        ) : (
          renderConversation()
        )}
      </PropertyControlledComponent>
      <div ref={bottomDivRef} />
    </div>
  );
};

PreviewContent.propTypes = {
  selectedCommunication: PropTypes.object.isRequired,
  viewByConversation: PropTypes.object.isRequired,
  allowActions: PropTypes.bool.isRequired,
  selectedEntityDetails: PropTypes.object,
  aiBdcUser: PropTypes.object,
  handleOpenDrawer: PropTypes.func,
  isCTCDisabled: PropTypes.bool,
};

PreviewContent.defaultProps = {
  selectedEntityDetails: EMPTY_OBJECT,
  aiBdcUser: undefined,
  handleOpenDrawer: _noop,
  isCTCDisabled: false,
};

export default PreviewContent;
