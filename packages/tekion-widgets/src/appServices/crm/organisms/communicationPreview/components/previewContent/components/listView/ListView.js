import React, { useState } from 'react';
import cx from 'classnames';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';
import PermanentMessageFailure from '@tekion/tekion-widgets/src/organisms/communications/molecules/permanentMessageFailure';

import { checkFailedCommunication } from '../../../../../activityLogs/constraints/communicationActivity';
import MessageReader from '../../../../../../readers/message.reader';
import MessageFAB from '../../../../molecules/communicationActions'; // Floating Action Buttons
import TileFooter from '../tileFooter';
import { TYPE_VS_COMPONENT } from '../../previewContent.config';
import { getInboundState } from '../../previewContent.helpers';

import sx from './listView.module.scss';

const ListView = ({
  previewData,
  viewByConversation,
  selectedEntityDetails,
  allowActions,
  aiBdcMetadata,
  aiBdcUser,
  handleOpenDrawer,
  isCTCDisabled,
}) => {
  const allDays = [...previewData.entries()];
  const [selectedMessageId, setSelectedMessageId] = useState('');
  const [showActions, setShowActions] = useState(false);

  const displayActions = id => showActions && selectedMessageId === id && viewByConversation;

  const handleShowActions = id => () => {
    setShowActions(true);
    setSelectedMessageId(id);
  };

  const handleHideActions = () => {
    setShowActions(false);
    setSelectedMessageId('');
  };

  if (_isEmpty(allDays)) return <div className="flex-center mt-2">{__('No Record Found!')}</div>;
  return _map(allDays, ([key, messageList]) => {
    if (_isEmpty(messageList)) return null;
    return (
      <>
        <div key={key} className={`text-center pt-1 pb-2 ${sx.dayTimeRow}`}>
          {key}
        </div>
        {_map(messageList, message => {
          const { id } = message;
          const type = MessageReader.type(message);
          const Component = TYPE_VS_COMPONENT[type];
          const isInbound = getInboundState(message);

          const failedMessageData = checkFailedCommunication(message?.communicationData);

          return (
            <div
              className={cx(sx.container, {
                [sx.bgGray]: viewByConversation,
              })}
              onMouseOver={handleShowActions(id)}
              onMouseLeave={handleHideActions}
              onFocus={_noop}
              key={id}
            >
              <div
                className={cx('flex align-items-center', {
                  'justify-content-end': !isInbound,
                  [sx.rowReverse]: isInbound,
                })}
              >
                <div
                  className={cx(sx.actionContainer, 'mx-3 flex align-items-center justify-content-around', {
                    'd-none': !displayActions(id),
                  })}
                >
                  <PropertyControlledComponent controllerProperty={allowActions}>
                    <MessageFAB
                      isCTCDisabled={isCTCDisabled}
                      message={message}
                      type={type}
                      selectedEntityDetails={selectedEntityDetails}
                      isMinimized
                    />
                  </PropertyControlledComponent>
                </div>
                <div className={`d-flex ${sx.tileContainer}`}>
                  <Component
                    viewByConversation={viewByConversation}
                    message={message}
                    key={message.id}
                    isInbound={isInbound}
                    showActions={showActions}
                    handleOpenDrawer={handleOpenDrawer}
                  />
                </div>
              </div>
              <TileFooter
                message={message}
                isInbound={isInbound}
                viewByConversation={viewByConversation}
                aiBdcMetadata={aiBdcMetadata}
                aiBdcUser={aiBdcUser}
              />
              <div className="d-flex flex-row-reverse">
                <PropertyControlledComponent controllerProperty={failedMessageData?.isFailedCommunication}>
                  <PermanentMessageFailure failedMessageData={failedMessageData} />
                </PropertyControlledComponent>
              </div>
            </div>
          );
        })}
      </>
    );
  });
};

export default ListView;
