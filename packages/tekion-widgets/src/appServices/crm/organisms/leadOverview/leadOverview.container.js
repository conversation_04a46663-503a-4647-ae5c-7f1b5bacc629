import React from 'react';
import { compose } from 'recompose';

import withActions from '@tekion/tekion-components/src/connectors/withActions';
import withPropertyConsumer from '@tekion/tekion-components/src/organisms/propertyProvider/withPropertyConsumer';

import { withTekionConversion } from '@tekion/tekion-conversion-web';
import withAiBdcUser from '../../../../hocs/withAiBdcUser';
import withPusherEventHandler from '../../../../hocs/withPusherEventHandler/withPusherEventHandler';
import WithMetadataContext from '../../hocs/withMetadata/WithMetadataContext';
import LeadOverview from './LeadOverview';
import ACTION_HANDLERS from './leadOverview.actionHandlers';
import { INITIAL_STATE } from './leadOverview.constants';
import { withNDC } from '../ndc';
import { CommunicationSetupConfigProvider } from '../../communicationSetup';

function LeadOverviewContainer(props) {
  return (
    <CommunicationSetupConfigProvider>
      <LeadOverview {...props} />
    </CommunicationSetupConfigProvider>
  );
}

export default compose(
  withPropertyConsumer,
  WithMetadataContext,
  withPusherEventHandler,
  withNDC,
  withTekionConversion,
  withActions(INITIAL_STATE, ACTION_HANDLERS),
  withAiBdcUser
)(LeadOverviewContainer);
