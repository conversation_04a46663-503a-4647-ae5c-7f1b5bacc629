import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import { withProps } from 'recompose';
import _noop from 'lodash/noop';

import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import Divider from '@tekion/tekion-components/src/atoms/Divider';

import crmSetupApi from '@tekion/tekion-business/src/services/crm/crmSetup.api';
import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import useAIBdcUser from '../../../../../../hooks/useAiBdcUser';
import useFetchData from '../../../../hooks/useFetchData';
import CommunicationPreview from '../../../communicationPreview';
import CommunicationThreadHeader from './components/CommunicationThreadHeader';

import styles from '../../leadOverview.module.scss';

const CommunicationThread = props => {
  const { entityData, onBackClick, getDealerPropertyValue, handleOpenDrawer } = props;

  const isAiBdcFeatureEnabled = getDealerPropertyValue(DEALER_PROPERTIES.CRM_AI_BDC_ENABLED);
  const { data: communicationSetupConfig = EMPTY_OBJECT } = useFetchData({
    fetchData: crmSetupApi.fetchCommunicationSetup,
    failureMessage: __('Failed to fetch communication setup configuration'),
  });

  const { aiBdcUser } = useAIBdcUser(getDealerPropertyValue, isAiBdcFeatureEnabled);
  const Header = useMemo(() => withProps({ onBackClick })(CommunicationThreadHeader), [onBackClick]);
  const actions = useMemo(() => ({ toggleViewByConversation: _noop }), []);

  const { entityInfo: leadDetails } = entityData;
  const isDataRestricted = LeadReader.dataRestrict(leadDetails);

  return (
    <>
      <Divider className="m-t-16 m-b-16" />
      <CommunicationPreview
        communicationSetupConfig={communicationSetupConfig}
        header={Header}
        viewByConversation
        previewContainerClass={`p-x-24 ${styles.previewContainerClass}`}
        actions={actions}
        selectedCommunicationDetails={entityData}
        isDataRestricted={isDataRestricted}
        aiBdcUser={aiBdcUser}
        handleOpenDrawer={handleOpenDrawer}
      />
    </>
  );
};

CommunicationThread.propTypes = {
  entityData: PropTypes.object.isRequired,
  onBackClick: PropTypes.func,
  getDealerPropertyValue: PropTypes.func,
  handleOpenDrawer: PropTypes.func,
};

CommunicationThread.defaultProps = {
  onBackClick: _noop,
  getDealerPropertyValue: _noop,
  handleOpenDrawer: _noop,
};

export default memo(CommunicationThread);
