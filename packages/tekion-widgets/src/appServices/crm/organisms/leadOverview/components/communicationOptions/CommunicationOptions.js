import React, { memo, useMemo } from 'react';
import PropTypes from 'prop-types';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import { compose } from 'recompose';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getCTCDisabled } from '@tekion/tekion-business/src/helpers/crm/communication';
import { getCommunicationModeConfig } from './communicationOptions.helpers';
import CommunicationAction from './components/CommunicationAction';
import { withCommunicationSetupConsumer } from '../../../../communicationSetup';

import styles from './communicationOptions.module.scss';

const CommunicationOptions = ({ leadDetails, onClickLog, communicationSetupConfig }) => {
  const isCTCDisabled = getCTCDisabled(communicationSetupConfig);
  const actionTypesConfig = useMemo(
    () =>
      getCommunicationModeConfig({
        leadDetails,
        onClickLog,
        isCTCDisabled,
      }),
    [leadDetails, onClickLog, isCTCDisabled]
  );

  return (
    <div className={`${styles.container} m-b-8`}>
      {_map(actionTypesConfig, ({ id, renderProps }) => (
        <CommunicationAction key={id} type={id} leadDetails={leadDetails} {...renderProps} />
      ))}
    </div>
  );
};

CommunicationOptions.propTypes = {
  leadDetails: PropTypes.object.isRequired,
  onClickLog: PropTypes.func,
  communicationSetupConfig: PropTypes.object,
};

CommunicationOptions.defaultProps = {
  onClickLog: _noop,
  communicationSetupConfig: EMPTY_OBJECT,
};

export default compose(withCommunicationSetupConsumer, memo)(CommunicationOptions);
