import React, { memo, useCallback } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import { EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';

import CommunicationIcon from '../../../../../molecules/communicationIcon';
import CommunicationButton from './CommunicationButton';
import { isLogTaskAllowed } from '../communicationOptions.helpers';

import styles from '../communicationOptions.module.scss';
import { ACTION_TYPES } from '../../../../activityLogs/activityLogs.constants';

const CommunicationAction = ({ type, name, icon, leadDetails, isCTCDisabled, onClickLog }) => {
  const isDataRestricted = LeadReader.dataRestrict(leadDetails);

  const handleOnCLick = useCallback(() => {
    if (isLogTaskAllowed(type, isCTCDisabled)) {
      onClickLog(ACTION_TYPES.LOG_CALL);
    }
  }, [onClickLog, isCTCDisabled, type]);

  return (
    <CommunicationIcon
      isCTCDisabled={isCTCDisabled}
      communicationType={type}
      leadDetails={leadDetails}
      render={({ isDisabled }) => (
        <CommunicationButton
          icon={icon}
          label={name}
          className={styles.communicationButton}
          disabled={isDisabled}
          onClick={handleOnCLick}
        />
      )}
      disabled={isDataRestricted}
    />
  );
};

CommunicationAction.propTypes = {
  type: PropTypes.string.isRequired,
  name: PropTypes.string,
  icon: PropTypes.string.isRequired,
  leadDetails: PropTypes.object.isRequired,
  isCTCDisabled: PropTypes.bool,
  onClickLog: PropTypes.func,
};

CommunicationAction.defaultProps = {
  name: EMPTY_STRING,
  isCTCDisabled: false,
  onClickLog: _noop,
};

export default memo(CommunicationAction);
