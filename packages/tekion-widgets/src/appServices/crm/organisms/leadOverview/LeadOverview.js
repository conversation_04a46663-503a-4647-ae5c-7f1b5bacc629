/* NOTE:
  It should be used only in CRM, since it has dependency with crm related metadata.
  Make sure to wrap the crmSetupProvider in parent component
*/
import React, { memo, PureComponent } from 'react';
import PropTypes from 'prop-types';
import { defaultMemoize } from 'reselect';
import compose from 'recompose/compose';

// Lodash
import _noop from 'lodash/noop';
// Components
import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';

import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import { tget } from '@tekion/tekion-base/utils/general';
import PropertyControlledComponentWithFallback from '@tekion/tekion-components/src/molecules/PropertyControlledComponentWithFallback';
import { withCommunicationSetupConsumer } from '../../communicationSetup';

import CallDetails from '../callDetails';
import renderUserOrCampaignData from '../userRender/renderUserOrCampaignData';

import CommunicationThread from './components/communicationThread';
import LeadOverviewHeader from './components/leadOverviewHeader';
import TabPanel from './components/tabPanel';
import {
  DEFAULT_ACTIVITY_PAGE_NO,
  LEAD_OVERVIEW_ACTION_TYPES as ACTION_TYPES,
  OVERVIEW_TAB_IDS,
} from './leadOverview.constants';
import { getLeadEntityData } from './leadOverview.helper';
import { getLeadOverviewTabsConfigList, TABS_CONFIG } from './leadOverview.tabsConfig';

// Styles
import styles from './leadOverview.module.scss';

const INITIAL_STATE = {
  visible: false,
  lead: EMPTY_OBJECT,
  leadData: EMPTY_OBJECT,
  users: EMPTY_OBJECT,
  pageNo: DEFAULT_ACTIVITY_PAGE_NO + 1,
};

class LeadOverview extends PureComponent {
  constructor(props) {
    super(props);
    props.subscribePusher(this);
    this.state = INITIAL_STATE;
  }

  componentDidMount() {
    const { initialLead, onAction } = this.props;

    if (initialLead && LeadReader.leadId(initialLead)) {
      onAction({ type: ACTION_TYPES.HANDLE_FETCH_LEAD_OVERVIEW, payload: { lead: initialLead } });
    }

    onAction({ type: ACTION_TYPES.HANDLE_PUSH_SUBSCRIBE });
  }

  componentDidUpdate(prevProps) {
    const { initialLead, onAction } = this.props;

    if (
      LeadReader.leadId(initialLead) !== LeadReader.leadId(prevProps.initialLead) ||
      LeadReader.modifiedTime(initialLead) !== LeadReader.modifiedTime(prevProps.initialLead)
    ) {
      onAction({ type: ACTION_TYPES.HANDLE_FETCH_LEAD_OVERVIEW, payload: { lead: initialLead } });
      this.setPageNo(DEFAULT_ACTIVITY_PAGE_NO);
    }
  }

  componentWillUnmount() {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.HANDLE_PUSH_UNSUBSCRIBE });
  }

  setPageNo = pageNo => {
    this.setState({ pageNo });
  };

  getLeadOverviewTabsConfigMemoized = defaultMemoize(getLeadOverviewTabsConfigList);

  handleRefreshOverview = () => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.HANDLE_REFRESH_LEAD_OVERVIEW });
  };

  handleActivityLogsRefresh = () => {
    const { onAction, activeTab } = this.props;
    if (activeTab === OVERVIEW_TAB_IDS.OVERVIEW) this.handleRefreshOverview();
    else {
      onAction({ type: ACTION_TYPES.HANDLE_FETCH_ACTIVITY_LOGS, payload: { tab: activeTab } });
      this.setPageNo(DEFAULT_ACTIVITY_PAGE_NO);
    }
  };

  handleFetchMoreActivityLogs = () => {
    const { pageNo } = this.state;
    const { onAction, activeTab } = this.props;
    onAction({ type: ACTION_TYPES.HANDLE_FETCH_ACTIVITY_LOGS, payload: { tab: activeTab, pageNo } });
    this.setPageNo(pageNo + 1);
  };

  handleCommunicationThreadBackClick = () => {
    const { onAction } = this.props;
    onAction({ type: ACTION_TYPES.HANDLE_COMMUNICATION_VIEW_THREAD, payload: { visible: false } });
    this.handleActivityLogsRefresh();
  };

  onTabChange = tab => {
    const { onAction } = this.props;
    this.setPageNo(DEFAULT_ACTIVITY_PAGE_NO + 1);
    onAction({ type: ACTION_TYPES.HANDLE_TAB_CHANGE, payload: { tab } });
    if (tab === OVERVIEW_TAB_IDS.OVERVIEW) this.handleRefreshOverview();
    else {
      onAction({ type: ACTION_TYPES.HANDLE_FETCH_ACTIVITY_LOGS, payload: { tab } });
      this.setPageNo(DEFAULT_ACTIVITY_PAGE_NO);
    }
  };

  openCallTrackingModal = selectedCommunicationId => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.SHOW_TRANSCRIPT_MODAL,
      payload: {
        response: { selectedCommunicationId },
      },
    });
  };

  closeCallTrackingModal = () => {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HIDE_TRANSCRIPT_MODAL,
    });
  };

  componentDidReceiveEvent(event) {
    const { onAction } = this.props;
    onAction({
      type: ACTION_TYPES.HANDLE_PUSHER_EVENTS,
      payload: {
        event,
      },
    });
  }

  render() {
    const {
      initialLead,
      onRedirection,
      activeTab,
      onAction,
      handleLeadRedirectClick,
      isFetching,
      lead,
      users,
      isLoadingActivity,
      activityLogs,
      showCommunicationThread,
      hasMoreActivity,
      isFetchingMore,
      getDealerPropertyValue,
      aiBdcUser,
      selectedCommunicationId,
      messageId,
      isCallTrackingModalVisible,
      communicationSetupConfig,
    } = this.props;

    const leadDetails = {
      ...tget(lead, 'leadDetails', EMPTY_OBJECT),
      aggregatedSentiment: initialLead?.aggregatedSentiment,
      coordinateToModeToCommPrefs: tget(lead, 'coordinateToModeToCommPrefs', {}),
      communicationPreferences: tget(lead, 'communicationPreferences', {}),
    };
    const entityData = getLeadEntityData(leadDetails);

    return (
      <div className={`p-t-24 ${styles.leadOverviewContainer}`}>
        <PropertyControlledComponentWithFallback controllerProperty={!isFetching}>
          <div className="d-flex-column full-height">
            <div className={styles.leadDetailContainer}>
              <LeadOverviewHeader
                leadDetails={leadDetails}
                onRedirection={onRedirection}
                onAction={onAction}
                handleLeadRedirectClick={handleLeadRedirectClick}
                refresh={this.handleRefreshOverview}
              />
            </div>
            {showCommunicationThread ? (
              <CommunicationThread
                entityData={entityData}
                onBackClick={this.handleCommunicationThreadBackClick}
                getDealerPropertyValue={getDealerPropertyValue}
                handleOpenDrawer={this.openCallTrackingModal}
              />
            ) : (
              <TabPanel
                config={TABS_CONFIG}
                tabs={this.getLeadOverviewTabsConfigMemoized(TABS_CONFIG)}
                activeTab={activeTab}
                activityLogs={activityLogs}
                isFetching={isFetching}
                lead={lead}
                leadDetails={leadDetails}
                onTabChange={this.onTabChange}
                onAction={onAction}
                users={users}
                isLoadingActivity={isLoadingActivity}
                handleRefreshOverview={this.handleRefreshOverview}
                handleActivityLogsRefresh={this.handleActivityLogsRefresh}
                fetchMore={this.handleFetchMoreActivityLogs}
                hasMoreActivity={hasMoreActivity}
                isFetchingMore={isFetchingMore}
              />
            )}
          </div>
          {isCallTrackingModalVisible && (
            <CallDetails
              selectedCommunicationId={selectedCommunicationId}
              renderUserOrCampaignData={renderUserOrCampaignData}
              aiBdcUser={aiBdcUser}
              onCloseDrawer={this.closeCallTrackingModal}
              messageId={messageId}
            />
          )}
        </PropertyControlledComponentWithFallback>
      </div>
    );
  }
}

LeadOverview.defaultProps = {
  initialLead: EMPTY_OBJECT,
  onAction: _noop,
  handleLeadRedirectClick: _noop,
  activeTab: EMPTY_STRING,
  activityLogs: EMPTY_ARRAY,
  users: EMPTY_ARRAY,
  isFetching: false,
  isLoadingActivity: false,
  showCommunicationThread: false,
  hasMoreActivity: false,
  isFetchingMore: false,
  lead: EMPTY_OBJECT,
  getDealerPropertyValue: _noop,
  aiBdcUser: undefined,
  communicationSetupConfig: EMPTY_OBJECT,
};

LeadOverview.propTypes = {
  onRedirection: PropTypes.func.isRequired,
  initialLead: PropTypes.object,
  onAction: PropTypes.func,
  handleLeadRedirectClick: PropTypes.func,
  activeTab: PropTypes.string,
  activityLogs: PropTypes.array,
  users: PropTypes.array,
  isFetching: PropTypes.bool,
  isLoadingActivity: PropTypes.bool,
  showCommunicationThread: PropTypes.bool,
  hasMoreActivity: PropTypes.bool,
  isFetchingMore: PropTypes.bool,
  subscribePusher: PropTypes.func.isRequired,
  lead: PropTypes.object,
  getDealerPropertyValue: PropTypes.func,
  aiBdcUser: PropTypes.object,
  communicationSetupConfig: PropTypes.object,
};

export default compose(withCommunicationSetupConsumer, memo)(LeadOverview);
