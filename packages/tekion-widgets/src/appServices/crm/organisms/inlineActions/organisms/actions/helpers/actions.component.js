import React from 'react';
import cx from 'classnames';
import _curry from 'lodash/curry';

import IconWithLabelAsButton from '../organisms/iconWithLabelAsButton';

import styles from '../../../inlineActions.module.scss';

export const renderAction = _curry((onClickAction, isMouseEntered, isCTCDisabled, item) => {
  const id = item.getId();
  const label = item.getLabel();
  const iconClassName = item.getIconClassName();
  const disabled = item.getDisabled();
  const iconName = item.getIconName();
  const iconColor = item.getIconColor();
  const size = item.getSize();
  const Renderer = item.getRenderer();

  if (Renderer) {
    return <Renderer isCTCDisabled={isCTCDisabled} isMouseEntered={isMouseEntered} />;
  }

  return (
    <IconWithLabelAsButton
      onClick={onClickAction}
      id={id}
      label={label}
      buttonClassName={cx({ [styles.primaryButton]: !disabled, [styles.highlight]: isMouseEntered })}
      iconClassName={iconClassName}
      disabled={disabled}
      iconName={iconName}
      iconColor={iconColor}
      size={size}
    />
  );
});
