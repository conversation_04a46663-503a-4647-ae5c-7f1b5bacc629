import { memo } from 'react';
import _map from 'lodash/map';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';

import ActionsBuilder from '../../builders/Actions';
import { renderAction } from './helpers/actions.component';

const Actions = props => {
  const { items, onClickAction, isMouseEntered, isCTCDisabled } = props;
  return _map(items, renderAction(onClickAction, isMouseEntered, isCTCDisabled));
};

Actions.propTypes = {
  items: PropTypes.arrayOf(PropTypes.instanceOf(ActionsBuilder)),
  onClickAction: PropTypes.func.isRequired,
  isMouseEntered: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

Actions.defaultProps = {
  items: EMPTY_ARRAY,
  isMouseEntered: false,
  isCTCDisabled: false,
};

export default memo(Actions);
