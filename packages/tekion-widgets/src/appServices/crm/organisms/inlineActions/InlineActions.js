import React, { memo, useMemo } from 'react';
import cx from 'classnames';
import _isEmpty from 'lodash/isEmpty';
import PropTypes from 'prop-types';

import { EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import FontIcon, { SIZES } from '@tekion/tekion-components/src/atoms/FontIcon';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';

import ICONS from '../../constants/icons';
import ActivityActionsBuilder from './builders/Actions';
import getKebabMenuItems from './helpers/getKebabMenuItems';
import ActivityActions from './organisms/actions';

import styles from './inlineActions.module.scss';

const InlineActions = props => {
  const { rightActions, kebabActionProps, onClickAction, className, hideKebabMenu, isMouseEntered, isCTCDisabled } =
    props;

  const kebabMenuItems = useMemo(() => getKebabMenuItems(kebabActionProps), [kebabActionProps]);

  const isKebabMenuDisabled = _isEmpty(kebabMenuItems);

  return (
    <div className={`flex align-items-start ${className}`}>
      {!_isEmpty(rightActions) && (
        <ActivityActions
          isCTCDisabled={isCTCDisabled}
          isMouseEntered={isMouseEntered}
          onClickAction={onClickAction}
          items={rightActions}
        />
      )}
      {!hideKebabMenu && (
        <KebabMenu
          className={isKebabMenuDisabled && styles.disabledButton}
          onClickAction={onClickAction}
          menuItems={kebabMenuItems}
          disabled={isKebabMenuDisabled}
          triggerElement={
            <FontIcon
              size={SIZES.MD_S}
              className={cx('cursor-pointer align-self-center', styles.kebabMenu, {
                [styles.highlight]: isMouseEntered,
              })}>
              {ICONS.OVERFLOW}
            </FontIcon>
          }
        />
      )}
    </div>
  );
};

InlineActions.propTypes = {
  rightActions: PropTypes.arrayOf(PropTypes.instanceOf(ActivityActionsBuilder)),
  kebabActionProps: PropTypes.arrayOf(PropTypes.instanceOf(ActivityActionsBuilder)),
  onClickAction: PropTypes.func.isRequired,
  className: PropTypes.string,
  hideKebabMenu: PropTypes.bool,
  isCTCDisabled: PropTypes.bool,
};

InlineActions.defaultProps = {
  rightActions: EMPTY_ARRAY,
  kebabActionProps: EMPTY_ARRAY,
  className: '',
  hideKebabMenu: false,
  isCTCDisabled: false,
};

export default memo(InlineActions);
