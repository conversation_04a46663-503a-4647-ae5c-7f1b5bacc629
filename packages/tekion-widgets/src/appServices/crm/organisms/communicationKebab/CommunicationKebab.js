import React, { useState, useMemo, useCallback, useContext } from 'react';
import PropTypes from 'prop-types';
import _compact from 'lodash/compact';
import _map from 'lodash/map';
import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';
import _noop from 'lodash/noop';
import _size from 'lodash/size';
import _find from 'lodash/find';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { PERMISSIONS } from '@tekion/tekion-base/constants/permissions';
import { ENTITY_TYPE } from '@tekion/tekion-base/constants/emailCommunication';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import DealerInfoReader from '@tekion/tekion-base/readers/DealerInfo';
import { getCTCDisabled } from '@tekion/tekion-business/src/helpers/crm/communication';

import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import ConfirmationModal from '@tekion/tekion-components/src/molecules/confirmationDialog';
import { COMMUNICATION_TYPES as BASE_COMMUNICATION } from '@tekion/tekion-base/constants/communicationTypes';
import isEUDealer from '@tekion/tekion-business/src/appServices/crm/helpers/isEUDealer';
import { hasLogCall } from '@tekion/tekion-business/src/appServices/crm/permissions/crm.permissions';
import KebabMenu from '@tekion/tekion-components/src/molecules/KebabMenu';
import { EmailComposerEvent, EMAIL_COMPOSER_EVENTS } from '@tekion/tekion-components/src/emitters/EmailComposerEmitter';
import { PropertyControlledComponent } from '@tekion/tekion-components/src/molecules';
import { useTekionConversion } from '@tekion/tekion-conversion-web';

import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';
import { PRIVACY_PREFERENCES } from '@tekion/tekion-business/src/appServices/communication/constants/privacyPreferences';
import {
  shouldShowOptedOutModal as showOptedOutModal,
  isCommunicationAllowedWithPopup,
} from '../../../communication/helpers/templateBuilder/privacyPreference.helper';

import { showNDCAlert } from '../ndcAlert/ndcAlert.events';
import {
  getShouldShowNdcAlert,
  getCommunicationLabels,
  getOptionsByEntityType,
  isAgentNumberSetupKey,
} from './helpers/communicationKebab.general';
import { onClickLog } from '../../helpers/callLog';
import withCallCommunication from '../../../../connectors/withCallCommunication';

import { COMMUNICATION_OPTIONS, getMenuItems } from './helpers/communicationKebab.config';
import COMMUNICATION_TYPES from '../../constants/communicationTypes';
import { openConversation, getLeadName as getEntityCustomerName } from '../../helpers/lead';
import { getEntityId } from '../../helpers/commonEntities';
import { isDuplicateLead } from '../../helpers/common';
import usePermissions from '../../../../customHooks/usePermissions';
import { NDCContext } from '../ndc/ndcContextProvider';
import useModalVisibility from '../../hooks/useModalVisibility/useModalVisibility';
import StopCommunicationModal from '../../molecules/stopCommunicationModal';
import { DEFAULT_COUNTRY_CODE } from './communicationKebab.constants';
import { isCommunicationTypeCall } from '../../helpers/communications/communication.type';
import BloctelModal from '../../molecules/bloctelModal';
import { isLeadBelongToMainSite } from '../../../../helpers/crm/communication';
import { createSubMenu } from './helpers/communicationKebab.subMenu';
import { callAndOpenConversation } from './helpers/communicationKebab.callSetup';
import { COMMUNICATION_PREFERENCES } from '../../constants/communicationPreferences';
import { getDealerIds } from '../../../../helpers/communication.helpers';

const CommunicationKebab = ({
  config,
  order = 0,
  onClickAction,
  getDealerPropertyValue,
  leadDetails,
  entityType,
  currentCallDetails,
  preferenceChannel = PRIVACY_PREFERENCES.PURCHASE_INQUIRY_FOLLOWUPS,
  dealerInfo,
  appointmentId,
  communicationOptInPreference,
  communicationSetupConfig,
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [selectedValues, setSelectedValues] = useState(EMPTY_OBJECT);
  const isCRMEnabled = getDealerPropertyValue(DEALER_PROPERTIES.CRM_ENABLED);
  const allowCommunicationOnOptOut = useMemo(
    () => DealerInfoReader.allowCommunicationOnOptOut(dealerInfo),
    [dealerInfo]
  );

  const isCTCDisabled = getCTCDisabled(communicationSetupConfig);
  const { ndcPreferences } = useContext(NDCContext);
  const callPermissions = usePermissions({ validFor: [PERMISSIONS.SALES.CRM.CALL_CUSTOMER] });
  const textPermission = usePermissions({ validFor: [PERMISSIONS.SALES.CRM.TEXT_CUSTOMER] });
  const preference = LeadReader.coordinateToModeToCommPrefs(leadDetails);
  const emailPermission = usePermissions({ validFor: [PERMISSIONS.SALES.CRM.EMAIL_CUSTOMER] });
  const callPermissionWithDealerProp = isCTCDisabled ? hasLogCall() : callPermissions;
  const isCentralPreferenceEnabled = getDealerPropertyValue(
    DEALER_PROPERTIES.CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED
  );
  const isCurrentDealerIsEUDealer = isEUDealer(getDealerPropertyValue);
  const siteId = LeadReader.siteId(leadDetails);
  const dealerId = LeadReader.dealerId(leadDetails);
  const isLeadBelongsToMainSite = isLeadBelongToMainSite(siteId, getDealerPropertyValue);
  const isStopCommunication = LeadReader.buyerStopCommunication(leadDetails);
  const { isModalVisible, openModal, closeModal } = useModalVisibility();
  const {
    isModalVisible: isOptedOutVisible,
    openModal: openOptedOutModal,
    closeModal: closeOptedOutModal,
  } = useModalVisibility();
  const {
    isModalVisible: isbloctelModalVisible,
    openModal: openBloctelModal,
    closeModal: closeBloctelModal,
  } = useModalVisibility();
  const { selectedCommunicationType, selectedCoordinate, selectedRowInfo } = selectedValues;

  const { getFormattedPhoneNumber } = useTekionConversion();

  const permissions = useMemo(
    () => ({
      CALL: callPermissionWithDealerProp,
      EMAIL: emailPermission,
      TEXT: textPermission,
    }),
    [callPermissionWithDealerProp, textPermission, emailPermission]
  );

  const localConfig = useMemo(() => {
    const communicationLabels = getCommunicationLabels(
      leadDetails,
      preferenceChannel,
      isCRMEnabled,
      allowCommunicationOnOptOut,
      currentCallDetails,
      permissions,
      ndcPreferences,
      isCentralPreferenceEnabled,
      isCurrentDealerIsEUDealer,
      getFormattedPhoneNumber,
      communicationOptInPreference,
      isLeadBelongsToMainSite
    );

    const allConfigs = order === 0 ? [...COMMUNICATION_OPTIONS, ...config] : [...config, ...COMMUNICATION_OPTIONS];

    const configData = _compact(
      _map(allConfigs, row => {
        const communicationCoordinates = _get(communicationLabels, [row?.type], []);

        if (_size(communicationCoordinates) > 0) {
          const subMenu = createSubMenu(row?.type, communicationCoordinates, getDealerPropertyValue);
          return {
            ...row,
            subMenu,
          };
        }

        const subMenu = _get(communicationLabels, [row?.actionType], []);
        if (_size(subMenu) > 0) {
          return {
            ...row,
            subMenu,
          };
        }

        return row;
      })
    );

    return getOptionsByEntityType(leadDetails, configData);
  }, [
    getDealerPropertyValue,
    leadDetails,
    config,
    isCRMEnabled,
    allowCommunicationOnOptOut,
    currentCallDetails,
    permissions,
    order,
    preferenceChannel,
    ndcPreferences,
    getFormattedPhoneNumber,
    communicationOptInPreference,
    isCentralPreferenceEnabled,
    isCurrentDealerIsEUDealer,
    isLeadBelongsToMainSite,
  ]);

  const handleCommunicationWithNDCInfo = useCallback(({ contact, communicationType, ndcInfo, onSubmitCallback }) => {
    const shouldShowNdcAlert = getShouldShowNdcAlert(ndcInfo, communicationType);
    if (shouldShowNdcAlert) {
      showNDCAlert({
        contact,
        communicationType,
        onSubmitCallback,
      });
      return;
    }
    onSubmitCallback();
  }, []);

  const handleCommunications = useCallback((communicationType, contact, itemInfo) => {
    const { countryCode = DEFAULT_COUNTRY_CODE } = itemInfo;
    const department = LeadReader.department(leadDetails);
    switch (communicationType) {
      case COMMUNICATION_TYPES.CALL: {
        if (isCTCDisabled) {
          onClickLog({ leadDetails, getFormattedPhoneNumber });
        } else {
          handleCommunicationWithNDCInfo({
            contact,
            communicationType,
            ndcInfo: itemInfo?.extraConfig,
            onSubmitCallback: callAndOpenConversation({
              contact,
              countryCode,
              department,
              leadDetails,
              entityType,
              siteId,
              dealerId,
              getDealerPropertyValue,
            }),
          });
        }
        break;
      }
      case COMMUNICATION_TYPES.TEXT: {
        const openText = () =>
          openConversation(
            {
              phoneNumber: contact,
              customerId: getEntityId(leadDetails, entityType),
              customerName: getEntityCustomerName(leadDetails),
              showNDCAlert: false,
              countryCode,
            },
            undefined,
            undefined,
            undefined,
            undefined,
            LeadReader.department(leadDetails)
          );
        handleCommunicationWithNDCInfo({
          onSubmitCallback: openText,
        });
        break;
      }
      case BASE_COMMUNICATION.EMAIL:
      case COMMUNICATION_TYPES.MAIL: {
        // change entityType for email as and when if required
        EmailComposerEvent.emit(EMAIL_COMPOSER_EVENTS.SHOW, {
          entity: { entityType: ENTITY_TYPE.LEAD, entityId: getEntityId(leadDetails, entityType), appointmentId },
          to: {
            addresses: [contact],
          },
          selectedDealerIds: getDealerIds(leadDetails),
          dealerId,
          siteId,
          // workspaceContext,
        });
        break;
      }
      default:
        break;
    }
    closeModal();
    closeBloctelModal();
    closeOptedOutModal();
  });

  const handleActions = useCallback(
    (key, rowInfo, label, value) => {
      const isBloctel = _get(rowInfo, 'isBloctel');
      const preferencesForCall = _get(preference, `${value}.${COMMUNICATION_TYPES.CALL}.categoryOptedInPreferences`);
      const preferencesForCallForSubcategory = _find(preferencesForCall, {
        subCategory: COMMUNICATION_PREFERENCES.PURCHASE_ENQUIRY_FOLLOWUPS,
      });
      const blockReason = _get(preferencesForCallForSubcategory, 'blockReason');
      const allowCommunicationState = _get(preferencesForCallForSubcategory, 'allowCommunicationState');

      if (
        [
          COMMUNICATION_TYPES.CALL,
          COMMUNICATION_TYPES.MAIL,
          COMMUNICATION_TYPES.TEXT,
          BASE_COMMUNICATION.EMAIL,
        ].includes(key)
      ) {
        const isSelectedCommunicationTypeCall = isCommunicationTypeCall(key);
        const showBloctelModal = isBloctel && isSelectedCommunicationTypeCall;
        const shouldShowOptedoutModal =
          isSelectedCommunicationTypeCall &&
          showOptedOutModal(blockReason) &&
          isCommunicationAllowedWithPopup(allowCommunicationState);
        if (isAgentNumberSetupKey(rowInfo)) {
          showGlobalModal({ modalType: MODAL_TYPE.AGENT_NUMBER_SETUP_MODAL });
        }
        if (shouldShowOptedoutModal) {
          openOptedOutModal();
          setSelectedValues({ selectedCoordinate: value, selectedCommunicationType: key, selectedRowInfo: rowInfo });
        } else if (isStopCommunication) {
          openModal();
          setSelectedValues({ selectedCoordinate: value, selectedCommunicationType: key, selectedRowInfo: rowInfo });
        } else if (showBloctelModal) {
          openBloctelModal();
          setSelectedValues({ selectedCoordinate: value, selectedCommunicationType: key, selectedRowInfo: rowInfo });
        } else handleCommunications(key, value, rowInfo);
      } else {
        onClickAction({ key, label, value, rowInfo });
      }
      setIsVisible(false);
    },
    [
      handleCommunications,
      onClickAction,
      openModal,
      openOptedOutModal,
      preference,
      isStopCommunication,
      openBloctelModal,
    ]
  );

  const onSubmitStopCommunication = () =>
    handleCommunications(selectedCommunicationType, selectedCoordinate, selectedRowInfo);

  const onSubmitbloctelCommunication = () =>
    handleCommunications(selectedCommunicationType, selectedCoordinate, selectedRowInfo);

  const onSubmitOptedOutModal = useCallback(
    () => handleCommunications(selectedCommunicationType, selectedCoordinate, selectedRowInfo),
    [selectedCoordinate, selectedCommunicationType, selectedRowInfo, handleCommunications]
  );

  const onClickCancelinStopCommunication = () => {
    setSelectedValues(EMPTY_OBJECT);
    closeModal();
  };

  const onClickCancelBloctelModal = () => {
    setSelectedValues(EMPTY_OBJECT);
    closeBloctelModal();
  };

  const onClickCancelOptedOutModal = useCallback(() => {
    setSelectedValues(EMPTY_OBJECT);
    closeOptedOutModal();
  }, [closeOptedOutModal]);

  return (
    <PropertyControlledComponent controllerProperty={!_isEmpty(leadDetails) && !isDuplicateLead(leadDetails)}>
      <KebabMenu
        onClickAction={handleActions}
        menuItems={getMenuItems(localConfig, isStopCommunication, isCurrentDealerIsEUDealer, isLeadBelongsToMainSite)}
        visible={isVisible}
        onVisibleChange={setIsVisible}
      />
      {isStopCommunication && (
        <StopCommunicationModal
          communicationType={selectedCommunicationType}
          visible={isModalVisible}
          onCancel={onClickCancelinStopCommunication}
          onHandleClick={onSubmitStopCommunication}
        />
      )}
      <BloctelModal
        communicationType={selectedCommunicationType}
        visible={isbloctelModalVisible}
        onCancel={onClickCancelBloctelModal}
        onSubmitModal={onSubmitbloctelCommunication}
      />
      <ConfirmationModal
        isVisible={isOptedOutVisible}
        title={__('Call to Outed out Phone No.')}
        onSubmit={onSubmitOptedOutModal}
        onCancel={onClickCancelOptedOutModal}
        content={__('This number was Opted Out by the customer. Do you want to continue making this call?')}
      />
    </PropertyControlledComponent>
  );
};

CommunicationKebab.propTypes = {
  config: PropTypes.array.isRequired,
  onClickAction: PropTypes.func.isRequired,
  order: PropTypes.oneOf([0, 1]),
  communicationOptInPreference: PropTypes.object,
  appointmentId: PropTypes.string,
  getDealerPropertyValue: PropTypes.func,
  leadDetails: PropTypes.object,
  entityType: PropTypes.string,
  currentCallDetails: PropTypes.object,
  preferenceChannel: PropTypes.string,
  dealerInfo: PropTypes.object,
  communicationSetupConfig: PropTypes.object,
};

CommunicationKebab.defaultProps = {
  order: 0,
  communicationOptInPreference: EMPTY_OBJECT,
  appointmentId: undefined,
  getDealerPropertyValue: _noop,
  leadDetails: EMPTY_OBJECT,
  entityType: undefined,
  currentCallDetails: EMPTY_OBJECT,
  preferenceChannel: PRIVACY_PREFERENCES.PURCHASE_INQUIRY_FOLLOWUPS,
  dealerInfo: EMPTY_OBJECT,
  communicationSetupConfig: EMPTY_OBJECT,
};

export default withCallCommunication(CommunicationKebab);
