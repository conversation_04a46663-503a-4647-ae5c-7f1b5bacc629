import { isAppointmentFieldDisabled } from '@tekion/tekion-base/helpers/appointments.helper';
import { isCreatedStatus } from '@tekion/tekion-business/src/helpers/crm/appointment.actions';

export const isAppointmentLeadFieldDisabled = (values, isAECEnabledAndCRMDisabled, disableLeadSelection) => {
  if (disableLeadSelection) {
    return true;
  }
  const { appointmentStatus, appointmentId } = values;
  if (!appointmentId) return false;

  if (isCreatedStatus(appointmentStatus) && isAECEnabledAndCRMDisabled) {
    return true;
  }

  return isAppointmentFieldDisabled(values);
};
