import _omit from 'lodash/omit';

import { APPOINTMENT_TYPE as APPOINTMENT_TYPES } from '@tekion/tekion-base/constants/crm/appointments';
import { isMomentValid } from '@tekion/tekion-base/utils/dateUtils';

export const checkIfValidTime = ({ selectedTime, minAllowedTime }) =>
  isMomentValid(selectedTime) && isMomentValid(minAllowedTime) && selectedTime.valueOf() > minAllowedTime.valueOf();

export const getAECAppointmentTypes = appointmentTypes =>
  _omit(appointmentTypes, [APPOINTMENT_TYPES.ONLINE, APPOINTMENT_TYPES.LEASE_BUYOUT, APPOINTMENT_TYPES.APPRAISAL]);
