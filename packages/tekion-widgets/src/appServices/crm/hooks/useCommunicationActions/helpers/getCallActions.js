import _noop from 'lodash/noop';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import { MODAL_TYPE, showGlobalModal } from '@tekion/tekion-components/src/emitters/ModalEventEmitter';
import { isAgentUserSetupEnabled } from '@tekion/tekion-business/src/appServices/crm/helpers/isAgentUserSetupEnabled';
import { getAgentNumberForCurrentDealership } from '@tekion/tekion-base/helpers/crm/localStorage.helpers';
import { LEAD_ITEM_ACTION_MENU_ENUM } from '../../../constants/leads/activityLogs';
import CALL_ACTIONS from '../constants/call/callActions';
import { getMarkAsRespondedKebabMenu } from './useCommunicationActions.general';
import { markAsResponded } from './markAsResponded';

// Helpers
import { onClickLog } from '../../../helpers/callLog';

const getCallActions = ({ data = EMPTY_OBJECT, additionalInfo = EMPTY_OBJECT }) => {
  const { activityData } = data;
  const { taskId, activityDataType } = activityData || EMPTY_OBJECT;

  const {
    onAction = _noop,
    actionType = '',
    isItemDisabled = false,
    isToDo = false,
    phoneNumber,
    countryCode,
    onMarkUnrResponded = _noop,
    getDealerPropertyValue = _noop,
    isCTCDisabled,
    leadDetails,
  } = additionalInfo;

  const onClickCallAction = initiatorNumber => {
    onAction({
      type: actionType,
      payload: {
        response: {
          phoneNumber,
          countryCode,
          activityDataType,
          taskId,
          initiatorNumber,
          isCTCDisabled,
          leadDetails,
        },
      },
    });
  };

  const onCallBackClick = (isCTCDisabled = false, leadDetails = EMPTY_OBJECT) => {
    const agentNumberSetupEnabled = isAgentUserSetupEnabled(getDealerPropertyValue);
    const initiatorNumber = getAgentNumberForCurrentDealership();
    if (isCTCDisabled) {
      onClickLog({ leadDetails });
      return;
    }

    if (agentNumberSetupEnabled && _isEmpty(initiatorNumber)) {
      showGlobalModal({
        modalType: MODAL_TYPE.AGENT_NUMBER_SETUP_MODAL,
        payload: { onCallViaAgentNumberSetup: onClickCallAction },
      });
    } else if (agentNumberSetupEnabled) {
      onClickCallAction(initiatorNumber);
    } else {
      onClickCallAction();
    }
  };

  const onClickAction = key => {
    switch (key) {
      case LEAD_ITEM_ACTION_MENU_ENUM.CALL:
        onCallBackClick(isCTCDisabled, leadDetails);
        break;
      case LEAD_ITEM_ACTION_MENU_ENUM.MARK_AS_RESPONDED:
        markAsResponded(data, onMarkUnrResponded);
        break;
      default:
        break;
    }
  };

  const rightActions = [CALL_ACTIONS.CALL_BACK.setDisabled(isItemDisabled)];

  const kebabActionProps = getMarkAsRespondedKebabMenu(isToDo);

  return {
    rightActions,
    onClickAction,
    kebabActionProps,
    hideKebabMenu: !isToDo,
  };
};

export default getCallActions;
