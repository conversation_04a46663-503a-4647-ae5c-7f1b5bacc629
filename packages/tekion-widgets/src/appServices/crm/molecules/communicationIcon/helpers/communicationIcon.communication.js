import _get from 'lodash/get';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import { makeDealerContextQueryParams } from '@tekion/tekion-business/src/appServices/communication/helpers/communications.chatQueryParams';
import { EMAIL_COMPOSER_EVENTS, EmailComposerEvent } from '@tekion/tekion-components/src/emitters/EmailComposerEmitter';
import { onClickLog } from '../../../helpers/callLog';

import COMMUNICATION_TYPES from '../../../constants/communicationTypes';

import { getEntityId } from '../../../helpers/commonEntities';
import { handleCommunicationInitByConfig } from '../communicationIcon.helpers';
import {
  getLeadName as getEntityCustomerName,
  callAndNavigateToConversation,
  openConversation,
} from '../../../helpers/lead';
import { getEmailComposerPayload } from '../communicationIcon.factory';
import { ADD_VIDEO_TO_EMAIL_DEEPLINK_WIDGET_METADATA } from '../../../../../organisms/emailCommunication';
import { ADD_VIDEO_TO_TEXT_DEEPLINK_WIDGET_METADATA } from '../../../../../organisms/communications/nonEnterpriseCommunication/externalCommunication';

const isTextCoordinate = coordinateType => coordinateType === COMMUNICATION_TYPES.TEXT;

const isEmailCoordinate = coordinateType => coordinateType === COMMUNICATION_TYPES.EMAIL;

const openCallWidget =
  ({
    contact,
    countryCode,
    leadDetails,
    entityType,
    entityPayload,
    afterTaskComplete,
    getDealerPropertyValue,
    department,
    siteId,
    dealerId,
    initiatorNumber,
  }) =>
  () => {
    const customerId = getEntityId(leadDetails, entityType);
    const customerName = getEntityCustomerName(leadDetails);
    const dealerContextQueryParams = makeDealerContextQueryParams({ siteId, dealerId });

    callAndNavigateToConversation({
      phoneNumber: contact,
      countryCode,
      customerId,
      customerName,
      entityType,
      entityPayload,
      showNDCAlert: false,
      afterCallComplete: afterTaskComplete,
      getDealerPropertyValue,
      department,
      siteId,
      dealerContextQueryParams,
      initiatorNumber,
    });
  };

const openTextWidget =
  ({
    contact,
    afterTaskComplete,
    templateId,
    externalTemplateData,
    workspaceContext,
    deepLinkWidgetMetaData,
    leadDetails,
    entityType,
    countryCode,
  }) =>
  () => {
    const customerId = getEntityId(leadDetails, entityType);
    const customerName = getEntityCustomerName(leadDetails);
    openConversation(
      {
        phoneNumber: contact,
        countryCode,
        customerId,
        customerName,
        showNDCAlert: false,
        entityType,
      },
      afterTaskComplete,
      templateId,
      externalTemplateData,
      workspaceContext,
      deepLinkWidgetMetaData
    );
  };

export const handleCallCommunication = ({ getState, params = EMPTY_OBJECT }) => {
  const { leadDetails, entityType, entityPayload, afterTaskComplete, getDealerPropertyValue, isCTCDisabled } =
    getState();

  const { contact, coordinatesNDCInfo, countryCode, communicationType, initiatorNumber } = params;

  const department = _get(leadDetails, 'department');
  const siteId = LeadReader.siteId(leadDetails);
  const dealerId = LeadReader.dealerId(leadDetails);

  if (isCTCDisabled) {
    onClickLog({ leadDetails });
    return;
  }

  handleCommunicationInitByConfig({
    coordinatesNDCInfo,
    onSubmitCallback: openCallWidget({
      contact,
      countryCode,
      leadDetails,
      entityType,
      entityPayload,
      afterTaskComplete,
      getDealerPropertyValue,
      department,
      siteId,
      dealerId,
      initiatorNumber,
    }),
    communicationType,
    leadDetails,
  });
};

export const handleTextCommunication = async ({ getState, params = EMPTY_OBJECT }) => {
  const { leadDetails, entityType, afterTaskComplete, templateId } = getState();

  const { contact, getExternalTemplateData, workspaceContext, countryCode, deepLinkWidgetMetaData } = params;

  const externalTemplateData = await getExternalTemplateData();
  const onSubmitCallback = openTextWidget({
    contact,
    afterTaskComplete,
    templateId,
    externalTemplateData,
    workspaceContext,
    deepLinkWidgetMetaData,
    leadDetails,
    entityType,
    countryCode,
  });
  onSubmitCallback();
};

export const handleMailCommunication = async ({ getState, params = EMPTY_OBJECT }) => {
  const data = getState();
  const payload = await getEmailComposerPayload(data, params);
  EmailComposerEvent.emit(EMAIL_COMPOSER_EVENTS.SHOW, payload);
};

export const onVideoClick = ({ getState, params = EMPTY_OBJECT }) => {
  const { coordinateType } = params;

  const isCoordinateText = isTextCoordinate(coordinateType);

  if (isCoordinateText) {
    const updatedParams = {
      ...params,
      deepLinkWidgetMetaData: ADD_VIDEO_TO_TEXT_DEEPLINK_WIDGET_METADATA,
      communicationType: coordinateType,
    };
    handleTextCommunication({ getState, params: updatedParams });
    return;
  }

  const isCoordinateEmail = isEmailCoordinate(coordinateType);

  if (isCoordinateEmail) {
    const updatedParams = {
      ...params,
      deepLinkWidgetMetaData: ADD_VIDEO_TO_EMAIL_DEEPLINK_WIDGET_METADATA,
      communicationType: coordinateType,
    };
    handleMailCommunication({ getState, params: updatedParams });
  }
};
