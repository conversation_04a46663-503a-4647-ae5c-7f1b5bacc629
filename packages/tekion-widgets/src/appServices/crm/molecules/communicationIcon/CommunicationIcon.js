import React, { memo, useState, useCallback, useMemo, useContext } from 'react';
import PropTypes from 'prop-types';
import _get from 'lodash/get';
import _includes from 'lodash/includes';
import _isEmpty from 'lodash/isEmpty';
import _isFunction from 'lodash/isFunction';
import _noop from 'lodash/noop';
import { compose } from 'recompose';

import _stubFlase from 'lodash/stubFalse';

import { EMPTY_STRING, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import withActions from '@tekion/tekion-components/src/connectors/withActions';
import { PHONE_ACTIONS } from '@tekion/tekion-base/constants/crm/tasks';
import ENTITY_TYPES from '@tekion/tekion-base/constants/entityTypes';
import LeadReader from '@tekion/tekion-base/readers/lead.reader';
import DealerInfoReader from '@tekion/tekion-base/readers/DealerInfo';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';
import { isWorkspaceView } from '@tekion/tekion-base/helpers/user.helper';
import isEUDealer from '@tekion/tekion-business/src/appServices/crm/helpers/isEUDealer';
import useDealerInfo from '@tekion/tekion-business/src/hooks/useDealerInfo';
import { hasRetailView } from '@tekion/tekion-business/src/appServices/crm/permissions/crm.permissions';
import Content from '@tekion/tekion-components/src/atoms/Content';
import ConfirmationModal from '@tekion/tekion-components/src/molecules/confirmationDialog';
import { useTekionConversion } from '@tekion/tekion-conversion-web';
import PrivacyPreferenceReader from '@tekion/tekion-base/readers/communication/privacyPreference';
import { PRIVACY_PREFERENCES } from '@tekion/tekion-business/src/appServices/communication/constants/privacyPreferences';
import {
  getWarnConfig,
  shouldShowOptedOutModal,
} from '../../../communication/helpers/templateBuilder/privacyPreference.helper';

import withCallCommunication from '../../../../connectors/withCallCommunication';
import { isCommunicationTypeCall } from '../../helpers/communications/communication.type';
import COMMUNICATION_TYPES from '../../constants/communicationTypes';
import CoordinateSelector from '../coordinateSelector';
import { isCommunicationDisabled, getDisabledCommunicationMessage } from '../../helpers/lead';
import { ALLOWED_PERMISSIONS_MAP, INITIAL_STATE } from './communicationIcon.constants';
import ACTION_HANDLERS from './communicationIcon.actionHandlers';
import usePermissions from '../../../../customHooks/usePermissions';
import { getEntityPhoneNumberDetails, getDealerId, getEntityEmailDetails } from '../../helpers/commonEntities';
import {
  getCommunicationChannelConfig,
  hasValidSingleCoordinate,
  shouldDisplayCallIcon,
  getDisabledStatus,
  onClickHandlerInStopCommunication,
  isDropdownVisible,
  shouldRenderDropDownForSingleCordinate,
} from './communicationIcon.helpers';
import { NDCContext } from '../../organisms/ndc/ndcContextProvider';
import ACTION_TYPES from './communicationIcon.actionTypes';
import useModalVisibility from '../../hooks/useModalVisibility/useModalVisibility';
import StopCommunicationModal from '../stopCommunicationModal';
import BloctelModal from '../bloctelModal';
import withCommunicationPreferenceFields from '../../hocs/withCommunicationPreferenceFields';
import { isLeadBelongToMainSite } from '../../../../helpers/crm/communication';
import { renderAgentNumberSetupMenuItem } from './helpers/communicationIcon.components';
import { getIsCommunicationAllowedWithPopup } from './helpers/communicationIncon.general';
import { getNDCInfoForLead } from '../../organisms/ndc/ndc.helper';

// getEntityCustomerName we don't need to explicitly handle for ENTITY_TYPE as CUSTOMER as is already handled in lead helper file.

const CommunicationIcon = ({
  communicationType,
  leadDetails,
  entityType,
  currentCallDetails,
  disabled,
  coordinate,
  render,
  className,
  externalTemplate: extTemplate,
  getExternalTemplate,
  getDealerPropertyValue,
  containerNodeSelector,
  preferenceChannel,
  shouldDisplayDropdown,
  communicationOptinPreference,
  onAction,
  dataTest,
  isCTCDisabled,
  deepLinkWidgetMetaData,
}) => {
  const isCurrentDealerIsEUDealer = isEUDealer(getDealerPropertyValue);
  const singleCoordinateDetails = useMemo(
    () => hasValidSingleCoordinate(communicationType, leadDetails, entityType),
    [communicationType, leadDetails, entityType]
  );
  const communicationCoordinate = singleCoordinateDetails || coordinate;
  const coordinatesDetail = getEntityPhoneNumberDetails(leadDetails, entityType);

  const showIcon = usePermissions({ validFor: ALLOWED_PERMISSIONS_MAP[communicationType] });
  const [phoneSelectorStateMap, setPhoneSelectorStateMap] = useState(EMPTY_OBJECT);
  const dealerInfo = useDealerInfo({ dealerId: getDealerId(leadDetails, entityType) });
  const { ndcPreferences = EMPTY_OBJECT } = useContext(NDCContext);
  const allowCommunicationOnOptOut = useMemo(
    () => DealerInfoReader.allowCommunicationOnOptOut(dealerInfo),
    [dealerInfo]
  );
  const isCentralPreferenceEnabled = getDealerPropertyValue(
    DEALER_PROPERTIES.CENTRAL_COMMUNICATION_OPTIN_PREFERENCES_ENABLED
  );

  const siteId = LeadReader.siteId(leadDetails);
  const dealerId = getDealerId(leadDetails, entityType);

  const isLeadBelongsToMainSite = isLeadBelongToMainSite(siteId, getDealerPropertyValue);

  const shouldShowDropdown = isDropdownVisible(isLeadBelongsToMainSite, shouldDisplayDropdown);

  const isStopCommunication = LeadReader.buyerStopCommunication(leadDetails);
  const { isModalVisible, openModal, closeModal } = useModalVisibility();
  const {
    isModalVisible: isOptedOutVisible,
    openModal: openOptedOutModal,
    closeModal: closeOptedOutModal,
  } = useModalVisibility();
  const {
    isModalVisible: isbloctelModalVisible,
    openModal: openBloctelModal,
    closeModal: closeBloctelModal,
  } = useModalVisibility();
  const onPhoneSelectorVisibleChange = (communicationKey, visible) => {
    setPhoneSelectorStateMap({ [communicationKey]: visible });
  };

  const isDeceased = LeadReader.buyerDeceased(leadDetails);
  const isStopDataProcessing = LeadReader.stopDataProcessing(leadDetails);
  const { getFormattedPhoneNumber } = useTekionConversion();

  const { coordinateToModeToCommPrefs: communicationPreferences } = leadDetails;

  const isPreferenceNotAllowed = useMemo(() => {
    const coordinateDetails = coordinate || singleCoordinateDetails?.coordinate;
    const userPreference = getCommunicationChannelConfig({
      coordinate: coordinateDetails,
      type: communicationType,
      channel: preferenceChannel,
      communicationPreferences,
    });
    return coordinateDetails && getWarnConfig(userPreference)?.disabled;
  }, [communicationType, communicationPreferences, coordinate, singleCoordinateDetails, preferenceChannel]);

  const isCommunicationStatusAllowedWithPopup = getIsCommunicationAllowedWithPopup({
    coordinate,
    singleCoordinateDetails,
    communicationType,
    preferenceChannel,
    communicationPreferences,
  });

  const isOptinStatusOptedOut = useMemo(() => {
    const coordinateDetails = coordinate || singleCoordinateDetails?.coordinate;
    const userPreference = getCommunicationChannelConfig({
      coordinate: coordinateDetails,
      type: communicationType,
      channel: preferenceChannel,
      communicationPreferences,
    });
    const blockReason = PrivacyPreferenceReader.blockReason(userPreference);
    return coordinateDetails && shouldShowOptedOutModal(blockReason);
  }, [communicationType, communicationPreferences, coordinate, singleCoordinateDetails, preferenceChannel]);

  const iconConfigByStatus = useMemo(() => {
    const coordinateDetails = coordinate || singleCoordinateDetails?.coordinate;
    const userPreference = getCommunicationChannelConfig({
      coordinate: coordinateDetails,
      type: communicationType,
      channel: preferenceChannel,
      communicationPreferences,
    });
    return getWarnConfig(userPreference);
  }, [coordinate, singleCoordinateDetails?.coordinate, communicationType, preferenceChannel, communicationPreferences]);

  // Currently for Campaign Task, only call action is there. If other actions are added, we need to change isCommunicationDisabled function.
  const { isDisabled, isCallDisabled, isIconVisible } = useMemo(
    () =>
      isCommunicationDisabled({
        communicationKey: communicationType,
        entity: leadDetails,
        currentCallDetails,
        entityType,
        isLeadBelongsToMainSite,
      }),
    [communicationType, currentCallDetails, leadDetails, entityType, isLeadBelongsToMainSite]
  );

  const workspaceContext = isWorkspaceView()
    ? { dealerId: getDealerId(leadDetails, entityType), dealerInfo }
    : EMPTY_OBJECT;

  const getExternalTemplateData = useCallback(async () => {
    if (!getExternalTemplate && _isEmpty(extTemplate)) return EMPTY_OBJECT;
    const res = _isFunction(getExternalTemplate) ? await getExternalTemplate() : extTemplate;
    return !_isEmpty(res) ? { ...res, body: res?.templateBody } : EMPTY_OBJECT;
  }, [extTemplate, getExternalTemplate]);

  const handleOnClickWithCoordinate = useCallback(() => {
    const contact = coordinate || singleCoordinateDetails?.coordinate;
    const countryCode = singleCoordinateDetails?.countryCode;
    const coordinatesNDCInfo = getNDCInfoForLead(ndcPreferences, contact, leadDetails);
    closeOptedOutModal();
    closeModal();
    closeBloctelModal();
    onAction({
      type: ACTION_TYPES.HANDLE_ON_CLICK,
      payload: {
        contact,
        coordinatesNDCInfo,
        getExternalTemplateData,
        workspaceContext,
        countryCode,
        communicationType,
        deepLinkWidgetMetaData,
        dealerId,
        siteId,
      },
    });
  }, [
    ndcPreferences,
    coordinate,
    getExternalTemplateData,
    workspaceContext,
    singleCoordinateDetails,
    onAction,
    closeModal,
    communicationType,
    closeBloctelModal,
    closeOptedOutModal,
    deepLinkWidgetMetaData,
    leadDetails,
    dealerId,
    siteId,    
  ]);

  const containerHandleClick = useCallback(event => {
    event.stopPropagation();
  }, []);

  const isPhoneSelectorOpen = phoneSelectorStateMap[communicationType];

  const isPhoneSelectorRequired = useMemo(() => _includes(PHONE_ACTIONS, communicationType), [communicationType]);

  const shouldRenderCommunicationIcon = !isIconVisible || (isPhoneSelectorRequired && !hasRetailView());

  const renderIcon = () => {
    const disabledCommunicationMessage = getDisabledCommunicationMessage({
      communicationType,
      isDisabled,
      isCallDisabled,
      isCurrentDealerIsEUDealer,
      isCommunicationStatusAllowedWithPopup,
      communicationCoordinate,
      getFormattedPhoneNumber,
      isLeadBelongsToMainSite,
      showDefaultCommunicationMessage: disabled,
    });

    const isCommunicationIconDisabled = getDisabledStatus({
      communicationType,
      leadDetails,
      currentCallDetails,
      entityType,
      isPreferenceNotAllowed,
      disabled,
      isCentralPreferenceEnabled,
      isCurrentDealerIsEUDealer,
      isLeadBelongsToMainSite,
    });

    const renderedIcon = render({
      selected: isPhoneSelectorOpen,
      disableMessage: disabledCommunicationMessage,
      isDisabled: isCommunicationIconDisabled,
    });

    return <div className={className}>{renderedIcon}</div>;
  };

  const renderIconWithCoordinateSelector = () => {
    let coordinateSelectorProps = {
      communicationType,
      onVisibleChange: onPhoneSelectorVisibleChange,
      communicationPreferences,
      allowCommunicationOnOptOut,
      containerNodeSelector,
      entityType,
      entity: leadDetails,
      preferenceChannel,
      ndcPreferences,
      onAction,
      isCentralPreferenceEnabled,
      isCurrentDealerIsEUDealer,
      isStopCommunication,
      leadDetails,
      getExternalTemplateData,
      workspaceContext,
      communicationOptinPreference,
      deepLinkWidgetMetaData,
      getDealerPropertyValue,
    };

    switch (communicationType) {
      case COMMUNICATION_TYPES.MAIL:
      case COMMUNICATION_TYPES.EMAIL: {
        const emailDetails = getEntityEmailDetails(leadDetails, entityType);
        coordinateSelectorProps = {
          ...coordinateSelectorProps,
          coordinateDetails: emailDetails,
          isDisabled: getDisabledStatus({
            communicationType,
            leadDetails,
            currentCallDetails,
            entityType,
            isPreferenceNotAllowed,
            disabled,
            isCentralPreferenceEnabled,
            isCurrentDealerIsEUDealer,
            isLeadBelongsToMainSite,
          }),
        };
        break;
      }

      case COMMUNICATION_TYPES.TEXT:
      case COMMUNICATION_TYPES.CALL: {
        coordinateSelectorProps = {
          ...coordinateSelectorProps,
          coordinateDetails: coordinatesDetail,
          isDisabled: getDisabledStatus({
            communicationType,
            leadDetails,
            currentCallDetails,
            entityType,
            isPreferenceNotAllowed,
            disabled,
            isCentralPreferenceEnabled,
            isCurrentDealerIsEUDealer,
            isLeadBelongsToMainSite,
          }),
          formatter: getFormattedPhoneNumber,
          renderAgentNumberSetupMenuItem,
          isCTCDisabled,
        };
        break;
      }

      case COMMUNICATION_TYPES.VIDEO: {
        const emailDetails = getEntityEmailDetails(leadDetails, entityType);
        coordinateSelectorProps = {
          ...coordinateSelectorProps,
          coordinateDetails: { emailDetails, phoneTextDetails: coordinatesDetail },
          isDisabled: getDisabledStatus({
            communicationType,
            leadDetails,
            currentCallDetails,
            entityType,
            isPreferenceNotAllowed,
            disabled,
            isCentralPreferenceEnabled,
            isCurrentDealerIsEUDealer,
            isLeadBelongsToMainSite,
          }),
          formatter: getFormattedPhoneNumber,
        };
        break;
      }
      default:
        break;
    }
    const Container = shouldShowDropdown
      ? CoordinateSelector
      : ({ children, ...rest }) => <Content {...rest}>{children}</Content>;

    return (
      <div onClick={containerHandleClick} role="button" tabIndex={0} data-test={dataTest}>
        {!isDeceased && !isStopDataProcessing && (
          <Container {...coordinateSelectorProps} workspaceContext={workspaceContext}>
            {renderIcon()}
          </Container>
        )}
      </div>
    );
  };

  const renderCommunicationIcon = () => {
    if (shouldDisplayCallIcon(communicationType, getDealerPropertyValue, showIcon)) {
      return <></>;
    }

    const isDropdownVisibleForSingleCordinate = shouldRenderDropDownForSingleCordinate({
      coordinate,
      singleCoordinateDetails,
      getDealerPropertyValue,
      communicationType,
    });

    const ndcInfo = getNDCInfoForLead(ndcPreferences, coordinate, leadDetails);
    if (!isDropdownVisibleForSingleCordinate) {
      const getDisabledNDCState =
        [COMMUNICATION_TYPES.CALL, COMMUNICATION_TYPES.TEXT].includes(communicationType) && !_isEmpty(ndcInfo)
          ? ndcInfo?.getDisabledNDCState
          : _stubFlase;
      const isBloctel = _get(singleCoordinateDetails, 'isBloctel');
      const ndcConfig = getDisabledNDCState() || _noop;
      const isNdcDisabledForCall = _get(ndcConfig, 'getDisabledNDCStateForCall');
      const isNdcDisabledForText = _get(ndcConfig, 'getDisabledNDCStateForText');
      const isSelectedCommunicationTypeCall = isCommunicationTypeCall(communicationType);
      const isNdcDisabled = isSelectedCommunicationTypeCall ? isNdcDisabledForCall : isNdcDisabledForText;
      const isSingleCoordinateDisabled =
        getDisabledStatus({
          communicationType,
          leadDetails,
          currentCallDetails,
          entityType,
          isPreferenceNotAllowed,
          disabled,
          isCentralPreferenceEnabled,
          isCurrentDealerIsEUDealer,
          isDeceased,
          isStopDataProcessing,
          isLeadBelongsToMainSite,
        }) || isNdcDisabled;
      const clickHandler = onClickHandlerInStopCommunication({
        isStopCommunication,
        openModal,
        onClickHandler: handleOnClickWithCoordinate,
        isSingleCoordinateDisabled,
        isBloctel,
        openBloctelModal,
        communicationType,
        openOptedOutModal,
        isCommunicationStatusAllowedWithPopup,
        isOptinStatusOptedOut,
      });

      const handleDisabledNDCState = () => getDisabledNDCState() || _noop;

      return (
        <>
          <div onClick={clickHandler} role="presentation" className={className} data-test={dataTest}>
            {render({
              isDisabled: isSingleCoordinateDisabled,
              disableMessage: getDisabledCommunicationMessage({
                communicationType,
                isDisabled: isSingleCoordinateDisabled,
                isCallDisabled,
                isCurrentDealerIsEUDealer,
                isPreferenceNotAllowed,
                isCommunicationStatusAllowedWithPopup,
                iconConfigByStatus,
                getDisabledNDCState: handleDisabledNDCState,
                communicationCoordinate,
                getFormattedPhoneNumber,
                isBloctel,
                isLeadBelongsToMainSite,
                showDefaultCommunicationMessage: disabled,
              }),
            })}
          </div>
          {isStopCommunication && (
            <StopCommunicationModal
              communicationType={communicationType}
              visible={isModalVisible}
              onCancel={closeModal}
              onHandleClick={() => handleOnClickWithCoordinate()}
            />
          )}
          <BloctelModal
            communicationType={communicationType}
            visible={isbloctelModalVisible}
            onCancel={closeBloctelModal}
            onSubmitModal={() => handleOnClickWithCoordinate()}
          />
          <ConfirmationModal
            isVisible={isOptedOutVisible}
            title={__('Call to Outed out Phone No.')}
            onSubmit={() => handleOnClickWithCoordinate()}
            onCancel={closeOptedOutModal}
            content={__('This number was Opted Out by the customer. Do you want to continue making this call?')}
          />
        </>
      );
    }
    return renderIconWithCoordinateSelector();
  };

  if (shouldRenderCommunicationIcon) {
    return <></>;
  }
  return renderCommunicationIcon();
};

CommunicationIcon.propTypes = {
  leadDetails: PropTypes.object,
  currentCallDetails: PropTypes.object,
  disabled: PropTypes.bool,
  entityType: PropTypes.string,
  entityPayload: PropTypes.object,
  className: PropTypes.string,
  communicationType: PropTypes.string,
  coordinate: PropTypes.string,
  render: PropTypes.func,
  afterTaskComplete: PropTypes.func,
  preferenceChannel: PropTypes.string,
  containerNodeSelector: PropTypes.string,
  shouldDisplayDropdown: PropTypes.bool,
  externalTemplate: PropTypes.object,
  getExternalTemplate: PropTypes.func,
  onAction: PropTypes.func,
  dataTest: PropTypes.string,
  deepLinkWidgetMetaData: PropTypes.object,
  isCTCDisabled: PropTypes.bool,
};

CommunicationIcon.defaultProps = {
  leadDetails: EMPTY_OBJECT,
  currentCallDetails: EMPTY_OBJECT,
  disabled: false,
  entityType: ENTITY_TYPES.LEAD,
  entityPayload: EMPTY_OBJECT,
  className: EMPTY_STRING,
  communicationType: EMPTY_STRING,
  coordinate: EMPTY_STRING,
  preferenceChannel: PRIVACY_PREFERENCES.PURCHASE_INQUIRY_FOLLOWUPS,
  render: _noop,
  containerNodeSelector: EMPTY_STRING,
  shouldDisplayDropdown: true,
  externalTemplate: EMPTY_OBJECT,
  getExternalTemplate: undefined,
  onAction: _noop,
  dataTest: '',
  deepLinkWidgetMetaData: EMPTY_OBJECT,
  isCTCDisabled: false,
};

export default compose(
  memo,
  withCallCommunication,
  withCommunicationPreferenceFields,
  withActions(INITIAL_STATE, ACTION_HANDLERS)
)(CommunicationIcon);
