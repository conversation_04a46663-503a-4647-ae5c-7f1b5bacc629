import _map from 'lodash/map';

import AppointmentReader from '@tekion/tekion-business/src/appServices/crm/readers/appointment.reader';
import { getAllContactNumbersOfLeads } from '@tekion/tekion-business/src/helpers/crm/lead.helpers';

import { DEPARTMENTS, makeCommunicationPreferencePayload } from '../organisms/ndc';

const createFetchCommunicationPreferencesPayload = appointmentWithEntity => {
  const entityId = AppointmentReader.entityId(appointmentWithEntity);
  const entityType = AppointmentReader.entityType(appointmentWithEntity);
  const appointmentWithEntityInfo = AppointmentReader.lookupEntityInfo(appointmentWithEntity);
  const contacts = getAllContactNumbersOfLeads(appointmentWithEntityInfo);
  const communicationPreferencePayload = makeCommunicationPreferencePayload({
    coordinates: contacts,
    entityType,
    entityId,
    department: DEPARTMENTS.COMMUNICATION_RETAIL,
  });
  return communicationPreferencePayload;
};

export const makeFetchCommunicationPreferencesPayload = appointmentsWithEntityInfo => {
  const communicationPreferences = _map(appointmentsWithEntityInfo, createFetchCommunicationPreferencesPayload);
  return { requests: communicationPreferences };
};
