import { isInchcape, isInchcapeOrRRG, isRRG } from '@tekion/tekion-base//utils/sales/dealerProgram.utils';
import DealerPropertyHelper from '@tekion/tekion-components//helpers/sales/dealerPropertyHelper';

import SalesDealerPropertyHelper from '../../../../appServices/sales/helpers/salesDealerPropertyHelper';

export default {
  shouldBuildVehicleFromChromeConstruct: () => !isInchcapeOrRRG(),
  shouldFetchDataForCAPProvider: () => isInchcape(),
  shouldFetchDataForNatCode: () => isRRG(),
  shouldShowOptionAction: () => !DealerPropertyHelper.isViArcLiteEnabled() && !DealerPropertyHelper.isAECProgram(),
  isVehicleOptionsEnabled: () => DealerPropertyHelper.isVehicleOptionsEnabled() || SalesDealerPropertyHelper.isVehicleProfileEnabled(),
  shouldShowEUV2Options: isInchcape,
  shouldShowDealerAddOnsOption: () => SalesDealerPropertyHelper.isVehicleProfileEnabled(),
  isFormatterRequired: () => !DealerPropertyHelper.isViV2Enabled(),
};
