import { defaultMemoize } from 'reselect';
import _isEmpty from 'lodash/isEmpty';
import _size from 'lodash/size';
import _compact from 'lodash/compact';
import _find from 'lodash/find';
import _values from 'lodash/values';
import _filter from 'lodash/filter';
import _get from 'lodash/get';
import _uniqBy from 'lodash/uniqBy';
import _map from 'lodash/map';
import _forEach from 'lodash/forEach';
import _concat from 'lodash/concat';
import _reduce from 'lodash/reduce';
import _slice from 'lodash/slice';
import _difference from 'lodash/difference';
import _last from 'lodash/last';
import _omit from 'lodash/omit';
import _pick from 'lodash/pick';
import _isNull from 'lodash/isNull';
import _includes from 'lodash/includes';
import _head from 'lodash/head';
import _sortBy from 'lodash/sortBy';
import _keyBy from 'lodash/keyBy';
import _keys from 'lodash/keys';
import _mapValues from 'lodash/mapValues';
import _intersection from 'lodash/intersection';
import _take from 'lodash/take';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { VEHICLE_TYPES, USAGE_TARGETTING_MAKEID } from '@tekion/tekion-base/constants/deal/formSetup';
import { MODULE_TARGETTING_TYPES } from '@tekion/tekion-base/constants/formConfigurator/constants';
import { MARKET_CLASS_CATEGORY_OPTIONS } from '@tekion/tekion-base/constants/vehicleInventory/trim';
import { getFormattedMake } from '@tekion/tekion-base/helpers/vehicle.helper';
import { getTimeStamp, startOfDay, isMomentValid, toMoment, isValidDate } from '@tekion/tekion-base/utils/dateUtils';
import { orderOptionAscendingly } from '@tekion/tekion-base/utils/sales';
import { getPaymentTypeOptions } from '@tekion/tekion-business/src/helpers/sales/paymentType.helper';
// import { FNI_PROVIDER_SOURCE } from '@tekion/tekion-base/constants/deal/fnis';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

import { E_CONTRACTING_FORM_FIELD, E_CONTRACT_FORM_TYPES, VAULTING_PARTNERS } from '../../constants/eContractForm';
import {
  DEALER_CERTIFIED_STATUS,
  OEM_CERTIFIED_STATUS,
  REQUIRED_FORM_FIELDS,
  SALESPERSON_OPTIONS,
  REGEX_FOR_VALID_FORMNAME,
  USAGE_CATEGORY,
  USAGE_CATEGORY_VS_MODULE,
  FORM_FIELD,
  USAGE_CATEGORY_TREE,
  USAGE_RULES_LABEL,
} from './AddForm.constants';
import { EDIT_TYPE_PERMISSIONS } from '../formConfiguratorTool/FormConfiguratorTool.constants';

export const shouldActionDisabled = (formValue, errors, customFields = REQUIRED_FORM_FIELDS) =>
  !_isEmpty(_find(customFields, field => !formValue[field] || _isEmpty(formValue[field]))) ||
  _size(_compact(_values(errors)));

export const getNewTargettingData = () => ({ operator: 'INCLUDE' });
const getProductIdProviderId = fni => ({
  productId: _get(fni, 'product.id'),
  providerId: _get(fni, 'provider.id'),
});
const combineId = (id1, id2) => `${id1}${id2}`;
const getFnIName = fnI => _get(fnI, 'product.displayName') || '';
const getDueBillName = dueBill => _get(dueBill, 'name');
const getDueBillId = dueBill => _get(dueBill, 'code');
const getFNIId = fni => {
  const { productId, providerId } = getProductIdProviderId(fni);
  return combineId(productId, providerId);
};

// const getFnIFromPenSource = fnI => _get(fnI, 'provider.source') === FNI_PROVIDER_SOURCE.PEN;
const getStateId = state => _get(state, 'id');
const getStateName = state => _get(state, 'name');

const getFormattedUsageTargetParams = (usageTargetParams, module) =>
  _isEmpty(usageTargetParams)
    ? [getNewTargettingData()]
    : _map(
        _filter(usageTargetParams || [], targetParam => {
          const usageCategory = _get(targetParam, 'usageCategory', EMPTY_STRING);
          return (
            usageCategory !== USAGE_TARGETTING_MAKEID &&
            (_isEmpty(usageCategory) || _includes(_get(USAGE_CATEGORY_VS_MODULE, module, EMPTY_ARRAY), usageCategory))
          );
        }),
        ({ operator, ...rest }) => {
          const { usageCategory, rangeRuleParams } = rest;
          let { usageRules } = rest;
          switch (usageCategory) {
            case USAGE_CATEGORY.FNI:
              {
                const { providerRuleParams } = rest;
                usageRules = (providerRuleParams || []).map(({ productId, providerId }) =>
                  combineId(productId, providerId)
                );
              }
              break;
            case USAGE_CATEGORY.DUE_BILLS:
              {
                const { accRuleParams } = rest;
                usageRules = (accRuleParams || []).map(({ accCode }) => accCode);
              }
              break;
            default:
          }
          if (!_isEmpty(rangeRuleParams)) {
            const { lowerBound, upperBound, rangeRuleDescription } = _head(rangeRuleParams) || EMPTY_OBJECT;
            if (rangeRuleDescription === USAGE_CATEGORY.SELLING_PRICE_RANGE) {
              return {
                ...rest,
                rangeRuleParams: null,
                usageCategory: rangeRuleDescription,
                usageRules: [lowerBound, upperBound],
                operator: operator || 'INCLUDE',
              };
            }
          }
          return { ...rest, usageRules, operator: operator || 'INCLUDE' };
        }
      );

export const getUsageTargetParamsFromPayload = formValue =>
  _reduce(
    _get(formValue, FORM_FIELD.TARGETED_MODULES, EMPTY_ARRAY),
    (allUsageTargetParams, module) => {
      switch (module) {
        case MODULE_TARGETTING_TYPES.DESKING:
          return {
            ...allUsageTargetParams,
            [module]: getFormattedUsageTargetParams(
              _get(formValue, FORM_FIELD.USAGE_TARGET_PARAMS, EMPTY_ARRAY),
              module
            ),
          };

        case MODULE_TARGETTING_TYPES.CRM:
          return {
            ...allUsageTargetParams,
            [module]: getFormattedUsageTargetParams(
              _get(formValue, FORM_FIELD.CRM_USAGE_TARGET_PARAMS, EMPTY_ARRAY),
              module
            ),
          };
        case MODULE_TARGETTING_TYPES.DIGITAL_RETAILING:
          return {
            ...allUsageTargetParams,
            [module]: getFormattedUsageTargetParams(
              _get(formValue, FORM_FIELD.DIGITAL_RETAILING_USAGE_TARGET_PARAMS, EMPTY_ARRAY),
              module
            ),
          };
        case MODULE_TARGETTING_TYPES.ACCOUNTING:
          return {
            ...allUsageTargetParams,
            [module]: getFormattedUsageTargetParams(
              _get(formValue, FORM_FIELD.ACCOUNTING_USAGE_TARGET_PARAMS, EMPTY_ARRAY),
              module
            ),
          };

        default:
          return allUsageTargetParams;
      }
    },
    {}
  );

export const getFNIOptions = fnIs =>
  (fnIs || []).map(fnI => {
    const label = getFnIName(fnI);
    const value = getFNIId(fnI);
    return { label, value };
  });

export const getDueBillsOptions = dueBills =>
  (dueBills || []).map(dueBill => {
    const label = getDueBillName(dueBill);
    const value = getDueBillId(dueBill);
    return { label, value };
  });

export const getStateOptions = states =>
  states.map(state => ({
    label: getStateName(state),
    value: getStateId(state),
  }));

export const getOptionsForMake = defaultMemoize(makes =>
  orderOptionAscendingly(
    _compact(
      _map(_compact(_uniqBy(makes)), make => ({
        label: make?.displayMake || getFormattedMake(make?.make),
        value: make?.make,
      }))
    )
  )
);

const getFuelTypeOptions = fuelTypes => (fuelTypes || []).map(fuelType => ({ label: fuelType, value: fuelType }));

export function formatModelOptions(models) {
  if (_isEmpty(models)) {
    return EMPTY_ARRAY;
  }
  return _uniqBy(
    models.map(model => ({
      value: model.model,
      label: model.model,
    })),
    'value'
  );
}

const getLenderTypeOptions = lenderTypes =>
  _compact(
    (lenderTypes || []).map(lenderType => !lenderType.hidden && { label: lenderType.displayName, value: lenderType.id })
  );

const getDealTypeOptions = dealTypeConfigs =>
  (dealTypeConfigs || [])
    .filter(dealTypeConfig => dealTypeConfig.enabled === true)
    .map(dealTypeConfig => ({ label: dealTypeConfig.displayName, value: dealTypeConfig.dealType }));

const getCustomStatus = options =>
  (options || [])
    .filter(option => option.enabled === true)
    .map(option => ({ label: option.status, value: option.status }));

export const makeVehicleTypeOptions = defaultMemoize(vehicleTypes => {
  let options = [];
  _forEach(vehicleTypes, type => {
    const { enabled, subTypes } = type;
    if (enabled) {
      const subTypeOptions = _map(subTypes, ({ name }) => ({ label: name, value: name }));
      options = _concat(options, subTypeOptions);
    }
  });
  return options;
});

export const getVehicleSubTypeOptionsByType = defaultMemoize((stockTypes, vehicleType) =>
  _reduce(
    stockTypes,
    (allSubTypes, { subTypes, enabled, type }) => {
      const vehicleSubTypes =
        enabled && (vehicleType === VEHICLE_TYPES.ALL || type === vehicleType)
          ? _map(subTypes, ({ name }) => ({ label: name, value: name }))
          : [];
      return [...allSubTypes, ...vehicleSubTypes];
    },
    []
  )
);

export const getTrimsOptions = (trims = []) => trims.map(trim => ({ label: trim, value: trim }));

export const getDealStatusOptions = (dealStatus = EMPTY_ARRAY) =>
  _map(dealStatus, status => ({ label: status?.displayName, value: status?.dealStatusKey }));

export const getFilteredOptionsFromUsageCategoryTree = (category, numberOfItems = 0) => {
  const options = _map(USAGE_CATEGORY_TREE[category], option => ({
    label: USAGE_RULES_LABEL[option],
    value: option,
  }));
  return _take(options, numberOfItems);
};

export const getDynamicUsageCategoryVsRulesOptions = ({
  fnIs,
  dueBills,
  states = [],
  makes,
  models,
  fuelTypes,
  lenderTypes,
  dealTypeConfigs,
  vehicleTypes,
  customStatus,
  siteOptions,
  trimsList,
  paymentOptionConfigs,
  dealStatus,
  tradeInCount,
}) => ({
  [USAGE_CATEGORY.FNI]: getFNIOptions(fnIs),
  [USAGE_CATEGORY.DUE_BILLS]: getDueBillsOptions(dueBills),
  [USAGE_CATEGORY.STATE]: states,
  [USAGE_CATEGORY.DEALERSHIP_STATE]: states,
  [USAGE_CATEGORY.APPLICANT_GARAGING_STATE]: states,
  [USAGE_CATEGORY.CO_APPLICANT_STATE]: states,
  [USAGE_CATEGORY.CO_APPLICANT_GARAGING_STATE]: states,
  [USAGE_CATEGORY.MAKE]: getOptionsForMake(makes, DealerPropertyHelper.standardizedMakeSetupEnabled()),
  [USAGE_CATEGORY.MODEL]: formatModelOptions(models),
  [USAGE_CATEGORY.VEHICLE_FUEL_TYPE]: getFuelTypeOptions(fuelTypes),
  [USAGE_CATEGORY.LENDER]: getLenderTypeOptions(lenderTypes),
  [USAGE_CATEGORY.DEAL_TYPE]: getDealTypeOptions(dealTypeConfigs),
  [USAGE_CATEGORY.VEHICLE_SUB_TYPE]: makeVehicleTypeOptions(vehicleTypes),
  [USAGE_CATEGORY.DEAL_SUB_STATUS]: getCustomStatus(customStatus),
  [USAGE_CATEGORY.SITE_SOLD]: siteOptions,
  [USAGE_CATEGORY.TRADE_IN_SUB_TYPE]: getVehicleSubTypeOptionsByType(vehicleTypes, VEHICLE_TYPES.ALL),
  [USAGE_CATEGORY.SOLD_VEHICLE_SUB_TYPE]: getVehicleSubTypeOptionsByType(vehicleTypes, VEHICLE_TYPES.USED),
  [USAGE_CATEGORY.SOLD_VEHICLE_BODY_CLASS]: MARKET_CLASS_CATEGORY_OPTIONS,
  [USAGE_CATEGORY.DEALER_CERTIFIED]: DEALER_CERTIFIED_STATUS,
  [USAGE_CATEGORY.OEM_CERTIFIED]: OEM_CERTIFIED_STATUS,
  [USAGE_CATEGORY.SALESPERSON]: SALESPERSON_OPTIONS,
  [USAGE_CATEGORY.TRIM]: getTrimsOptions(trimsList),
  [USAGE_CATEGORY.PAYMENT]: getPaymentTypeOptions(paymentOptionConfigs),
  [USAGE_CATEGORY.DEAL_STATUS]: getDealStatusOptions(dealStatus),
  [USAGE_CATEGORY.TRADE_INS]: getFilteredOptionsFromUsageCategoryTree(USAGE_CATEGORY.TRADE_INS, tradeInCount),
  [USAGE_CATEGORY.TRADE_IN_PAY_OFF]: getFilteredOptionsFromUsageCategoryTree(
    USAGE_CATEGORY.TRADE_IN_PAY_OFF,
    tradeInCount
  ),
});

export function getNumber(stringData) {
  const result = Number(stringData);
  return !result || isNaN(result) ? 0 : result; //eslint-disable-line
}

export const getStartOfDayTimeStamp = momentObj => {
  if (isMomentValid(momentObj)) return getTimeStamp(startOfDay(momentObj));
  return null;
};

export const isSupportedFileName = pdfName => {
  const validFormNameRegex = REGEX_FOR_VALID_FORMNAME;
  return validFormNameRegex.test(pdfName);
};

export const getUpdatedLabels = ({ allLabels = EMPTY_ARRAY, recentLabels = EMPTY_ARRAY, newLabels = EMPTY_ARRAY }) => {
  const newlyAddedLabels = _difference(newLabels, [...allLabels, ...recentLabels]);
  let oldRecentLabels = recentLabels;
  let removedRecentLabel = null;
  if (_size(newlyAddedLabels) === 0) return { newRecentLabels: recentLabels, newAllLabels: allLabels };
  if (_size(recentLabels) > 4) {
    oldRecentLabels = _slice(recentLabels, 0, 4);
    removedRecentLabel = _last(recentLabels);
  }

  const newRecentLabels = [...newlyAddedLabels, ...oldRecentLabels];
  const newAllLabels = _compact([removedRecentLabel, ...allLabels]);
  return { newRecentLabels, newAllLabels };
};

export const disableFormSetupField = (isFormsLibrary, editType) => {
  if (isFormsLibrary) return false;
  if (editType === EDIT_TYPE_PERMISSIONS.GLOBAL_EDIT) {
    return false;
  }

  if (
    editType === EDIT_TYPE_PERMISSIONS.TEKION_RESTRICTED_EDIT ||
    editType === EDIT_TYPE_PERMISSIONS.DP_RESTRICTED_EDIT
  ) {
    return true;
  }

  return true;
};

export const isFieldRequired = defaultMemoize((fieldId, type, isVaultingRequired, vaultingPartner, econtractForm) => {
  switch (fieldId) {
    case E_CONTRACTING_FORM_FIELD.TYPE:
      return econtractForm;
    case E_CONTRACTING_FORM_FIELD.AGENCY:
      return (
        econtractForm &&
        type === E_CONTRACT_FORM_TYPES.CONTRACT_FORM &&
        isVaultingRequired &&
        vaultingPartner === VAULTING_PARTNERS.ODE
      );
    case E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER:
    case E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE:
    case E_CONTRACTING_FORM_FIELD.STATE:
    case E_CONTRACTING_FORM_FIELD.PAYMENT_TYPE:
    case E_CONTRACTING_FORM_FIELD.FORM_IDENTIFIER:
      return econtractForm && type === E_CONTRACT_FORM_TYPES.CONTRACT_FORM && isVaultingRequired;
    default:
      return false;
  }
});

export const getCustomRequiredFields = ({ econtractForm, formValues, requiredFormFields = REQUIRED_FORM_FIELDS }) =>
  _concat(
    requiredFormFields,
    _filter(E_CONTRACTING_FORM_FIELD, fieldId =>
      isFieldRequired(
        fieldId,
        _get(formValues, E_CONTRACTING_FORM_FIELD.TYPE, EMPTY_STRING),
        _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED, false),
        _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER, EMPTY_STRING),
        econtractForm
      )
    )
  );

export const addEContractFormValues = formValues => {
  const formRevisionDate = _get(formValues, ['econtractFormDetails', 'vaultingDetails', 'formRevisionDate'], null);
  return {
    ..._omit(formValues, ['econtractFormDetails', 'econtractForm']),
    ..._omit(_get(formValues, 'econtractFormDetails', EMPTY_OBJECT), 'vaultingDetails'),
    ..._omit(_get(formValues, ['econtractFormDetails', 'vaultingDetails'], EMPTY_OBJECT), 'formRevisionDate'),
    ...(!_isNull(formRevisionDate)
      ? { [E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE]: toMoment(formRevisionDate) }
      : EMPTY_OBJECT),
  };
};

export const removeEContractFormValues = formValues => _omit(formValues, _values(E_CONTRACTING_FORM_FIELD));

export const getEContractingFormDetails = formValues => {
  const type = _get(formValues, E_CONTRACTING_FORM_FIELD.TYPE, EMPTY_STRING);
  const isVaultingRequired = _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED, false);
  const vaultingPartner = _get(formValues, E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER, EMPTY_STRING);
  const isContractForm = type === E_CONTRACT_FORM_TYPES.CONTRACT_FORM;

  const eContractFormValues = {
    [E_CONTRACTING_FORM_FIELD.TYPE]: type,
    [E_CONTRACTING_FORM_FIELD.VAULTING_REQUIRED]: isContractForm ? isVaultingRequired : false,
    vaultingDetails: null,
  };

  if (isContractForm && isVaultingRequired) {
    eContractFormValues.vaultingDetails = _pick(formValues, [
      E_CONTRACTING_FORM_FIELD.VAULTING_PARTNER,
      E_CONTRACTING_FORM_FIELD.STATE,
      E_CONTRACTING_FORM_FIELD.PAYMENT_TYPE,
    ]);

    const formRevisionDate = _get(formValues, E_CONTRACTING_FORM_FIELD.FORM_REVISION_DATE, null);
    if (formRevisionDate && toMoment(formRevisionDate) && isValidDate(formRevisionDate)) {
      eContractFormValues.vaultingDetails.formRevisionDate = Number(getTimeStamp(formRevisionDate));
    }
  } else return eContractFormValues;

  if (vaultingPartner === VAULTING_PARTNERS.ODE) {
    eContractFormValues.vaultingDetails[E_CONTRACTING_FORM_FIELD.AGENCY] = formValues[E_CONTRACTING_FORM_FIELD.AGENCY];
  }

  return eContractFormValues;
};

export const getModuleVisibilityRulesFromResponse = formValues =>
  _reduce(
    _get(formValues, FORM_FIELD.MODULE_VISIBILITY_RULES, []),
    (rules, { queryParamsMap, moduleName } = EMPTY_OBJECT) => ({
      ...rules,
      [moduleName]: _get(queryParamsMap, 'VehicleVisibilityTargeting', []),
    }),
    {}
  );

export const formattedSelectFormsList = formattedFormsList =>
  formattedFormsList.map(documentType => ({
    label: documentType?.friendlyName,
    value: documentType?.typeCode,
  }));

export const getFormattedFormsList = formsList => {
  const OTHER_FORM_TYPE = 'Other';
  const otherFormItem = _find(formsList, formItem => formItem?.friendlyName === OTHER_FORM_TYPE);
  const formattedFormsList = [
    ..._sortBy(
      _filter(formsList, formItem => !formItem?.friendlyName.startsWith(OTHER_FORM_TYPE)),
      'friendlyName'
    ),
    otherFormItem,
  ];
  return formattedFormsList;
};

export const getOptionsFromUsageCategoryTree = () =>
  _reduce(
    USAGE_CATEGORY_TREE,
    (usageOptions, categoryOptions, category) => ({
      ...usageOptions,
      [category]: _map(categoryOptions, categoryOption => ({
        label: USAGE_RULES_LABEL[categoryOption],
        value: categoryOption,
      })),
    }),
    {}
  );

export const getOptionsForCategories = metadata => {
  const categories = _keyBy(_get(metadata, 'category'), 'key');
  return _reduce(
    categories,
    (usageOptions, categoryOptions, category) => {
      const categoryValues = _get(categoryOptions, 'values');
      return {
        ...usageOptions,
        [category]: _map(categoryValues, option => ({
          label: option.displayName,
          value: option.key,
        })),
      };
    },
    {}
  );
};
export const disableFormSetupBasedOnField = ({ field, disableField, isFormsLibrary, editType }) => {
  if (isFormsLibrary) return disableField;
  if (field === FORM_FIELD.MANDATORY_SIGNATURE_TYPE) {
    if (editType === EDIT_TYPE_PERMISSIONS.GLOBAL_EDIT || editType === EDIT_TYPE_PERMISSIONS.TEKION_RESTRICTED_EDIT) {
      return false;
    }
    if (editType === EDIT_TYPE_PERMISSIONS.DP_RESTRICTED_EDIT) {
      return true;
    }
    return true;
  }
  return disableField;
};

export const getWorkflowTargetingData = workflowTargettingModules => {
  if (!_isEmpty(workflowTargettingModules)) {
    const workflowTargettedModules = _head(_keys(workflowTargettingModules));
    const workflowTargetted = [_get(workflowTargettingModules, [workflowTargettedModules, '0', 'workFlow'])];
    return {
      workflowTargettedModules,
      workflowTargetted,
    };
  }
  return {
    workflowTargettedModules: EMPTY_ARRAY,
    workflowTargetted: EMPTY_ARRAY,
  };
};

export const formatWorkFlowOptionListResponse = workflowOptionList =>
  _map(workflowOptionList, workflowOption => ({
    ...workflowOption,
    value: _get(workflowOption, 'key'),
  }));

export const formattedStatesListResponse = (countriesList = EMPTY_ARRAY, statesList = EMPTY_OBJECT) =>
  _map(countriesList, country => ({
    label: country.name,
    options: _map(
      _mapValues(_get(statesList, country.code), (stateDisplayName, stateCode) => ({
        label: stateDisplayName,
        value: stateCode,
      })),
      transformedStateList => transformedStateList
    ),
  }));

export const getCountryCodes = (states, selectedStates) =>
  _reduce(
    _keys(states),
    (result, countryCode) => {
      if (_intersection(_keys(_get(states, countryCode)), selectedStates).length) {
        result.push(countryCode);
      }
      return result;
    },
    []
  );

export const formattedCountriesList = (countriesList = EMPTY_ARRAY) =>
  _map(countriesList, country => ({
    label: country?.name,
    value: country?.code,
  }));

export const formattedStatesList = (statesList = EMPTY_OBJECT) =>
  _reduce(
    _keys(statesList),
    (result, countryCode) => {
      _mapValues(_get(statesList, countryCode), (stateDisplayName, stateCode) =>
        result.push({
          label: stateDisplayName,
          value: stateCode,
        })
      );
      return result;
    },
    []
  );
