import React from 'react';
import { connect } from 'react-redux';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';

import DEALER_PROPERTY_CONSTANTS from '@tekion/tekion-base/constants/dealerProperties';

import {
  fetchPrinterTypes,
  getDocumentTypeList,
  fetchFuelTypes,
  getLegalProviders,
  fetchTrimList,
  fetchDealerSites,
  getVehicleMake,
  getAllVehicleMakesAndModels,
} from './AddForm.actions';

function withAddFormActions(ComposedComponent) {
  const WithAddFormActions = props => {
    const {
      fetchPrinterTypesAction,
      fetchDocTypeAction,
      fetchS3objectsAction,
      fetchFuelTypesAction,
      getVehicleMakeAction,
      getAllVehicleMakesAndModelsAction,
      fetchDealerSitesAction,
      getLegalProvidersAction,
      fetchTrimListAction,
      getDealerPropertyValue,
    } = props;

    const initializeAddForm = async isDealerTrackEnabled => {
      const promises = [
        fetchPrinterTypesAction(),
        fetchDocTypeAction(isDealerTrackEnabled),
        fetchFuelTypesAction(),
        getVehicleMakeAction(),
        getAllVehicleMakesAndModelsAction(),
        getLegalProvidersAction(),
        fetchTrimListAction(),
        fetchS3objectsAction(),
      ];
      if (getDealerPropertyValue(DEALER_PROPERTY_CONSTANTS.MULTI_OEM_SWITCH_ENABLED)) {
        promises.push(fetchDealerSitesAction());
      }
      await Promise.all(promises);
    };
    return <ComposedComponent {...props} initializeAddForm={initializeAddForm} />;
  };

  WithAddFormActions.propTypes = {
    fetchPrinterTypesAction: PropTypes.func,
    fetchDocTypeAction: PropTypes.func,
    fetchS3objectsAction: PropTypes.func,
    fetchFuelTypesAction: PropTypes.func,
    getVehicleMakeAction: PropTypes.func,
    getAllVehicleMakesAndModelsAction: PropTypes.func,
    fetchSalesSetupInfoAction: PropTypes.func,
    getViSettingsAction: PropTypes.func,
    fetchDealerSitesAction: PropTypes.func,
    getLegalProvidersAction: PropTypes.func,
    fetchTrimListAction: PropTypes.func,
    getDealerPropertyValue: PropTypes.func,
  };

  WithAddFormActions.defaultProps = {
    fetchPrinterTypesAction: _noop,
    fetchDocTypeAction: _noop,
    fetchS3objectsAction: _noop,
    fetchFuelTypesAction: _noop,
    getVehicleMakeAction: _noop,
    getAllVehicleMakesAndModelsAction: _noop,
    fetchSalesSetupInfoAction: _noop,
    getViSettingsAction: _noop,
    fetchDealerSitesAction: _noop,
    getLegalProvidersAction: _noop,
    fetchTrimListAction: _noop,
    getDealerPropertyValue: _noop,
  };

  const mapDispatchToProps = {
    fetchPrinterTypesAction: fetchPrinterTypes,
    fetchDocTypeAction: getDocumentTypeList,
    fetchFuelTypesAction: fetchFuelTypes,
    getVehicleMakeAction: getVehicleMake,
    getAllVehicleMakesAndModelsAction: getAllVehicleMakesAndModels,
    fetchDealerSitesAction: fetchDealerSites,
    getLegalProvidersAction: getLegalProviders,
    fetchTrimListAction: fetchTrimList,
  };
  return connect(null, mapDispatchToProps)(WithAddFormActions);
}

export default withAddFormActions;
