import _isEmpty from 'lodash/isEmpty';
import _chunk from 'lodash/chunk';
import _get from 'lodash/get';
import _sortBy from 'lodash/sortBy';
import _map from 'lodash/map';
import _reduce from 'lodash/reduce';
import _values from 'lodash/values';
import _orderBy from 'lodash/orderBy';
import _uniqBy from 'lodash/uniqBy';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import { getFormattedMake } from '@tekion/tekion-base/helpers/vehicle.helper';
import TEnvReader from '@tekion/tekion-base/readers/Env';
import { toaster } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';

import * as AddFormAPI from './AddForm.api';
import ACTION_TYPES from './AddForm.actionTypes';
import { getFormattedFormsList } from './AddForm.utils';

export const fetchPrinterTypes = () => async dispatch => {
  const response = await AddFormAPI.fetchPrinterTypes();

  if (response) {
    dispatch({
      type: ACTION_TYPES.SET_PRINTER_TYPES,
      payload: response,
    });
  }
  return response;
};

export const getDocumentTypeList = isDealerTrackEnabled => async dispatch => {
  if (isDealerTrackEnabled) {
    const response = await AddFormAPI.getDocumentTypeList();
    const formsList = response?.data?.documentsList;
    const formattedFormsList = getFormattedFormsList(formsList);

    if (!_isEmpty(formattedFormsList)) {
      dispatch({
        type: ACTION_TYPES.SET_DOC_TYPE,
        payload: formattedFormsList,
      });
    }
    return formattedFormsList;
  }
  return EMPTY_ARRAY;
};

export const fetchFuelTypes = () => async dispatch => {
  const data = await AddFormAPI.getFuelTypes();
  const fuelTypes = _get(data, 'response.fueltype', EMPTY_ARRAY);
  if (fuelTypes) {
    dispatch({
      type: ACTION_TYPES.GET_FUEL_TYPES,
      payload: fuelTypes,
    });
  }
  return fuelTypes;
};

export const validatePrintSequence = async payload => {
  const response = await AddFormAPI.validatePrintSequence(payload);
  return response;
};

export const fetchDealerSites = () => async dispatch => {
  const userInfo = TEnvReader.userInfo();
  const { dealerId } = userInfo;
  const dealerSites = await AddFormAPI.fetchDealerSites(dealerId);
  if (dealerSites) {
    dispatch({
      type: ACTION_TYPES.FETCH_DEALER_SITES,
      payload: dealerSites,
    });
  }
};

export const getLegalProviders = () => async dispatch => {
  const payload = await AddFormAPI.getLegalProviders();
  dispatch({ type: ACTION_TYPES.GET_LEGAL_PROVIDERS, payload });
};

export const fetchTrimList = () => async (dispatch, getState) => {
  try {
    const trims = _get(getState(), 'vehicleSalesLog.filters.trims');
    if (!_isEmpty(trims)) return;

    const response = await AddFormAPI.getTrimList();
    dispatch({
      type: ACTION_TYPES.FETCH_TRIM_LIST,
      payload: _get(response, 'trims') || EMPTY_ARRAY,
    });
  } catch (error) {
    toaster('error', getErrorMessage(error));
  }
};

export const getVehicleMake = () => async (dispatch, getState) => {
  const vehicleMakes = _get(getState(), 'common.vehicleOptions.makes');

  if (!_isEmpty(vehicleMakes)) return vehicleMakes;
  const data = await AddFormAPI.getVehicleMake({ isStandardMakes: true });
  const vehicleMake = {
    makes: _sortBy(
      !DealerPropertyHelper.isRVDealerEnabled()
        ? _get(data, 'response.tekionMakes', EMPTY_ARRAY)?.map(makeData => ({
            displayMake: makeData?.tekionMakeDisplayName,
            makeId: makeData?.tekionMakeId,
            make: makeData?.make,
          }))
        : _get(data, 'response.makes', EMPTY_ARRAY).map(make => ({ displayMake: getFormattedMake(make), make })),
      ['displayMake']
    ),
  };
  if (data) {
    dispatch({
      type: ACTION_TYPES.GET_VEHICLE_MAKES,
      payload: vehicleMake || [],
    });
  }

  return { makes: vehicleMake?.makes?.map(makeData => makeData?.make) };
};

export const getModels = vehiclesDetailsData =>
  _orderBy(
    _reduce(
      vehiclesDetailsData,
      (acc, vehicleDetail = EMPTY_OBJECT) => {
        const allModels = _reduce(
          vehicleDetail.data,
          (result, data) => {
            const model = {
              make: data.make,
              model: data.model,
            };
            const displayModel = {
              make: data.make,
              model: data.displayModel,
            };
            const bestStyleName = {
              make: data.make,
              model: _get(data, 'bestStyleName'),
            };
            result.push(model, displayModel, bestStyleName);
            return result;
          },
          []
        );
        const uniqModels = _uniqBy([...acc, ...allModels], 'model');
        return uniqModels;
      },
      []
    ),
    ['model'],
    ['asc']
  );

export const getAllVehicleMakesAndModels = () => async dispatch => {
  const makesResponse = await AddFormAPI.getVehicleMake({ isStandardMakes: true });
  if (makesResponse) {
    const makes = !DealerPropertyHelper.isRVDealerEnabled()
      ? _get(makesResponse, 'response.tekionMakes', EMPTY_ARRAY)?.map(makeData => makeData?.make)
      : _get(makesResponse, 'response.makes', EMPTY_ARRAY);

    const chunkedVehicleMakes = _chunk(makes, 30);
    const responses = await Promise.all(_map(chunkedVehicleMakes, make => AddFormAPI.getVehicleModel({ makes: make })));
    const models = _reduce(
      responses,
      (acc, data) => ({ ...acc, ..._get(data, 'response', EMPTY_OBJECT) }),
      EMPTY_OBJECT
    );
    dispatch({
      type: ACTION_TYPES.SET_ALL_VEHICLE_OPTIONS,
      payload: { makes, models: getModels(_values(models)) },
    });
  }
};
