import Http from '@tekion/tekion-base/services/apiService/httpClient';
import { URL_TYPES } from '@tekion/tekion-base/constants/api';
import { INVENTORY_SERVICE, DEAL_SERVICE } from '@tekion/tekion-base/constants/deal/endPoint';
import DealerPropertyHelper from '@tekion/tekion-components/src/helpers/sales/dealerPropertyHelper';
import _get from 'lodash/get';
import _replace from 'lodash/replace';
import _property from 'lodash/property';

const API_PATHS = {
  VALIDATE_PRINT_SEQUENCE: 'print/u/form/maxPrintSequence/',
  FORM_SCAN_PDF_PREVIEW: 'print/u/forms/{formScanKey}/previewWithoutPayload',
  PRINTER_TYPES: 'print/u/printer/printerTypes',
  GET_LEGAL_PROVIDERS: 'print/u/legalProviders',
  GET_TRIM_LIST: `${INVENTORY_SERVICE}/u/v1.0.0/distinct/trim`,
  FETCH_DEALER_SITES: '/userservice/u/dealer/oem-sites',
  GET_FUEL_TYPES: '/vi/u/tdp/fueltype',
  GET_VEHICLE_MAKES_V2: '/vi/u/tdp/makes/v2?unsupportedMakes=true',
  GET_MODELS: '/vi/u/tdp/make/media',
  GET_STATE_LIST: 'settings/u/global/states/{countryCode}',
  GET_DOCUMENT_TYPE_LIST: `${DEAL_SERVICE}/u/sales-credit/lender/document-types/`,
  GET_WORK_FLOW_TARGETING_MODULES: '/sales/core/u/deal/workFlow/FORM',
};

const responseData = _property('data.data');
const cdmsErrorCode = _property('status');
const cdmsErrorDetails = _property('data.errorDetails');

const successCallBack = response => {
  const status = cdmsErrorCode(response);
  if (status >= 200 && status <= 299) {
    return {
      response: responseData(response),
      rawResponse: response,
    };
  }
  return {
    error: {
      errorCode: cdmsErrorCode(response),
      detail: cdmsErrorDetails(response),
      rawError: response,
    },
  };
};

const errorCallBack = error => ({
  error: {
    errorCode: cdmsErrorCode(error),
    detail: cdmsErrorDetails(error),
    rawError: error,
  },
});

const getResponse = response => _get(response, 'response');

export const validatePrintSequence = payload =>
  Http.post(URL_TYPES.CDMS, API_PATHS.VALIDATE_PRINT_SEQUENCE, payload)
    .then(successCallBack, errorCallBack)
    .then(getResponse);

export const getFormScanPdfUrl = formScanKey =>
  Http.post(URL_TYPES.CDMS, _replace(API_PATHS.FORM_SCAN_PDF_PREVIEW, '{formScanKey}', formScanKey))
    .then(successCallBack, errorCallBack)
    .then(getResponse);

export const fetchPrinterTypes = () =>
  Http.get(URL_TYPES.CDMS, API_PATHS.PRINTER_TYPES).then(successCallBack, errorCallBack).then(getResponse);

export const getDocumentTypeList = () =>
  Http.get(URL_TYPES.CDMS, API_PATHS.GET_DOCUMENT_TYPE_LIST).then(successCallBack, errorCallBack).then(getResponse);

export const getFuelTypes = () =>
  Http.get(URL_TYPES.CDMS, API_PATHS.GET_FUEL_TYPES).then(successCallBack, errorCallBack);

export const getLegalProviders = () =>
  Http.get(URL_TYPES.CDMS, API_PATHS.GET_LEGAL_PROVIDERS).then(successCallBack, errorCallBack).then(getResponse);

export const getTrimList = () =>
  Http.post(URL_TYPES.CDMS, API_PATHS.GET_TRIM_LIST).then(successCallBack, errorCallBack).then(getResponse);

export const fetchDealerSites = dealerId =>
  Http.get(URL_TYPES.CDMS, `${API_PATHS.FETCH_DEALER_SITES}/${dealerId}`)
    .then(successCallBack, errorCallBack)
    .then(getResponse);

export const getVehicleMake = ({ isStandardMakes }) => {
  let makesQueryParams = DealerPropertyHelper.isRVDealerEnabled() ? { includeRV: 'RV' } : {};
  if (isStandardMakes) {
    makesQueryParams = { ...makesQueryParams, tekionMake: true };
  }
  return Http.get(URL_TYPES.CDMS, API_PATHS.GET_VEHICLE_MAKES_V2, makesQueryParams).then(
    successCallBack,
    errorCallBack
  );
};

export const getVehicleModel = payload =>
  Http.post(URL_TYPES.CDMS, API_PATHS.GET_MODELS, payload).then(successCallBack, errorCallBack);

export const getStateList = countryCode =>
  Http.get(URL_TYPES.CDMS, _replace(API_PATHS.GET_STATE_LIST, '{countryCode}', countryCode))
    .then(successCallBack, errorCallBack)
    .then(getResponse);

export const getWorkFlowTargetingModules = () =>
  Http.get(URL_TYPES.CDMS, API_PATHS.GET_WORK_FLOW_TARGETING_MODULES).then(successCallBack, errorCallBack);
