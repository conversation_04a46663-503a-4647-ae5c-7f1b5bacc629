import _get from 'lodash/get';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

const getStateWithReducerKey = (state, reducerKey) =>
  !_isEmpty(reducerKey) ? _get(state, reducerKey, EMPTY_OBJECT) : state;

export const getPrinterTypes = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'addForm.printerTypes') || EMPTY_ARRAY;

export const getDocTypes = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'addForm.docTypes') || EMPTY_ARRAY;

export const getFuelTypeSelector = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'addForm.fuelTypes') || EMPTY_ARRAY;

export const getLicenseProviders = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'addForm.legalProviders') || EMPTY_ARRAY;

export const getMakes = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'common.vehicleOptions.makes') || EMPTY_ARRAY;

export const getModels = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'common.vehicleOptions.allModels') || EMPTY_ARRAY;

export const getTrimsList = (state, reducerKey) =>
  _get(getStateWithReducerKey(state, reducerKey), 'addForm.trimsList') || EMPTY_ARRAY;
