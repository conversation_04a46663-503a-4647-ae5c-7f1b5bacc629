import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import cx from 'classnames';
import _get from 'lodash/get';
import _map from 'lodash/map';
import _noop from 'lodash/noop';

import { EMPTY_ARRAY, EMPTY_OBJECT, EMPTY_STRING } from '@tekion/tekion-base/app.constants';
import { MODULE_TARGETING_OPTIONS } from '@tekion/tekion-base/constants/formConfigurator/constants';
import Heading from '@tekion/tekion-components/src/atoms/Heading';
import Label from '@tekion/tekion-components/src/atoms/Label';
import Select from '@tekion/tekion-components/src/molecules/Select';

import ACTION_TYPES from '../../AddForm.actionTypes';
import { MODULE_TARGETTING_CONFIG } from '../../AddForm.config';
import UsageRules from '../usageRules';
import styles from './usageTargetting.module.scss';
import { FORM_FIELD, FORM_FIELD_LABELS } from '../../AddForm.constants';

const TAG_MODE = 'tags';

const UsageTargetting = ({
  targettedModules,
  onAction,
  usageTargetParams,
  addUsageTargetParam,
  deleteUsageTargetParam,
  updateUsageTargetParams,
  vehicleTypes,
  dynamicUsageCategoryVsRules,
  categories,
  formUsageContainerClassName,
  isGlobalForm,
}) => {
  const getUsageTargetParamsForModule = useCallback(module => _get(usageTargetParams, module, []), [usageTargetParams]);
  const getSupportedUsageCategoriesForModule = useCallback(
    module => _get(MODULE_TARGETTING_CONFIG, [module, 'usageCategories'], EMPTY_ARRAY),
    []
  );
  const getModuleTargetingTitle = useCallback(
    module => _get(MODULE_TARGETTING_CONFIG, [module, 'title'], EMPTY_STRING),
    []
  );

  const onFieldChange = payload => {
    onAction({
      type: ACTION_TYPES.ON_TARGETTING_FIELD_CHANGE,
      payload: { value: payload },
    });
  };

  return (
    <div className={cx('m-b-16', styles.formUsageContainer, formUsageContainerClassName)}>
      <Heading size={3} className={styles.headerClassName}>
        {FORM_FIELD_LABELS[FORM_FIELD.TARGETED_MODULES_LABEL]}
      </Heading>
      <div className="d-flex flex-row p-t-16 m-b-8">
        <Label>{__('Usage Module(s)')}</Label>
        <span className="required">*</span>
      </div>
      <Select
        className={styles.moduleSelectContainer}
        options={MODULE_TARGETING_OPTIONS}
        onChange={onFieldChange}
        value={targettedModules}
        mode={TAG_MODE}
      />
      {_map(targettedModules, module => (
        <>
          <Heading className="p-t-24 p-b-16" size={4}>
            {getModuleTargetingTitle(module)}
          </Heading>
          <UsageRules
            usageTargetParams={getUsageTargetParamsForModule(module)}
            supportedUsageCategories={getSupportedUsageCategoriesForModule(module)}
            addUsageTargetParam={addUsageTargetParam(module)}
            deleteUsageTargetParam={deleteUsageTargetParam(module)}
            updateUsageTargetParams={updateUsageTargetParams(module)}
            vehicleTypes={vehicleTypes}
            dynamicUsageCategoryVsRules={dynamicUsageCategoryVsRules}
            categories={categories}
            showHeading={false}
            targettedModules={targettedModules}
            isGlobalForm={isGlobalForm}
          />
        </>
      ))}
    </div>
  );
};

UsageTargetting.propTypes = {
  targettedModules: PropTypes.array,
  onAction: PropTypes.func,
  usageTargetParams: PropTypes.object,
  addUsageTargetParam: PropTypes.func,
  deleteUsageTargetParam: PropTypes.func,
  updateUsageTargetParams: PropTypes.func,
  vehicleTypes: PropTypes.array,
  dynamicUsageCategoryVsRules: PropTypes.object,
  categories: PropTypes.object,
  formUsageContainerClassName: PropTypes.string,
  isGlobalForm: PropTypes.bool,
};

UsageTargetting.defaultProps = {
  targettedModules: EMPTY_ARRAY,
  onAction: _noop,
  usageTargetParams: EMPTY_OBJECT,
  addUsageTargetParam: _noop,
  deleteUsageTargetParam: _noop,
  updateUsageTargetParams: _noop,
  vehicleTypes: EMPTY_ARRAY,
  dynamicUsageCategoryVsRules: EMPTY_OBJECT,
  categories: EMPTY_OBJECT,
  formUsageContainerClassName: EMPTY_STRING,
  isGlobalForm: false,
};

export default UsageTargetting;
