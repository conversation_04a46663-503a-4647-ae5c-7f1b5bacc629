export default {
  ADD_USAGE_TARGET_PARAMS: 'ADD_<PERSON>GE_TARGET_PARAMS',
  DELETE_USAGE_TARGET_PARAMS: 'DELETE_USAGE_TARGET_PARAMS',
  UPDATE_USAGE_TARGET_PARAMS: 'UPDATE_USAGE_TARGET_PARAMS',
  ON_FORM_SUBMIT: 'ON_FORM_SUBMIT',
  ON_FORM_MOUNT: 'ON_FORM_MOUNT',
  ON_TARGETTING_FIELD_CHANGE: 'ON_TARGETTING_FIELD_CHANGE',

  SET_PRINTER_TYPES: 'SET_PRINTER_TYPES',
  SET_FNI_OPTIONS: 'SET_FNI_OPTIONS',
  GET_ALL_PRINTERS: 'FETCH_ALL_PRINTERS',
  GET_DUE_BILLS: 'FETCH_DUE_BILLS',
  GET_FUEL_TYPES: 'FETCH_FUEL_TYPES',
  GET_VEHICLE_MAKES: 'FETCH_VEHICLE_MAKES',
  GET_VEHICLE_MODELS: 'FETCH_VEHICLE_MODELS',
  SET_META_DATA: 'SET_META_DATA',
  SET_SALES_SETUP_INFO: 'SET_SALES_SETUP_INFO',
  GET_VI_SETTINGS: 'GET_VI_SETTINGS',
  FETCH_DEALER_SITES: 'FETCH_DEALER_SITES',
  GET_LEGAL_PROVIDERS: 'GET_LEGAL_PROVIDERS',
  FETCH_TRIM_LIST: 'FETCH_TRIM_LIST',
  SET_ALL_VEHICLE_OPTIONS: 'SET_THE_ALL_VEHICLE_OPTIONS',
  ON_LABEL_CHANGE: 'ON_LABEL_CHANGE',
  SET_STATE_LIST: 'SET_THE_STATE_LIST',

  TOGGLE_ECONTRACT_FORM_PANEL: 'TOGGLE_ECONTRACT_FORM_PANEL',
  TOGGLE_ECONTRACTING_FORM_SWITCH: 'TOGGLE_ECONTRACTING_FORM_SWITCH',
  TOGGLE_DIGICERT_FORM_SWITCH: 'TOGGLE_DIGICERT_FORM_SWITCH',
  ON_VI_VISIBILITY_TARGETTING_CHANGE: 'ON_VI_VISIBILITY_TARGETTING_CHANGE',
  DIGI_CERT_ENCRYPTION_FIELD_CHANGE: 'DIGI_CERT_ENCRYPTION_FIELD_CHANGE',
  SET_DOC_TYPE: 'SET_DOC_TYPE',
  SET_DEAL_STATUS: 'SET_DEAL_STATUS',
  ON_COMMENT_CHANGES: 'ON_COMMENT_CHANGES',

  SET_WORKFLOW_TARGETTING_DATA: 'SET_WORKFLOW_TARGETTING_DATA',
  ON_WORKFLOW_TARGETTING_MODULE_CHANGE: 'ON_WORKFLOW_TARGETTING_MODULE_CHANGE',

  SHOW_SAVE_CONFIRMATION_MODAL: 'SHOW_SAVE_CONFIRMATION_MODAL',
  HIDE_SAVE_CONFIRMATION_MODAL: 'HIDE_SAVE_CONFIRMATION_MODAL',
};
