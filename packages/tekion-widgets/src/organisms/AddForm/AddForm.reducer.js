import { produce } from 'immer';
import _get from 'lodash/get';

import { EMPTY_OBJECT, EMPTY_ARRAY } from '@tekion/tekion-base/app.constants';
import ACTION_TYPES from './AddForm.actionTypes';

const DEFAULT_STATE = {
  printerTypes: [],
  docTypes: [],
  fniOptions: [],
  allPrinters: [],
  dueBills: [],
  metaData: EMPTY_OBJECT,
  viSettings: {},
  dealerSites: [],
  legalProviders: [],
  trimsList: [],
  fuelTypes: [],
  makes: [],
  tekionMakes: [],
  models: [],
};
export const ADDFORM_REDUCER_KEY = 'addForm';
export const AddFormReducer = (state = DEFAULT_STATE, action) =>
  produce(state, draft => {
    switch (action.type) {
      case ACTION_TYPES.SET_PRINTER_TYPES:
        draft.printerTypes = action.payload;
        break;

      case ACTION_TYPES.SET_DOC_TYPE:
        draft.docTypes = action.payload;
        break;
      case ACTION_TYPES.SET_FNI_OPTIONS:
        draft.fniOptions = action.payload;
        break;
      case ACTION_TYPES.GET_DUE_BILLS:
        draft.dueBills = action.payload;
        break;
      case ACTION_TYPES.SET_META_DATA:
        draft.metaData = action.payload;
        break;
      case ACTION_TYPES.GET_FUEL_TYPES:
        draft.fuelTypes = action.payload;
        break;
      case ACTION_TYPES.GET_VEHICLE_MAKES:
        draft.makes = _get(action.payload, 'makes', EMPTY_ARRAY);
        draft.tekionMakes = _get(action.payload, 'tekionMakes', EMPTY_ARRAY);
        break;
      case ACTION_TYPES.GET_VEHICLE_MODELS:
        draft.models = action.payload;
        break;
      case ACTION_TYPES.FETCH_DEALER_SITES:
        draft.dealerSites = action.payload;
        break;

      case ACTION_TYPES.GET_LEGAL_PROVIDERS:
        draft.legalProviders = action.payload;
        break;

      case ACTION_TYPES.FETCH_TRIM_LIST:
        draft.trimsList = action.payload;
        break;

      // skip default
    }
  });
