import React, { useMemo } from 'react';
import PropTypes from 'prop-types';
import _noop from 'lodash/noop';
import _isFunction from 'lodash/isFunction';

import Content from '@tekion/tekion-components/src/atoms/Content';

import EmptyListRenderer from '@tekion/tekion-components/src/molecules/EmptyListRenderer';
import PageSummary from '@tekion/tekion-components/molecules/pageSummary/PageSummary';
import PropertyControlledComponent from '@tekion/tekion-components/src/molecules/PropertyControlledComponent';

import { EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import EmptyStateUniversalSearch from '../../assets/images/emptyStateUniversalSearch.svg';

import TableManager from '../../../tableManager';
import RedirectionCta from '../redirectionCta';
import { TABLE_MANAGER_PROPS } from './listSection.constants';
import { getTableProps } from './listSection.general';

import styles from './listSection.module.scss';
import { DEFAULT_ROW_SIZE } from '../../constants/general';

const ListSection = ({
  columns,
  data,
  onAction,
  loading,
  gsEntities,
  totalCount,
  currentPage,
  activeTab,
  searchText,
  setGlobalSearchVisibility,
  renderSubHeaderRightSection,
}) => {
  const dataCount = data.length;

  const EmptyPlaceholderTitle = (
    <div className={styles.overlay}>
      <Content>{__('No results found')}</Content>
    </div>
  );

  const EmptyPlaceholder = () => (
    <EmptyListRenderer
      imageToBeRendered={EmptyStateUniversalSearch}
      title={EmptyPlaceholderTitle}
      titleSize={3}
      containerClassName={`no-data ${styles.imageContainer}`}
    />
  );

  const tableProps = useMemo(
    () => getTableProps({ dataCount, totalCount, gsEntities, loading, currentPage, searchText }),
    [dataCount, totalCount, gsEntities, loading, currentPage, searchText]
  ); // this tdProp we can send in each cell and use some values in required ones, For Eg: Customer Sentiments for a customer can be used from here

  return (
    <>
      <div className={styles.header}>
        <div className={styles.subHeader}>
          <div className="d-flex">
            <PageSummary resultsPerPage={DEFAULT_ROW_SIZE} currentPage={currentPage} totalCount={totalCount} />
            <div className="m-x-8">|</div>
            <Content>
              {__('Not the results you expected?  Adjust filter criteria or try different search keywords.')}
            </Content>
          </div>
          <PropertyControlledComponent controllerProperty={_isFunction(renderSubHeaderRightSection)}>
            {renderSubHeaderRightSection()}
          </PropertyControlledComponent>
        </div>
        <RedirectionCta
          setGlobalSearchVisibility={setGlobalSearchVisibility}
          searchText={searchText}
          activeTab={activeTab}
        />
      </div>
      <div className={styles.listContainer}>
        <TableManager
          columns={columns}
          data={data}
          tableManagerProps={TABLE_MANAGER_PROPS}
          tableProps={tableProps}
          onAction={onAction}
          EmptyPlaceholder={EmptyPlaceholder}
          searchText={searchText}
        />
      </div>
    </>
  );
};

ListSection.propTypes = {
  columns: PropTypes.array,
  data: PropTypes.array,
  onAction: PropTypes.func,
  loading: PropTypes.bool,
  gsEntities: PropTypes.object,
  currentPage: PropTypes.number,
  totalCount: PropTypes.string,
  searchText: PropTypes.string,
  setGlobalSearchVisibility: PropTypes.func,
  renderSubHeaderRightSection: PropTypes.func,
};

ListSection.defaultProps = {
  columns: [],
  data: [],
  onAction: _noop,
  gsEntities: EMPTY_OBJECT,
  currentPage: 0,
  totalCount: 0,
  searchText: '',
  loading: true,
  setGlobalSearchVisibility: _noop,
  renderSubHeaderRightSection: null,
};

export default ListSection;
