import { produce } from 'immer';
import { handleActions } from 'redux-actions';

import _set from 'lodash/set';
import _forEach from 'lodash/forEach';
import _cloneDeep from 'lodash/cloneDeep';

import { tget } from '@tekion/tekion-base/utils/general';
import { STATUS } from '@tekion/tekion-base/constants/status.constants';
import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';

import ACTION_TYPES from '../constants/notificationSettings.actionTypes';

const INITIAL_STATE = {
  saveStatus: '',
  loadingStatus: '',
  loadingFilterStatus: '',
  sectionIds: EMPTY_ARRAY,
  selectedSubSectionId: '',
  sectionById: EMPTY_OBJECT,
  autoSelectedSubSectionId: '',
  userPreferences: EMPTY_ARRAY,
  initialSectionIds: EMPTY_ARRAY,
  initialSectionById: EMPTY_OBJECT,
  expandedSectionById: EMPTY_OBJECT,
  notificationMetadatas: EMPTY_ARRAY,
  selectedFilters: EMPTY_ARRAY,
  searchText: '',
  isSearching: false,
  selectedRows: new Set(),
  showSaveSelectionModal: false,
  isDirty: false,
  showViewChangeConfirmationDialog: false,
  tempSelectedFilters: new Set(),
  bulkUpdatePayload: EMPTY_OBJECT,
  showGlobalResetConfirmationDialog: false,
  isGlobalResetAllOverride: false,
};

const notificationSettingsRequest = produce((state = INITIAL_STATE) => {
  _set(state, 'loadingStatus', STATUS.REQUESTED);
});

const notificationSettingsSuccess = produce((state = INITIAL_STATE, action) => {
  const { sectionIds, sectionById, expandedSectionById, autoSelectedSubSectionId } = tget(
    action,
    'payload',
    EMPTY_OBJECT
  );

  _set(state, 'sectionIds', sectionIds);
  _set(state, 'sectionById', sectionById);
  _set(state, 'loadingStatus', STATUS.SUCCESS);
  _set(state, 'expandedSectionById', expandedSectionById);
  _set(state, 'initialSectionIds', _cloneDeep(sectionIds));
  _set(state, 'initialSectionById', _cloneDeep(sectionById));
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
});

const notificationSettingsFailure = produce((state = INITIAL_STATE) => {
  _set(state, 'loadingStatus', STATUS.FAILED);
});

const changeNotificationSettings = produce((state = INITIAL_STATE, action) => {
  const { path, data } = tget(action, 'payload', EMPTY_OBJECT);
  const { isDirty } = state;
  _set(state, path, data);
  if (!isDirty) _set(state, 'isDirty', true);
});

const setExpandedSectionById = produce((state = INITIAL_STATE, action) => {
  const { expandedSectionById } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'expandedSectionById', expandedSectionById);
});

const setSelectedSubSectionId = produce((state = INITIAL_STATE, action) => {
  const { selectedSubSectionId, autoSelectedSubSectionId } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'selectedSubSectionId', selectedSubSectionId);
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
});

const setToggleCheckAll = produce((state = INITIAL_STATE, action) => {
  const { type, value, sectionId, subSectionId, subSectionItemIds } = tget(action, 'payload', EMPTY_OBJECT);
  const pathArray = ['sectionById', sectionId, 'subSectionById', subSectionId, 'subSectionItemById'];
  _forEach(subSectionItemIds, subSectionItemId => {
    _set(state, [...pathArray, subSectionItemId, type, 'isChecked'], value);
  });
});

const setFilterRequest = produce((state = INITIAL_STATE, action) => {
  const { selectedFilters, setLoading = true } = tget(action, 'payload', EMPTY_OBJECT);
  if (setLoading) _set(state, 'loadingFilterStatus', STATUS.REQUESTED);
  _set(state, 'selectedFilters', selectedFilters);
});

const setFilterSuccess = produce((state = INITIAL_STATE, action) => {
  const { sectionIds, changedSectionByIdPaths, expandedSectionById, autoSelectedSubSectionId } = tget(
    action,
    'payload',
    EMPTY_OBJECT
  );
  _set(state, 'sectionIds', sectionIds);
  _set(state, 'selectedSubSectionId', '');
  _set(state, 'loadingFilterStatus', STATUS.SUCCESS);
  _set(state, 'expandedSectionById', expandedSectionById);
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
  _forEach(changedSectionByIdPaths, ({ pathArray, value } = EMPTY_OBJECT) => {
    _set(state, ['sectionById', ...pathArray], value);
  });
});

const saveNotificationSettingsRequest = produce((state = INITIAL_STATE) => {
  _set(state, 'saveStatus', STATUS.REQUESTED);
});

const saveNotificationSettingsSuccess = produce((state = INITIAL_STATE) => {
  _set(state, 'saveStatus', STATUS.SUCCESS);
  _set(state, 'isDirty', false);
});

const saveNotificationSettingsFailure = produce((state = INITIAL_STATE) => {
  _set(state, 'saveStatus', STATUS.FAILED);
});

const resetNotificationSettings = produce((state = INITIAL_STATE, action) => {
  const { initialSectionIds, initialSectionById } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'sectionIds', initialSectionIds);
  _set(state, 'sectionById', initialSectionById);
  _set(state, 'selectedFilters', EMPTY_ARRAY);
  _set(state, 'searchText', '');
  _set(state, 'isDirty', false);
});

const setSearchRequest = produce((state = INITIAL_STATE, action) => {
  const { searchText } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'searchText', searchText);
  _set(state, 'isSearching', true);
});

const setSearchComplete = produce((state = INITIAL_STATE, action) => {
  const { sectionIds, changedSectionByIdPaths, expandedSectionById, autoSelectedSubSectionId } = tget(
    action,
    'payload',
    EMPTY_OBJECT
  );
  _set(state, 'sectionIds', sectionIds);
  _set(state, 'selectedSubSectionId', '');
  _set(state, 'loadingFilterStatus', STATUS.SUCCESS);
  _set(state, 'expandedSectionById', expandedSectionById);
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
  _forEach(changedSectionByIdPaths, ({ pathArray, value } = EMPTY_OBJECT) => {
    _set(state, ['sectionById', ...pathArray], value);
  });
  _set(state, 'isSearching', false);
});

const saveRowSelections = produce((state = INITIAL_STATE, action) => {
  const { selectedRows } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'selectedRows', selectedRows);
});

const setShowSaveSelectionModal = produce((state = INITIAL_STATE, action) => {
  const { showSaveSelectionModal, bulkUpdatePayload } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'showSaveSelectionModal', showSaveSelectionModal);
  _set(state, 'bulkUpdatePayload', bulkUpdatePayload);
});

const setShowViewChangeConfirmationDialog = produce((state = INITIAL_STATE, action) => {
  const { showViewChangeConfirmationDialog, tempSelectedFilters } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'showViewChangeConfirmationDialog', showViewChangeConfirmationDialog);
  _set(state, 'tempSelectedFilters', tempSelectedFilters);
});

const handleSetViewChangeSuccess = produce((state = INITIAL_STATE, action) => {
  const {
    sectionById,
    sectionIds,
    changedSectionByIdPaths,
    expandedSectionById,
    autoSelectedSubSectionId,
    initialSectionIds,
    initialSectionById,
  } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'sectionIds', sectionIds);
  _set(state, 'selectedSubSectionId', '');
  _set(state, 'loadingFilterStatus', STATUS.SUCCESS);
  _set(state, 'expandedSectionById', expandedSectionById);
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
  _set(state, 'sectionById', sectionById);
  _forEach(changedSectionByIdPaths, ({ pathArray, value } = EMPTY_OBJECT) => {
    _set(state, ['sectionById', ...pathArray], value);
  });
  _set(state, 'loadingStatus', STATUS.SUCCESS);
  _set(state, 'expandedSectionById', expandedSectionById);
  _set(state, 'initialSectionIds', initialSectionIds);
  _set(state, 'initialSectionById', initialSectionById);
  _set(state, 'autoSelectedSubSectionId', autoSelectedSubSectionId);
  _set(state, 'isDirty', false);
});

const handleSetSectionById = produce((state = INITIAL_STATE, action) => {
  const { sectionById, initialSectionById } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'sectionById', sectionById);
  _set(state, 'initialSectionById', initialSectionById);
});

export const handleSetShowBulkResetConfirmationDialog = produce((state = INITIAL_STATE, action) => {
  const { showGlobalResetConfirmationDialog, isAllOverride } = tget(action, 'payload', EMPTY_OBJECT);
  _set(state, 'showGlobalResetConfirmationDialog', showGlobalResetConfirmationDialog);
  _set(state, 'isGlobalResetAllOverride', isAllOverride);
});

const ACTION_HANDLERS = {
  [ACTION_TYPES.SET_FILTER_REQUEST]: setFilterRequest,
  [ACTION_TYPES.SET_FILTER_SUCCESS]: setFilterSuccess,
  [ACTION_TYPES.SET_SEARCH_REQUEST]: setSearchRequest,
  [ACTION_TYPES.SET_SEARCH_COMPLETE]: setSearchComplete,
  [ACTION_TYPES.SET_TOGGLE_CHECK_ALL]: setToggleCheckAll,
  [ACTION_TYPES.SET_EXPANDED_SECTION_BY_ID]: setExpandedSectionById,
  [ACTION_TYPES.SET_SELECTED_SUB_SECTION_ID]: setSelectedSubSectionId,
  [ACTION_TYPES.SET_NOTIFICATION_SETTINGS]: changeNotificationSettings,
  [ACTION_TYPES.RESET_NOTIFICATION_SETTINGS]: resetNotificationSettings,
  [ACTION_TYPES.NOTIFICATION_SETTINGS_REQUEST]: notificationSettingsRequest,
  [ACTION_TYPES.NOTIFICATION_SETTINGS_SUCCESS]: notificationSettingsSuccess,
  [ACTION_TYPES.NOTIFICATION_SETTINGS_FAILURE]: notificationSettingsFailure,
  [ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_REQUEST]: saveNotificationSettingsRequest,
  [ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_SUCCESS]: saveNotificationSettingsSuccess,
  [ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_FAILURE]: saveNotificationSettingsFailure,
  [ACTION_TYPES.ROW_SELECTION_SET]: saveRowSelections,
  [ACTION_TYPES.SHOW_SAVE_SELECTION_MODAL]: setShowSaveSelectionModal,
  [ACTION_TYPES.SET_SHOW_VIEW_CHANGE_CONFIRMATION_DIALOG]: setShowViewChangeConfirmationDialog,
  [ACTION_TYPES.VIEW_CHANGE_SUCCESS]: handleSetViewChangeSuccess,
  [ACTION_TYPES.SET_UPDATED_SECTION_BY_ID]: handleSetSectionById,
  [ACTION_TYPES.SET_SHOW_BULK_RESET_CONFIRMATION_DIALOG]: handleSetShowBulkResetConfirmationDialog,
};

export default handleActions(ACTION_HANDLERS, INITIAL_STATE);
