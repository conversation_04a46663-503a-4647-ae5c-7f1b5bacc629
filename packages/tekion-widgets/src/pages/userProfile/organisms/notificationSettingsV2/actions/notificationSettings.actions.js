import _debounce from 'lodash/debounce';
import _find from 'lodash/find';
import _head from 'lodash/head';
import _cloneDeep from 'lodash/cloneDeep';
import _isEmpty from 'lodash/isEmpty';

import { EMPTY_ARRAY, EMPTY_OBJECT } from '@tekion/tekion-base/app.constants';
import { getErrorMessage } from '@tekion/tekion-base/utils/errorUtils';
import getDataFromResponse from '@tekion/tekion-base/utils/getDataFromResponse';

import { getLoginData } from '@tekion/tekion-components/src/pages/authentication/reducers/authentication.selectors';
import { toaster, TOASTER_TYPE } from '@tekion/tekion-components/src/organisms/NotificationWrapper';
import { filterReader } from '@tekion/tekion-components/src/organisms/filterSection/readers/FilterGroup';

import OPERATORS from '@tekion/tekion-base/constants/filterOperators';

import {
  fetchNotificationEventsWithUserPreference,
  saveUserPreferences,
} from '../services/notificationSettings.service';

import {
  getSavePayload,
  getRestructuredData,
  getUpdatedDataOnApplyFilter,
  getIsGlobalView,
  getUpdatedSectionByIdFromResponse,
  getInitialWorkspaceView,
} from '../helpers/notificationSettings.helpers';

import ACTION_TYPES from '../constants/notificationSettings.actionTypes';
import {
  getIsDirty,
  getSearchText,
  getSelectedFilters,
  getSelectedRows,
  getTempSelectedFilters,
  selectInitialSectionIds,
  selectSectionById,
  selectInitialSectionById,
  getBulkUpdatePayload,
} from '../selectors/notificationSettings.selectors';
import { FILTER_IDS, GLOBAL_NOTIFICATION_PREFERENCE } from '../organisms/subHeader/constants/subHeader.filterTypes';
import { FILTER_DEBOUNCE_TIMEOUT } from '../organisms/subHeader/constants/subHeader.general';

export const fetchNotificationSettings = () => async (dispatch, getState) => {
  try {
    const state = getState();
    const loginData = getLoginData(state);
    const view = getInitialWorkspaceView(loginData);
    const initalSelectedFilters = [
      {
        type: FILTER_IDS.VIEW_BY,
        operator: OPERATORS.IN,
        values: [view],
      },
    ];
    dispatch([
      { type: ACTION_TYPES.NOTIFICATION_SETTINGS_REQUEST },
      {
        type: ACTION_TYPES.SET_FILTER_REQUEST,
        payload: { selectedFilters: initalSelectedFilters, setLoading: false },
      },
    ]);
    const eventsWithUserPreferenceResponse = await fetchNotificationEventsWithUserPreference(view);
    const eventsWithUserPreference = getDataFromResponse(eventsWithUserPreferenceResponse);
    const restructuredData = getRestructuredData(eventsWithUserPreference);
    dispatch({
      type: ACTION_TYPES.NOTIFICATION_SETTINGS_SUCCESS,
      payload: restructuredData,
    });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to fetch Notification settings')));
    dispatch({ type: ACTION_TYPES.NOTIFICATION_SETTINGS_FAILURE });
  }
};

export const saveNotificationSettings = sectionById => async (dispatch, getState) => {
  const state = getState();
  const selectedFilters = getSelectedFilters(state);
  const isGlobal = getIsGlobalView(selectedFilters);
  if (isGlobal) {
    setShowSaveSelectionModal(true)(dispatch, getState);
  } else {
    const initialSectionById = selectInitialSectionById(state);
    const viewByFilter = _find(selectedFilters, filter => filterReader.type(filter) === FILTER_IDS.VIEW_BY);
    const id = _head(filterReader.values(viewByFilter));
    dispatch({ type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_REQUEST });
    const payload = getSavePayload(sectionById, initialSectionById);
    try {
      const response = await saveUserPreferences(id, payload);
      dispatch({ type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_SUCCESS });
      toaster(TOASTER_TYPE.SUCCESS, __('Notification settings saved successfully'));
      applyChangesToLocal({ newEventTypesWithPreference: response, dispatch, getState, shouldUpdateInitial: true });
    } catch (error) {
      toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to save Notification settings')));
      dispatch({ type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_FAILURE });
    }
  }
};

export const resetNotificationSettings = (initialSectionIds, initialSectionById) => dispatch => {
  dispatch({ type: ACTION_TYPES.RESET_NOTIFICATION_SETTINGS, payload: { initialSectionIds, initialSectionById } });
};

export const setExpandedSectionById = expandedSectionById => dispatch => {
  dispatch({ type: ACTION_TYPES.SET_EXPANDED_SECTION_BY_ID, payload: { expandedSectionById } });
};

export const setSelectedSubSectionId = (selectedSubSectionId, autoSelectedSubSectionId) => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_SELECTED_SUB_SECTION_ID,
    payload: { selectedSubSectionId, autoSelectedSubSectionId },
  });
};

export const setNotificationSettings = (path, data) => dispatch => {
  dispatch({ type: ACTION_TYPES.SET_NOTIFICATION_SETTINGS, payload: { path, data } });
};

export const setToggleCheckAll = ({
  type,
  value,
  sectionId,
  subSectionId,
  subSectionItemIdsToBeUpdated: subSectionItemIds,
}) => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_TOGGLE_CHECK_ALL,
    payload: { type, value, sectionId, subSectionId, subSectionItemIds },
  });
};

const debouncedLocalSearch = _debounce((params, dispatch) => {
  const {
    sectionIds,
    expandedSectionById,
    changedSectionByIdPaths,
    autoSelectedSubSectionId,
  } = getUpdatedDataOnApplyFilter(params);
  dispatch({
    type: ACTION_TYPES.SET_SEARCH_COMPLETE,
    payload: { sectionIds, changedSectionByIdPaths, expandedSectionById, autoSelectedSubSectionId },
  });
}, FILTER_DEBOUNCE_TIMEOUT);

export const setSearch = ({ searchText }) => async (dispatch, getState) => {
  const state = getState();
  const selectedFilters = getSelectedFilters(state);
  const initialSectionById = selectSectionById(state);
  const initialSectionIds = selectInitialSectionIds(state);
  dispatch({ type: ACTION_TYPES.SET_SEARCH_REQUEST, payload: { searchText } });
  debouncedLocalSearch(
    { searchText, selectedFilters, sectionIds: initialSectionIds, sectionById: initialSectionById },
    dispatch
  );
};

export const handleFilter = ({ selectedFilters, changedFilter }) => async (dispatch, getState) => {
  const state = getState();
  const searchText = getSearchText(state);
  const initialSectionById = selectSectionById(state);
  const initialSectionIds = selectInitialSectionIds(state);

  if (filterReader.type(changedFilter) !== FILTER_IDS.VIEW_BY) {
    dispatch({ type: ACTION_TYPES.SET_FILTER_REQUEST, payload: { selectedFilters } });
    const {
      sectionIds,
      expandedSectionById,
      changedSectionByIdPaths,
      autoSelectedSubSectionId,
    } = getUpdatedDataOnApplyFilter({
      searchText,
      selectedFilters,
      sectionIds: initialSectionIds,
      sectionById: initialSectionById,
    });
    dispatch({
      type: ACTION_TYPES.SET_FILTER_SUCCESS,
      payload: { sectionIds, changedSectionByIdPaths, expandedSectionById, autoSelectedSubSectionId },
    });
  } else {
    const isDirty = getIsDirty(state);
    if (isDirty) {
      dispatch({
        type: ACTION_TYPES.SET_SHOW_VIEW_CHANGE_CONFIRMATION_DIALOG,
        payload: { showViewChangeConfirmationDialog: true, tempSelectedFilters: selectedFilters },
      });
    } else {
      handleViewChange({ selectedFilters, dispatch, getState });
    }
  }
};

export const handleRowSelect = params => (dispatch, getState) => {
  const { id, value } = params;
  const state = getState();
  const selectedRows = getSelectedRows(state) || EMPTY_ARRAY;
  const updatedSelectedRows = new Set(selectedRows);
  if (value) updatedSelectedRows.add(id);
  else updatedSelectedRows.delete(id);
  dispatch({ type: ACTION_TYPES.ROW_SELECTION_SET, payload: { selectedRows: updatedSelectedRows } });
};

export const setShowSaveSelectionModal = (showSaveSelectionModal, bulkUpdatePayload = EMPTY_OBJECT) => dispatch => {
  dispatch({ type: ACTION_TYPES.SHOW_SAVE_SELECTION_MODAL, payload: { showSaveSelectionModal, bulkUpdatePayload } });
};

export const handleGroupSelect = params => (dispatch, getState) => {
  const { subSectionItemIds, value } = params;
  const state = getState();
  const selectedRows = getSelectedRows(state) || EMPTY_ARRAY;
  const updatedSelectedRows = new Set(selectedRows);
  if (value) subSectionItemIds.forEach(subSectionItemId => updatedSelectedRows.add(subSectionItemId));
  else subSectionItemIds.forEach(subSectionItemId => updatedSelectedRows.delete(subSectionItemId));
  dispatch({ type: ACTION_TYPES.ROW_SELECTION_SET, payload: { selectedRows: updatedSelectedRows } });
};

export const saveGlobalNotificationPrefrences = allFlag => async (dispatch, getState) => {
  const state = getState();
  const sectionById = selectSectionById(state);
  const initialSectionById = selectInitialSectionById(state);
  const bulkUpdatePayload = getBulkUpdatePayload(state);
  dispatch({ type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_REQUEST });
  const payload = !_isEmpty(bulkUpdatePayload) ? bulkUpdatePayload : getSavePayload(sectionById, initialSectionById);
  try {
    const response = await saveUserPreferences(GLOBAL_NOTIFICATION_PREFERENCE.value, payload, allFlag);
    toaster(
      TOASTER_TYPE.SUCCESS,
      allFlag
        ? __('For all dealerships, including with overrides')
        : __('For all dealerships excluding ones with overrides'),
      undefined,
      __('Preferences changed successfully')
    );
    dispatch([
      { type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_SUCCESS },
      {
        type: ACTION_TYPES.SHOW_SAVE_SELECTION_MODAL,
        payload: { showSaveSelectionModal: false, bulkUpdatePayload: EMPTY_OBJECT },
      },
    ]);
    applyChangesToLocal({ newEventTypesWithPreference: response, dispatch, getState, shouldUpdateInitial: true });
  } catch (error) {
    toaster(TOASTER_TYPE.ERROR, getErrorMessage(error, __('Failed to save Notification settings')));
    dispatch({ type: ACTION_TYPES.SAVE_NOTIFICATION_SETTINGS_FAILURE });
  }
};

export const handleViewByChangeConfirm = () => async (dispatch, getState) => {
  const state = getState();
  const tempSelectedFilters = getTempSelectedFilters(state);
  handleViewChange({ selectedFilters: tempSelectedFilters, dispatch, getState, closeConfirmationDialog: true });
};

const handleViewChange = async ({ selectedFilters, dispatch, getState, closeConfirmationDialog = false }) => {
  const state = getState();
  const searchText = getSearchText(state);
  const reduxActions = [
    {
      type: ACTION_TYPES.SET_FILTER_REQUEST,
      payload: { selectedFilters, setLoading: true },
    },
  ];
  if (closeConfirmationDialog)
    reduxActions.push({
      type: ACTION_TYPES.SET_SHOW_VIEW_CHANGE_CONFIRMATION_DIALOG,
      payload: { showViewChangeConfirmationDialog: false, tempSelectedFilters: EMPTY_ARRAY },
    });

  dispatch(reduxActions);
  const viewByFilter = _find(selectedFilters, filter => filterReader.type(filter) === FILTER_IDS.VIEW_BY);
  const view = _head(filterReader.values(viewByFilter));
  const eventsWithUserPreferenceResponse = await fetchNotificationEventsWithUserPreference(view);
  const eventsWithUserPreference = getDataFromResponse(eventsWithUserPreferenceResponse);
  const restructuredData = getRestructuredData(eventsWithUserPreference);
  const {
    sectionIds,
    expandedSectionById,
    changedSectionByIdPaths,
    autoSelectedSubSectionId,
  } = getUpdatedDataOnApplyFilter({
    searchText,
    selectedFilters,
    sectionIds: restructuredData.sectionIds,
    sectionById: restructuredData.sectionById,
  });
  dispatch({
    type: ACTION_TYPES.VIEW_CHANGE_SUCCESS,
    payload: {
      sectionIds,
      changedSectionByIdPaths,
      expandedSectionById,
      autoSelectedSubSectionId,
      sectionById: restructuredData.sectionById,
      initialSectionById: _cloneDeep(restructuredData.sectionById),
      initialSectionIds: _cloneDeep(restructuredData.sectionIds),
    },
  });
};

export const handleCloseViewByChangeConfirmationDialog = () => dispatch => {
  dispatch({
    type: ACTION_TYPES.SET_SHOW_VIEW_CHANGE_CONFIRMATION_DIALOG,
    payload: { showViewChangeConfirmationDialog: false, tempSelectedFilters: EMPTY_ARRAY },
  });
};

export const applyChangesToLocal = ({ newEventTypesWithPreference, dispatch, getState, shouldUpdateInitial }) => {
  const state = getState();
  const sectionById = selectSectionById(state);
  const initialSectionById = selectSectionById(state);
  const updatedSectionById = getUpdatedSectionByIdFromResponse(newEventTypesWithPreference, sectionById);
  const updatedInitialSectionById = shouldUpdateInitial
    ? getUpdatedSectionByIdFromResponse(newEventTypesWithPreference, initialSectionById)
    : updatedSectionById;
  dispatch({
    type: ACTION_TYPES.SET_UPDATED_SECTION_BY_ID,
    payload: { sectionById: updatedSectionById, initialSectionById: updatedInitialSectionById },
  });
};
