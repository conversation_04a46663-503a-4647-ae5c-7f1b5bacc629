import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';

import {
  savePayload,
  searchFilterData,
  restructuredData,
  invalidFilterData,
  combinedFilterData,
  userPreferenceData,
  notificationOnFilterData,
  notificationTypeFilterData,
  notificationSettingsMetadata,
} from '@tekion/tekion-mock-data/src/mockData/widgets/notificationSettingsV2';

import {
  getSavePayload,
  getRestructuredData,
  shouldShowDescription,
  getUpdatedDataOnApplyFilter,
} from '../notificationSettings.helpers';

jest.mock('@tekion/tekion-base/utils/general', () => {
  function* sequenceUuidGenerator() {
    for (let index = 0; index < 50; index += 1) {
      yield `mock_uuid_${index}`;
    }
  }

  const original = jest.requireActual('@tekion/tekion-base/utils/general');
  const sequenceUuid = sequenceUuidGenerator();
  return {
    ...original,
    uuid: jest.fn(() => sequenceUuid.next().value),
  };
});
describe('Test cases for notificationSettings.helpers.js', () => {
  test('Get restructured api data from notification settings metadata and user preference', () => {
    const getDealerPropertyValue = jest.fn(feature => feature !== 'HIDDEN_FEATURE');
    const result = getRestructuredData(notificationSettingsMetadata, userPreferenceData, getDealerPropertyValue);
    expect(result).toStrictEqual({
      ...restructuredData,
      userPreferences: userPreferenceData,
      notificationMetadatas: notificationSettingsMetadata,
    });
  });

  test('Get save payload for notification settings', () => {
    const result = getSavePayload(restructuredData.sectionById, notificationSettingsMetadata);
    expect(result).toStrictEqual(savePayload);
  });

  test('Get updated data on apply search filter', () => {
    const result = getUpdatedDataOnApplyFilter({
      searchText: 'Duplicate',
      sectionIds: restructuredData.sectionIds,
      sectionById: restructuredData.sectionById,
      notificationOn: [],
      notificationType: [],
    });
    expect(result).toStrictEqual(searchFilterData);
  });

  test('Get updated data on apply notification on filter', () => {
    const result = getUpdatedDataOnApplyFilter({
      searchText: '',
      sectionIds: restructuredData.sectionIds,
      sectionById: restructuredData.sectionById,
      notificationOn: ['EMAIL'],
      notificationType: [],
    });
    expect(result).toStrictEqual(notificationOnFilterData);
  });

  test('Get updated data on apply notification type filter', () => {
    const result = getUpdatedDataOnApplyFilter({
      searchText: '',
      sectionIds: restructuredData.sectionIds,
      sectionById: restructuredData.sectionById,
      notificationOn: [],
      notificationType: ['ALERT_BANNER'],
    });
    expect(result).toStrictEqual(notificationTypeFilterData);
  });

  test('Get updated data on apply all filters', () => {
    const result = getUpdatedDataOnApplyFilter({
      searchText: 'SUB',
      sectionIds: restructuredData.sectionIds,
      sectionById: restructuredData.sectionById,
      notificationOn: ['TEXT', 'EMAIL', 'WEB', 'MOBILE'],
      notificationType: ['GENERAL', 'NUDGE_BANNER', 'ALERT_BANNER'],
    });
    expect(result).toStrictEqual(combinedFilterData);
  });

  test('Get updated data on apply invalid filters', () => {
    const result = getUpdatedDataOnApplyFilter({
      searchText: 'Invalid',
      sectionIds: restructuredData.sectionIds,
      sectionById: restructuredData.sectionById,
      notificationOn: ['TEXT', 'EMAIL', 'WEB', 'MOBILE'],
      notificationType: ['GENERAL', 'NUDGE_BANNER', 'ALERT_BANNER'],
    });
    expect(result).toStrictEqual(invalidFilterData);
  });

  test('Validate description visibility via dealer property', () => {
    const getDealerPropertyValue = jest.fn(() => true);
    const result = shouldShowDescription(getDealerPropertyValue);
    expect(getDealerPropertyValue).toBeCalledWith(DEALER_PROPERTIES.DSE_STANDALONE);
    expect(result).toBeTruthy();
  });
});
