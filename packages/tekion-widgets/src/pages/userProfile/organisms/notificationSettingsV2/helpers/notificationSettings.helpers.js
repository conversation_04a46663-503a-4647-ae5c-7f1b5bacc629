import { defaultMemoize } from 'reselect';

import _find from 'lodash/find';
import _map from 'lodash/map';
import _noop from 'lodash/noop';
import _some from 'lodash/some';
import _head from 'lodash/head';
import _isNil from 'lodash/isNil';
import _union from 'lodash/union';
import _split from 'lodash/split';
import _keyBy from 'lodash/keyBy';
import _values from 'lodash/values';
import _reduce from 'lodash/reduce';
import _filter from 'lodash/filter';
import _compact from 'lodash/compact';
import _groupBy from 'lodash/groupBy';
import _isEmpty from 'lodash/isEmpty';
import _toLower from 'lodash/toLower';
import _orderBy from 'lodash/orderBy';
import _includes from 'lodash/includes';
import _capitalize from 'lodash/capitalize';
import _isEqual from 'lodash/isEqual';
import _omit from 'lodash/omit';
import _cloneDeep from 'lodash/cloneDeep';
import _forEach from 'lodash/forEach';
import _set from 'lodash/set';

import { tget } from '@tekion/tekion-base/utils/general';
import { filterReader } from '@tekion/tekion-components/src/organisms/filterSection/readers/FilterGroup';
import { checkIfWorkspaceIdIsValid } from '@tekion/tekion-business/src/helpers/workspace/workspace.helper';

import TEnvReader from '@tekion/tekion-base/readers/Env';
import loginDataReader from '@tekion/tekion-business/src/readers/LoginData';

import { DEALER } from '@tekion/tekion-business/src/constants/workspaceUserAccess.constants';
import { SORT_SEQUENCE } from '@tekion/tekion-base/constants/sortOrder';
import { EMPTY_ARRAY, EMPTY_OBJECT, NA } from '@tekion/tekion-base/app.constants';
import DEALER_PROPERTIES from '@tekion/tekion-base/constants/dealerProperties';

import { LABEL_KEY, LABEL_OTHERS, NOTIFICATION_ON, NOTIFICATION_TYPE } from '../constants/notificationSettings.general';
import NotificationEventPreferenceReader from '../readers/eventTypes';
import { FILTER_IDS, GLOBAL_NOTIFICATION_PREFERENCE } from '../organisms/subHeader/constants/subHeader.filterTypes';

const orderByAsc = (values, targetField) =>
  _orderBy(values, [value => _toLower(tget(value, targetField, ''))], [SORT_SEQUENCE.ASC]);

const getKeysOrderByAsc = (objectByKey, targetField) => {
  const values = _values(objectByKey);
  const orderedValues = orderByAsc(values, targetField);
  return _map(orderedValues, item => tget(item, 'id', ''));
};

const getExpandedSectionAndAutoSelectedSubSectionData = (sectionIds, sectionById) => {
  const expandedSectionId = _head(sectionIds);
  const firstSubSectionId = tget(sectionById, [expandedSectionId, 'subSectionIds', 0], '');
  const autoSelectedSubSectionId = `${expandedSectionId}_${firstSubSectionId}`;
  return { autoSelectedSubSectionId, expandedSectionById: { [expandedSectionId]: true } };
};

const getFilteredSubSectionItemIds = ({
  searchText = '',
  notificationOn = EMPTY_ARRAY,
  notificationType = EMPTY_ARRAY,
  subSectionItemIds = EMPTY_ARRAY,
  subSectionItemById = EMPTY_OBJECT,
}) => {
  const searchWords = _compact(_split(_toLower(searchText), ' '));
  return _filter(subSectionItemIds, subSectionItemId => {
    const subSectionItem = tget(subSectionItemById, subSectionItemId, EMPTY_OBJECT);

    const notificationLabel = _toLower(tget(subSectionItem, ['notification', 'label'], ''));
    const isWebDisabled = tget(subSectionItem, [NOTIFICATION_ON.WEB, 'disabled'], EMPTY_ARRAY);
    const selectedNotificationTypes = tget(subSectionItem, [NOTIFICATION_ON.WEB, 'values'], EMPTY_ARRAY);
    const hasValidNotificationType =
      _isEmpty(notificationType) ||
      (!isWebDisabled && _some(selectedNotificationTypes, type => _includes(notificationType, type)));
    const hasValidNotificationOn =
      _isEmpty(notificationOn) ||
      _some(notificationOn, targetOn => tget(subSectionItem, [targetOn, 'isChecked'], false));
    const hasValidSearchText =
      _isEmpty(searchText) || _some(searchWords, searchWord => _includes(notificationLabel, searchWord));
    return hasValidNotificationType && hasValidNotificationOn && hasValidSearchText;
  });
};

export const getUpdatedDataOnApplyFilter = defaultMemoize(
  ({ searchText = '', sectionIds = EMPTY_ARRAY, sectionById = EMPTY_OBJECT, selectedFilters = EMPTY_ARRAY }) => {
    let changedSectionByIdPaths = EMPTY_ARRAY;
    let subSectionIdsBySectionId = EMPTY_OBJECT;
    const newSectionIds = _filter(sectionIds, sectionId => {
      const section = tget(sectionById, sectionId, EMPTY_OBJECT);
      const { initialSubSectionIds, subSectionById } = section;
      const selectedFiltersByType = _keyBy(selectedFilters, 'type');
      const notificationOn = selectedFiltersByType[FILTER_IDS.NOTITICATION_ON]?.values || EMPTY_ARRAY;
      const notificationType = selectedFiltersByType[FILTER_IDS.NOTIFICATION_TYPE]?.values || EMPTY_ARRAY;

      const newSubSectionIds = _filter(initialSubSectionIds, subSectionId => {
        const subSection = tget(subSectionById, subSectionId, EMPTY_OBJECT);
        const { initialSubSectionItemIds, subSectionItemById } = subSection;

        const newSubSectionItemIds = getFilteredSubSectionItemIds({
          searchText,
          notificationOn,
          notificationType,
          subSectionItemById,
          subSectionItemIds: initialSubSectionItemIds,
        });
        changedSectionByIdPaths = [
          ...changedSectionByIdPaths,
          {
            pathArray: [sectionId, 'subSectionById', subSectionId, 'subSectionItemIds'],
            value: newSubSectionItemIds,
          },
        ];
        return !_isEmpty(newSubSectionItemIds);
      });

      changedSectionByIdPaths = [
        ...changedSectionByIdPaths,
        {
          pathArray: [sectionId, 'subSectionIds'],
          value: newSubSectionIds,
        },
      ];
      subSectionIdsBySectionId = { ...subSectionIdsBySectionId, [sectionId]: newSubSectionIds };
      return !_isEmpty(newSubSectionIds);
    });
    const expandedSectionId = _head(newSectionIds);
    const selectedSubSectionId = _head(tget(subSectionIdsBySectionId, expandedSectionId, EMPTY_ARRAY));
    const autoSelectedSubSectionId = `${expandedSectionId}_${selectedSubSectionId}`;

    return {
      changedSectionByIdPaths,
      autoSelectedSubSectionId,
      sectionIds: newSectionIds,
      expandedSectionById: { [expandedSectionId]: true },
    };
  }
);

const getDisabledDeliveryTypes = (deliveryTypes = EMPTY_ARRAY) => {
  const deliveryTypeObj = _reduce(deliveryTypes, (accum, type) => ({ ...accum, [type]: true }), EMPTY_OBJECT);
  const shouldDisableWeb = !tget(deliveryTypeObj, NOTIFICATION_ON.WEB, false);
  const shouldDisableText = !tget(deliveryTypeObj, NOTIFICATION_ON.TEXT, false);
  const shouldDisableEmail = !tget(deliveryTypeObj, NOTIFICATION_ON.EMAIL, false);
  const shouldDisableMobile = !tget(deliveryTypeObj, NOTIFICATION_ON.MOBILE, false);
  return { shouldDisableWeb, shouldDisableText, shouldDisableEmail, shouldDisableMobile };
};

const getIsCheckedFromPreference = (preference = EMPTY_OBJECT) => {
  const isWebChecked = tget(preference, NOTIFICATION_ON.WEB, false);
  const isTextChecked = tget(preference, NOTIFICATION_ON.TEXT, false);
  const isEmailChecked = tget(preference, NOTIFICATION_ON.EMAIL, false);
  const isMobileChecked = tget(preference, NOTIFICATION_ON.MOBILE, false);
  return { isWebChecked, isTextChecked, isEmailChecked, isMobileChecked };
};

const getEventSubGroups = subSectionItems => {
  const subEventGroups = _groupBy(
    subSectionItems,
    event => NotificationEventPreferenceReader.eventSubGroup(event) || NA
  );
  const returnList = _map(subEventGroups, (subEvents, groupName) => {
    if (groupName === NA) {
      return null;
    }
    const restructuredSubEvents = _map(subEvents, subEvent => ({
      id: NotificationEventPreferenceReader.eventType(subEvent),
      [LABEL_KEY]: NotificationEventPreferenceReader.title(subEvent),
    }));
    const orderedSubEvents = orderByAsc(restructuredSubEvents, LABEL_KEY);
    return {
      ..._head(subEvents),
      subEvents: orderedSubEvents,
      metadata: {
        ...NotificationEventPreferenceReader.metadata(_head(subEvents)),
        title: groupName,
        description: '',
        key: groupName,
      },
    };
  });
  return _compact(returnList);
};

const getAllPreferenceListValue = notification => {
  const deliveryType = NotificationEventPreferenceReader.deliveryType(notification);
  const override = NotificationEventPreferenceReader.override(notification);
  const preference = NotificationEventPreferenceReader.preference(notification);
  const soundPreference = NotificationEventPreferenceReader.soundPreference(notification);
  const notificationStylePreference = NotificationEventPreferenceReader.notificationStylePreference(notification);

  const { shouldDisableWeb, shouldDisableText, shouldDisableEmail, shouldDisableMobile } = getDisabledDeliveryTypes(
    deliveryType
  );

  const isMuted = !tget(soundPreference, NOTIFICATION_ON.WEB, false);
  const webStylePreferences = tget(notificationStylePreference, NOTIFICATION_ON.WEB, EMPTY_ARRAY);
  const values = _union(webStylePreferences, [NOTIFICATION_TYPE.GENERAL]);
  const { isWebChecked, isTextChecked, isEmailChecked, isMobileChecked } = getIsCheckedFromPreference(preference);
  return {
    [NOTIFICATION_ON.TEXT]: { isChecked: !shouldDisableText && isTextChecked, disabled: shouldDisableText },
    [NOTIFICATION_ON.EMAIL]: { isChecked: !shouldDisableEmail && isEmailChecked, disabled: shouldDisableEmail },
    [NOTIFICATION_ON.MOBILE]: {
      isChecked: !shouldDisableMobile && isMobileChecked,
      disabled: shouldDisableMobile,
    },
    [NOTIFICATION_ON.WEB]: {
      values,
      isMuted,
      isChecked: !shouldDisableWeb && isWebChecked,
      disabled: shouldDisableWeb,
    },
    override,
  };
};

const getSubSectionItemById = subSectionItems => {
  const eventSubGroups = getEventSubGroups(subSectionItems);
  const filteredSubSectionItems = _filter(subSectionItems, subSectionItem =>
    _isNil(NotificationEventPreferenceReader.eventSubGroup(subSectionItem))
  );
  return _reduce(
    [...filteredSubSectionItems, ...eventSubGroups],
    (acc, subSectionItem) => {
      const eventMetadata = NotificationEventPreferenceReader.metadata(subSectionItem);
      const { title, description, key: id } = eventMetadata || EMPTY_OBJECT;
      const subEvents = tget(subSectionItem, 'subEvents', EMPTY_ARRAY);
      const label = _capitalize(title);
      const hasSubEvents = !_isEmpty(subEvents);
      const eventId = id;

      return {
        ...acc,
        [eventId]: {
          id: eventId,
          hasSubEvents,
          [LABEL_KEY]: label,
          notification: { label, infoText: description, subEvents },
          ...getAllPreferenceListValue(subSectionItem),
        },
      };
    },
    EMPTY_OBJECT
  );
};

const getSubSectionById = subSectionGroup =>
  _reduce(
    subSectionGroup,
    (acc, subSectionItems, subSectionId) => {
      const subSectionItemById = getSubSectionItemById(subSectionItems);
      const subSectionItemIds = getKeysOrderByAsc(subSectionItemById, LABEL_KEY);
      return {
        ...acc,
        [subSectionId]: {
          id: subSectionId,
          subSectionItemIds,
          subSectionItemById,
          [LABEL_KEY]: _capitalize(subSectionId),
          initialSubSectionItemIds: subSectionItemIds,
        },
      };
    },
    EMPTY_OBJECT
  );

const getSectionById = sectionGroup =>
  _reduce(
    sectionGroup,
    (acc, subSections, sectionId) => {
      const subSectionGroup = _groupBy(
        subSections,
        event => NotificationEventPreferenceReader.group(event) || LABEL_OTHERS
      );
      const subSectionById = getSubSectionById(subSectionGroup);
      const subSectionIds = getKeysOrderByAsc(subSectionById, LABEL_KEY);
      return {
        ...acc,
        [sectionId]: {
          id: sectionId,
          subSectionIds,
          subSectionById,
          [LABEL_KEY]: _capitalize(sectionId),
          initialSubSectionIds: subSectionIds,
        },
      };
    },
    EMPTY_OBJECT
  );

export const getRestructuredData = eventsWithUserPreference => {
  const sectionGroup = _groupBy(
    eventsWithUserPreference,
    event => NotificationEventPreferenceReader.moduleName(event) || LABEL_OTHERS
  );

  const sectionById = getSectionById(sectionGroup);
  const sectionIds = getKeysOrderByAsc(sectionById, LABEL_KEY);

  const { autoSelectedSubSectionId, expandedSectionById } = getExpandedSectionAndAutoSelectedSubSectionData(
    sectionIds,
    sectionById
  );

  return {
    sectionIds,
    sectionById,
    expandedSectionById,
    autoSelectedSubSectionId,
  };
};

export const getSelectedPreferences = event => {
  const {
    [NOTIFICATION_ON.WEB]: web,
    [NOTIFICATION_ON.TEXT]: text,
    [NOTIFICATION_ON.EMAIL]: email,
    [NOTIFICATION_ON.MOBILE]: mobile,
  } = event;

  const preference = {
    [NOTIFICATION_ON.WEB]: tget(web, 'isChecked', false),
    [NOTIFICATION_ON.TEXT]: tget(text, 'isChecked', false),
    [NOTIFICATION_ON.EMAIL]: tget(email, 'isChecked', false),
    [NOTIFICATION_ON.MOBILE]: tget(mobile, 'isChecked', false),
  };

  const soundPreference = {
    [NOTIFICATION_ON.WEB]: !tget(web, 'isMuted', true),
  };

  const notificationStylePreference = {
    [NOTIFICATION_ON.WEB]: tget(web, 'values', [NOTIFICATION_TYPE.GENERAL]),
  };
  return { preference, soundPreference, notificationStylePreference };
};

export const getSubEventsPayload = (payload, currentNotificationPayload, subEvents) => {
  const subEventPayload = _reduce(
    subEvents,
    (acc, subEvent) => ({ ...acc, [subEvent.id]: currentNotificationPayload }),
    EMPTY_OBJECT
  );
  return { ...payload, ...subEventPayload };
};

const getSubSectionItemSavePayload = (subSectionItemById, initialSubSectionItemById = EMPTY_OBJECT) =>
  _reduce(
    subSectionItemById,
    (subSectionItemAcc, subSectionItem, subSectionItemId) => {
      const initialSubSectionItem = initialSubSectionItemById[subSectionItemId];

      if (
        _isEqual(
          _omit(initialSubSectionItem, ['id', 'hasSubEvents', 'notification']),
          _omit(subSectionItem, ['id', 'hasSubEvents', 'notification'])
        )
      )
        return subSectionItemAcc;
      const { id, hasSubEvents, notification } = subSectionItem;

      const preferencePayload = getSelectedPreferences(subSectionItem);

      if (hasSubEvents) {
        const subEvents = tget(notification, 'subEvents', EMPTY_ARRAY);
        return getSubEventsPayload(subSectionItemAcc, preferencePayload, subEvents);
      }

      return { ...subSectionItemAcc, [id]: preferencePayload };
    },
    EMPTY_OBJECT
  );

const getSubSectionSavePayload = (subSectionById, initialSubSectionById = EMPTY_OBJECT) =>
  _reduce(
    subSectionById,
    (subSectionAcc, subSection, subSectionId) => {
      const subSectionItemResult = getSubSectionItemSavePayload(
        subSection.subSectionItemById,
        initialSubSectionById[subSectionId]?.subSectionItemById
      );
      return { ...subSectionAcc, ...subSectionItemResult };
    },
    EMPTY_OBJECT
  );

const getSectionSavePayload = (sectionById, initialSectionById = EMPTY_OBJECT) =>
  _reduce(
    sectionById,
    (sectionAcc, section, sectionId) => {
      const subSectionResult = getSubSectionSavePayload(
        section.subSectionById,
        initialSectionById[sectionId]?.subSectionById
      );
      return { ...sectionAcc, ...subSectionResult };
    },
    EMPTY_OBJECT
  );

export const getSavePayload = (sectionById, initialSectionById) => {
  const payload = getSectionSavePayload(sectionById, initialSectionById);
  return { userPreferences: payload };
};

export const shouldShowDescription = defaultMemoize((getDealerPropertyValue = _noop) =>
  getDealerPropertyValue(DEALER_PROPERTIES.DSE_STANDALONE)
);

export const getIsGlobalView = selectedFilters => {
  const viewByFilter = _find(selectedFilters, filter => filterReader.type(filter) === FILTER_IDS.VIEW_BY);
  if (_isEmpty(viewByFilter)) return false;
  const value = filterReader.values(viewByFilter);
  return _head(value) === GLOBAL_NOTIFICATION_PREFERENCE.value;
};

export const getEventTypesByKey = sectionById =>
  _reduce(
    sectionById,
    (acc, section) => {
      const sectionEvents = _reduce(
        section.subSectionById,
        (subAcc, subSection) => ({
          ...subAcc,
          ...subSection.subSectionItemById,
        }),
        EMPTY_OBJECT
      );
      return {
        ...acc,
        ...sectionEvents,
      };
    },
    EMPTY_OBJECT
  );

export const getUpdatedSectionByIdFromResponse = (newEventTypesWithPreference, sectionById) => {
  const updatedSectionById = _cloneDeep(sectionById);
  _forEach(newEventTypesWithPreference, event => {
    const sectionId = NotificationEventPreferenceReader.moduleName(event) || LABEL_OTHERS;
    const subSectionId = NotificationEventPreferenceReader.group(event) || LABEL_OTHERS;
    const eventType = NotificationEventPreferenceReader.eventType(event);
    const currentValue = tget(
      updatedSectionById,
      [sectionId, 'subSectionById', subSectionId, 'subSectionItemById', eventType],
      EMPTY_OBJECT
    );
    if (_isEmpty(currentValue)) return;
    const updatedValue = {
      ...currentValue,
      ...getAllPreferenceListValue(event),
    };
    _set(
      updatedSectionById,
      [sectionId, 'subSectionById', subSectionId, 'subSectionItemById', eventType],
      updatedValue
    );
  });
  return updatedSectionById;
};

export const createBulkUpdatePayload = ({ bulkUpdateChanges, selectedRows, eventTypesByKey, notificationOn }) => {
  const {
    preference: preferenceChanges,
    soundPreference: soundPreferenceChanges,
    notificationStylePreference: notificationStylePreferenceChanges,
  } = bulkUpdateChanges;
  const userPreferences = _reduce(
    selectedRows,
    (payloadAcc, eventType) => {
      const event = eventTypesByKey[eventType];
      if (_isNil(event)) return payloadAcc;
      const { id, hasSubEvents, notification, [notificationOn]: notifciationOnData } = event;
      const { disabled } = notifciationOnData || EMPTY_OBJECT;
      if (disabled) return payloadAcc;
      const { preference, soundPreference, notificationStylePreference } = getSelectedPreferences(event);

      const preferencePayload = {
        preference: { ...preference, ...preferenceChanges },
        soundPreference: { ...soundPreference, ...soundPreferenceChanges },
        notificationStylePreference: { ...notificationStylePreference, ...notificationStylePreferenceChanges },
      };
      if (hasSubEvents) {
        const subEvents = tget(notification, 'subEvents', EMPTY_ARRAY);
        return getSubEventsPayload(payloadAcc, preferencePayload, subEvents);
      }
      return { ...payloadAcc, [id]: preferencePayload };
    },
    EMPTY_OBJECT
  );
  return {
    userPreferences,
  };
};

export const getEventsTypeKeys = (selectedRows, eventTypesByKey) =>
  _reduce(
    selectedRows,
    (acc, eventKey) => {
      const event = eventTypesByKey[eventKey] || EMPTY_OBJECT;
      if (_isEmpty(event)) return acc;
      if (!event.hasSubEvents) return [...acc, eventKey];
      const subEventKeys = _map(event.notification.subEvents, 'id');
      return [...acc, ...subEventKeys];
    },
    EMPTY_ARRAY
  );

export const getInitialWorkspaceView = loginData => {
  const workspaces = loginDataReader.workspaces(loginData);
  const workspacesById = _keyBy(workspaces, 'workspaceId');
  const { workspaceId, dealerId } = TEnvReader.userInfo();
  if (!checkIfWorkspaceIdIsValid(workspaceId)) return dealerId;
  const currentWorkspace = workspacesById[workspaceId];
  const currentWorkspaceType = currentWorkspace?.workspaceType;
  const view =
    currentWorkspaceType === DEALER || !currentWorkspaceType ? workspaceId : GLOBAL_NOTIFICATION_PREFERENCE.value;
  return view;
};
